# config/routes.rb
Rails.application.routes.draw do
  authenticate :user, ->(user) { user.has_role?(:admin) } do
    mount PgHero::Engine, at: "pghero"
  end

  get "people", to: "people#index"
  get "places", to: "places#index"
  get "projects", to: "projects#index"
  post "people", to: "people#search"
  post "projects", to: "projects#search"
  post "places", to: "places#search"
  get "projects/recomendations", to: "projects#recomendation"
  get "people_recomendataion", to: "people#people_recomendataion"
  get "locations_recomentation", to: "places#locations_recomentation"
  post "projects/new", to: "projects#new_project"
  get "projects/userprojects", to: "projects#user_projects"
  get "user_places", to: "places#user_places"
  post "places/new", to: "places#create"
  post "ubuntu", to: "forums#search"
  get "ubuntu/recommendations", to: "forums#recommendations"
  get "user_ubuntu_channels", to: "forums#user_channels"

  post   '/bookmarks', to: 'bookmarks#create'
  delete '/bookmarks', to: 'bookmarks#destroy'

  get 'projects/bookmarked', to: 'projects#bookmarked'
  get '/people/bookmarked', to: 'people#bookmarked'
  get '/places/bookmarked', to: 'places#bookmarked'
  get '/forums/bookmarked', to: 'forums#bookmarked'

resources :notifications do
  member do
    patch :mark_as_read
  end
end

  devise_for :users, controllers: {
    sessions: 'users/sessions',
    registrations: 'users/registrations',
    omniauth_callbacks: 'users/omniauth_callbacks'
  }

  get "profile", to: "dashboard#index"
  post "profile/update", to: "profile#update_avatar"
  get "signup", to: "signup#index"
  get "admin_dashboard", to: "admin#index"
  get "users", to: "admin#users"
  post "admin/remove_role", to: "admin#remove_role"
  post "admin/add_role", to: "admin#add_role"
  post "join_project", to: "join_project_request#create"

  # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html

  # Reveal health status on /up that returns 200 if the app boots with no exceptions, otherwise 500.
  # Can be used by load balancers and uptime monitors to verify that the app is live.
  get "up" => "rails/health#show", as: :rails_health_check

  root "home#index"
  if Rails.env.development?
    mount LetterOpenerWeb::Engine, at: "/letter_opener"
  end

  get 'profile/:id', to: 'profile#show', as: :show_profile
  get 'place/:id', to: 'places#placesingleshow'
  get 'project/:id', to: 'projects#projectsingleshow'
  post "forums/join/:id", to: "forums#join"
  delete 'forums/leave/:id', to: 'forums#leave_forum', as: 'leave_forum'
  delete 'forums/delete/:id', to: 'forums#delete', as: 'delete_forum'
  post 'posts/addComment', to: 'posts#add_comment'
  get 'likes', to: 'likes#post_likes'
  resources :forums, only: [ :show, :index, :create ]
  resources :posts, only: [ :show, :index, :create ]
  resources :likes, only: [ :create, :destroy, :show ]
  resources :replies, only: [ :create, :destroy, :update, :index ]
  # match "*path", to: "application#not_found", via: :all
end
