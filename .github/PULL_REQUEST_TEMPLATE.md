# Description

Please include a summary of the changes and the related issue.
Include relevant motivation and context.
List any new dependencies that are required for this change.

Fixes # (issue)

## Type of change

Please delete options that are not relevant.

- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] This change requires a documentation update

## How Has This Been Tested?

Please describe the tests that you ran to verify your changes.
Provide instructions so we can reproduce.

- [ ] Test A
- [ ] Test B

## Checklist:

- [ ] My code follows the style guidelines of this project
- [ ] I have performed a self-review of my code
- [ ] I have commented my code, particularly in hard-to-understand areas
- [ ] I have made corresponding changes to the documentation
- [ ] My changes generate no new warnings
- [ ] I have added tests that prove my fix is effective or that my feature works
- [ ] New and existing unit tests pass locally with my changes
- [ ] I am making the PR to develop branch

## Rails Specific:

- [ ] Database migrations are reversible
- [ ] ActiveRecord queries are optimized and N+1 issues addressed
- [ ] Routes are properly defined and named
- [ ] Models include appropriate validations and callbacks
- [ ] Controllers follow RESTful conventions
- [ ] Views do not contain business logic
- [ ] Added or updated translations if necessary
- [ ] Considered query caching where appropriate
- [ ] Background jobs configured correctly (if applicable)

## Screenshots (if appropriate):

## Security Considerations:

- [ ] Proper parameter sanitization
- [ ] Authorization checks in place
- [ ] No sensitive data exposed in logs/responses
- [ ] CSRF protection maintained
