{"private": true, "type": "module", "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@vitejs/plugin-react": "^4.4.1", "jsdom": "^26.1.0", "vite": "^5.4.17", "vite-plugin-ruby": "^5.1.1", "vitest": "^3.1.3"}, "dependencies": {"@emoji-mart/data": "^1.2.1", "@emoji-mart/react": "^1.1.1", "@inertiajs/inertia": "^0.11.1", "@inertiajs/inertia-react": "^0.8.1", "@inertiajs/react": "^2.0.6", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.3", "@vitejs/plugin-react": "^4.3.4", "date-fns": "^4.1.0", "emoji-mart": "^5.6.0", "lucide-react": "^0.503.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.5.2", "tailwindcss": "^4.1.3"}, "scripts": {"test": "vitest"}}