This guide will help you run the project locally **without installing dependencies manually**, by using **Dev Containers in VS Code** and **Docker**.

---

## 🛠️ Tech Stack

- [**Ruby on Rails**](https://rubyonrails.org/)
- [**React (via Inertia.js)**](https://inertiajs.com/)
- [**PostgreSQL**](https://www.postgresql.org/)
- [**Vite**](https://vitejs.dev/) for frontend bundling
- [**Docker**](https://www.docker.com/) for containerization
- [**Dev Containers**](https://code.visualstudio.com/docs/devcontainers/containers) for easy VS Code setup
---

## 💎 Ruby Version

- [**Ruby 3.4.3**](https://www.ruby-lang.org/en/news/2024/03/30/ruby-3-4-3-released/) (specified in `.ruby-version`)
- [**Rails 8.0.2**](https://rubyonrails.org/)  
---

## 🧰 Requirements

- **Docker** installed and running  
  [Get Docker →](https://www.docker.com/products/docker-desktop)

- **Visual Studio Code**  
  [Get VS Code →](https://code.visualstudio.com/)

- **Dev Containers Extension**  
  Install from the VS Code Marketplace: `Dev Containers` by Microsoft

---

## 🐳 Run the Project with Dev Container (Recommended)

### Prerequisites

- [Docker](https://www.docker.com/products/docker-desktop/) installed and running  
- [Visual Studio Code](https://code.visualstudio.com/)  
- [Dev Containers extension](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.remote-containers) installed in VS Code

---

### 🚀 Steps

1. **Clone the repository**

   ```bash
   git clone https://github.com/Flying-Tea-Squad/bongo_hub
   cd bongo_hub

2. **Open in VS Code** (in the terminal type):
    ```
    code .
    ```

3. **Reopen in Dev Container**

    When prompted, click "Reopen in Container"

    If not prompted:
        Press F1 (or Fn + F1)
        Search: Dev Containers: Reopen in Container
        Select it
    
    Wait for the container to build
    The first time might take a few minutes—it installs Ruby, Node, Yarn, etc.

4. **Set up**

    Open a terminal inside the container (`Ctrl + `` or View → Terminal):
    ```
    bundle install && npm i && rails db:migrate
    ```
5. **Start the server**
    
    Inside the container terminal:
    ```
    rails s
    ```
    This runs both the Rails API and Vite-powered React frontend.
6. **Visit the App**
    
    Open your browser: http://localhost:3000

## 🧪 How to Run the Test Suite

Inside your **Dev Container** or **Docker** environment, run:

```
bundle exec rspec
```

Or, for Rails system tests:

    rails test
