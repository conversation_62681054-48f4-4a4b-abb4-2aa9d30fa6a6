#!/bin/bash

set -e # Exit immediately if a command fails

echo ">>>>>>> 🛠️ Updating apt..."
sudo apt update

echo ">>>>>>> 📦 Ensuring required system packages are installed..."
REQUIRED_PACKAGES=(
  build-essential
  libssl-dev
  libreadline-dev
  zlib1g-dev
  libsqlite3-dev
  libffi-dev
  libyaml-dev
  libgdbm-dev
  libgmp-dev
  libncurses5-dev
  libncursesw5-dev
  autoconf
  bison
  curl
  git
  libdb-dev
  libbz2-dev
  postgresql
  libpq-dev
)

for pkg in "${REQUIRED_PACKAGES[@]}"; do
  if ! dpkg -s "$pkg" &> /dev/null; then
    echo "Installing $pkg..."
    sudo apt install -y "$pkg"
  fi
done

echo ">>>>>>> 💎 Installing Ruby $RUBY_VERSION if not already..."
RUBY_VERSION="3.4.3"

if ruby -v | grep -q "$RUBY_VERSION"; then
  echo "Ruby $RUBY_VERSION is already installed."
else
  echo "Installing Ruby $RUBY_VERSION from source..."
  cd /tmp
  curl -O https://cache.ruby-lang.org/pub/ruby/3.4/ruby-$RUBY_VERSION.tar.gz
  tar -xzvf ruby-$RUBY_VERSION.tar.gz
  cd ruby-$RUBY_VERSION
  ./configure
  make
  sudo make install

  if ! command -v ruby &>/dev/null; then
    echo "❌ Ruby installation failed. Exiting..."
    exit 1
  fi

  cd ~
fi

echo ">>>>>>> 💎 Setting Ruby $RUBY_VERSION as default..."
RUBY_BIN="$(which ruby)"
GEM_BIN="$(which gem)"

sudo update-alternatives --install /usr/bin/ruby ruby "$RUBY_BIN" 1
sudo update-alternatives --install /usr/bin/gem gem "$GEM_BIN" 1
sudo update-alternatives --set ruby "$RUBY_BIN"
sudo update-alternatives --set gem "$GEM_BIN"

echo "Using Ruby: $(ruby -v)"


echo ">>>>>>> 🔢 Installing Node.js 20.x if version is less than 20..."
install_nodejs_20() {
  curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
  sudo apt install -y nodejs
}

if command -v node &>/dev/null; then
  NODE_VERSION=$(node -v | cut -d'v' -f2)
  NODE_MAJOR=$(echo "$NODE_VERSION" | cut -d. -f1)
  if [ "$NODE_MAJOR" -lt 20 ]; then
    echo "Node.js is outdated. Reinstalling Node.js 20.x..."
    install_nodejs_20
  else
    echo "Node.js v$NODE_VERSION is sufficient."
  fi
else
  echo "Node.js not found. Installing Node.js 20.x..."
  install_nodejs_20
fi

# Ensure npm is installed and accessible
if ! command -v npm &>/dev/null; then
  echo "❗ npm not found. Trying to fix by reinstalling Node.js..."
  install_nodejs_20
fi

# Symlink npm if it's not in sudo's PATH
if ! command -v npm &>/dev/null && [ -f /usr/local/bin/npm ]; then
  sudo ln -s /usr/local/bin/npm /usr/bin/npm
fi

if command -v npm &>/dev/null; then
  echo ">>>>>>> ✅ npm is installed."
else
  echo "❌ npm installation failed. Exiting..."
  exit 1
fi

echo ">>>>>>> 🧶 Installing Yarn..."
if ! command -v yarn &>/dev/null; then
  echo "Installing Yarn with npm..."
  sudo npm install -g yarn
else
  echo "Yarn is already installed."
fi

echo ">>>>>>> 💎 Installing Rails..."
if ! gem list -i rails &>/dev/null; then
  gem install rails
else
  echo "Rails is already installed: $(rails -v)"
fi

echo ">>>>>>> 🔐 Setting PostgreSQL password for 'postgres' user..."
sudo -u postgres psql -c "ALTER USER postgres PASSWORD 'postgres';" || echo "Could not set password. Continuing..."

echo ">>>>>>> 📄 Ensuring .env file exists with PostgreSQL credentials..."
if [ ! -f .env ]; then
  cat <<EOF > .env
PG_USERNAME=postgres
PG_PASSWORD=postgres
EOF
  echo "Created .env file."
else
  echo ".env file already exists."
fi

# Export env vars into current shell
export $(grep -v '^#' .env | xargs)

echo ">>>>>>> 📦 Installing Ruby gems..."
bundle install

echo ">>>>>>> 📦 Installing JavaScript dependencies..."
if [ -f "yarn.lock" ]; then
  yarn install
elif [ -f "package-lock.json" ]; then
  npm install
else
  echo ">>>>>>> ⚠️ No JavaScript package manager lock file found."
fi

echo ">>>>>>> 🗃️ Setting up database..."
bin/rails db:setup || {
  echo "❌ Failed to set up the database. Please check PostgreSQL setup."
  exit 1
}

echo ">>>>>>> ✅ All set! Starting your Rails app."
rails s
