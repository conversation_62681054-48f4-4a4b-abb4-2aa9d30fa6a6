# This file should ensure the existence of records required to run the application in every environment (production,
# development, test). The code here should be idempotent so that it can be executed at any point in every environment.
# The data can then be loaded with the bin/rails db:seed command (or created alongside the database with db:setup).
#
# Example:
#
#   ["Action", "Comedy", "Drama", "Horror"].each do |genre_name|
#     MovieGenre.find_or_create_by!(name: genre_name)
#   end

require "faker"

puts "✅ Seeding users."
  # Generate fake users
  10.times do
    first_name = Faker::Name.first_name
    last_name  = Faker::Name.last_name
    email      = Faker::Internet.unique.email(name: "#{first_name} #{last_name}")

    user = User.find_or_initialize_by(email: email)
    user.first_name = first_name
    user.last_name  = last_name
    user.password = "password"
    user.password_confirmation = "password"
    user.skip_confirmation! if user.respond_to?(:skip_confirmation!)

    user.save!
  end


puts "✅ Done."

# Add profiles to users
require 'open-uri'

puts "✅ Seeding user profiles."
User.all.each do |user|
  profile_attrs = {
    bio: Faker::Lorem.paragraph(sentence_count: 2),
    first_name: user.first_name,
    last_name: user.last_name,
    profession: Faker::Job.title,
    date_of_birth: Faker::Date.birthday(min_age: 18, max_age: 65)
  }

  # Generate a random avatar URL (pravatar has 1–70 images)
  avatar_url = "https://i.pravatar.cc/150?img=#{rand(1..70)}"
  avatar_file = URI.open(avatar_url)

  if user.profile.present?
    user.profile.update!(profile_attrs)
  else
    profile = user.create_profile!(profile_attrs)
    profile.avatar.attach(
      io: avatar_file,
      filename: "avatar.jpg",
      content_type: "image/jpeg"
      )
      full_address = "#{Faker::Address.city}, #{Faker::Address.country}"
      location = Location.create(
      latitude: Faker::Address.latitude,
      longitude: Faker::Address.longitude,
      address: "#{full_address}",
      locatable: profile
    )
    skills = Array.new(3) { Faker::Marketing.buzzwords }.uniq
    categories = Array.new(3) { Faker::Commerce.department } # or Faker::Company.industry
    profile.skill_list = skills
    profile.category_list = categories
    profile.save
  end
end

puts "✅ Done"

puts "✅ Seeding Admin user"
# Create admin user
user = User.find_or_initialize_by(email: "<EMAIL>")
user.assign_attributes(
  first_name: "Admin",
  last_name:  "Doe",
  password:   "adminpassword",
  password_confirmation: "adminpassword"
)
user.skip_confirmation! if user.respond_to?(:skip_confirmation!)
user.save!
user.add_role(:admin) unless user.has_role?(:admin)

puts "✅ Done"

puts "✅ Seeded #{User.count} users and #{Profile.count} profiles."

puts "🗨️ Seeding forum posts..."

User.all.each_with_index do |user, i|
  forum = Forum.create!(
    title: Faker::Lorem.sentence(word_count: 5),
    description: Faker::Lorem.paragraph(sentence_count: 3),
    owner: user
  )

  avatar_url = "https://i.pravatar.cc/150?img=#{rand(1..70)}"
  avatar_file = URI.open(avatar_url)

  if forum.persisted?
    forum.avatar.attach(
      io: avatar_file,
      filename: "avatar.jpg",
      content_type: "image/jpeg"
    )
  end
end

puts "✅ Seeded #{Forum.count} forum posts."

puts "Seeding projects...."

User.all.each_with_index do |user, i|
  project = Project.create!(
    name: Faker::Company.name,
    description: Faker::Lorem.paragraph(sentence_count: 3),
    owner: user,
    )
    full_address = "#{Faker::Address.city}, #{Faker::Address.country}"
    location = Location.create(
      latitude: Faker::Address.latitude,
      longitude: Faker::Address.longitude,
      address: "#{full_address}",
      locatable: project
    )
  # Add 3 collaborators with pravatar images
  User.order("RANDOM()").limit(3).each do |collaborator|
    project.project_memberships.create!(user: collaborator)
  end



  # Attach 3 random remote images from picsum.photos
  3.times do
    avatar_url = "https://picsum.photos/seed/#{rand(1000)}#{rand(1000)}/800/600"
    filename = "project_#{SecureRandom.hex(4)}.jpg"

    file = URI.open(avatar_url)
    project.images.attach(io: file, filename: filename, content_type: 'image/jpeg')
  end
puts "#{i+1}✅ Done"
end

puts "🏞️ Seeding 11 places..."

users = User.limit(10) # Use the first 11 users or fewer if not enough users exist

10.times do |i|
  user = users[i % users.size] # Reuse users if fewer than 11

  place = Place.create!(
    name: Faker::Address.community,
    description: Faker::Lorem.paragraph(sentence_count: 2),
    owner: user
  )

  # Attach a random avatar image for the place
  avatar_url = "https://picsum.photos/seed/place#{place.id}_#{rand(1000)}/800/600"
  avatar_file = URI.open(avatar_url)
  place.avatar.attach(
    io: avatar_file,
    filename: "place_avatar_#{place.id}.jpg",
    content_type: "image/jpeg"
  )

  # Optional: Mark some as verified
  place.update!(verified: [ true, false ].sample) if place.respond_to?(:verified)

  puts "✅ Seeded Place ##{i + 1}: #{place.name}"
end

puts "✅ Seeded #{Place.count} total places."

puts "📍 Seeding Locations..."

locatable_models = Profile.all # or another model that implements `has_many :locations, as: :locatable`

locatable_models.each do |locatable|
  Location.find_or_create_by!(
    locatable: locatable
  ) do |location|
    location.address = Faker::Address.full_address
    location.latitude = Faker::Address.latitude
    location.longitude = Faker::Address.longitude
  end
end

puts "✅ Seeded #{Location.count} locations."

puts "📷 Attaching carousel images to Locations..."

Location.all.each_with_index do |location, i|
  3.times do
    avatar_url = "https://picsum.photos/seed/location#{location.id}_#{rand(1000)}/800/600"
    filename = "location_#{SecureRandom.hex(4)}.jpg"

    file = URI.open(avatar_url)
    location.images.attach(io: file, filename: filename, content_type: 'image/jpeg')
  end
  puts "✅ Location ##{i + 1} with ID #{location.id} has images attached."
end

puts "✅ Success! happy coding."
