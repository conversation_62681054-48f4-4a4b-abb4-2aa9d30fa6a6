class CreateProjectJoinRequests < ActiveRecord::Migration[8.0]
  def change
    create_table :project_join_requests, id: :uuid do |t|
      t.references :project, null: false, foreign_key: true, type: :uuid
      t.references :user, null: false, foreign_key: true, type: :uuid
      t.datetime :date_approved
      t.integer :status, default: 0
      t.references :approver, null: true, foreign_key: { to_table: :users }, type: :uuid

      t.timestamps
    end
  end
end
