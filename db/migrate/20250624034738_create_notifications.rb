class CreateNotifications < ActiveRecord::Migration[8.0]
def change
    create_table :notifications, id: :uuid do |t|
      t.references :user, null: false, foreign_key: true, type: :uuid
      t.references :actor, null: false, foreign_key: { to_table: :users }, type: :uuid
      t.references :notifiable, polymorphic: true, null: false, type: :uuid
      t.boolean :read, default: false

      t.timestamps
    end

    add_index :notifications, :read
  end
end
