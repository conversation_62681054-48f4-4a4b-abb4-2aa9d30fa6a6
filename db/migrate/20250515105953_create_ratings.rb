class CreateRatings < ActiveRecord::Migration[8.0]
  def change
    create_table :ratings, id: :uuid do |t|
      t.integer :score, null: false
      t.references :user, null: false, foreign_key: true, type: :uuid  # User who created the rating
      t.references :rateable, polymorphic: true, null: false, type: :uuid  # Can be a User a Place or Project
      t.text :comment  # Optional comment with the rating
      t.timestamps
    end
    add_index :ratings, [ :user_id, :rateable_type, :rateable_id ], unique: true
  end
end
