class DropNotifications < ActiveRecord::Migration[8.0]
  def up
    drop_table :notifications
  end

  def down
    create_table :notifications do |t|
      t.references :user, null: false, type: :uuid
      t.references :actor, null: false, type: :uuid
      t.string :notifiable_type, null: false
      t.uuid :notifiable_id, null: false
      t.boolean :read, default: false
      t.timestamps

      t.index [ "notifiable_type", "notifiable_id" ], name: "index_notifications_on_notifiable"
    end
  end
end
