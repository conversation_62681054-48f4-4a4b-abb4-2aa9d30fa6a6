/// <reference types="vitest" />
/// <reference types="vite/client" />

import react from '@vitejs/plugin-react'
import tailwindcss from '@tailwindcss/vite'
import { defineConfig } from 'vite'
import RubyPlugin from 'vite-plugin-ruby'

export default defineConfig({
  build: {
    sourcemap: true,
  },
  plugins: [
    react(),
    tailwindcss(),
    RubyPlugin(),
  ],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: '../../setup.ts',
    css: true,
  },
})
