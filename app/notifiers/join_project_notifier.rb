# To deliver this notification:
#
# JoinProjectRequestNotifier.with(record: @post, message: "New post").deliver(User.all)

class JoinProjectNotifier < ApplicationNotifier
  # Add your delivery methods
  #
  # deliver_by :email do |config|
  #   config.mailer = "UserMailer"
  #   config.method = "new_post"
  # end
  #
  # bulk_deliver_by :slack do |config|
  #   config.url = -> { Rails.application.credentials.slack_webhook_url }
  # end
  #
  # deliver_by :custom do |config|
  #   config.class = "MyDeliveryMethod"
  # end

  # Add required params
  #
  # required_param :message
  notification_methods do
    # I18n helpers
    def message
      "You have a new Project member"
    end
  end
end
