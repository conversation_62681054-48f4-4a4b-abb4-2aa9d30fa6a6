# app/notifications/bookmark_notification.rb
class BookmarkNotification < Noticed::Event
  deliver_by :database

  param :bookmark
  param :actor
  param :bookmarkable

  def message
    "#{params[:actor].profile.display_name} bookmarked your #{bookmarkable_type} #{bookmarkable_name}"
  end

  def bookmarkable_type
    params[:bookmarkable].class.name.downcase
  end

  def bookmarkable_name
    case params[:bookmarkable]
    when Place, Project then params[:bookmarkable].name
    when Forum then params[:bookmarkable].title
    end
  end

 def url
    case params[:bookmarkable]
    when Place
      Rails.application.routes.url_helpers.places_path(params[:bookmarkable])
    when Project
      Rails.application.routes.url_helpers.projects_path(params[:bookmarkable])
    when Forum
      Rails.application.routes.url_helpers.forum_path(params[:bookmarkable])
    end
  end
end
