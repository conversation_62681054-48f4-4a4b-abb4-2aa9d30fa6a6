<div class="min-h-screen flex items-center justify-center bg-brown-transparent font-inter">
  <div class="w-full max-w-xl mx-4 overflow-hidden rounded-lg">
    <!-- Pattern border with content -->
    <div class="relative border-[10px] border-transparent rounded-2xl overflow-hidden" 
         style="border-image: url(<%= asset_path('strip.png') %>) 10 stretch">
      
      <!-- Content -->
      <div class="relative z-10 bg-white px-8 py-10 rounded-lg" style="padding : 40px">
        <h2 class="text-2xl font-bold text-center text-brown mb-4">Welcome Back BongoHubber</h2>

        <div class="mb-6 text-center">
          <span class="text-gray-800 text-base">Don't have an account? </span>
          <%= link_to "Sign up", new_registration_path(resource_name), class: "text-blue-600 font-medium text-base hover:underline" %>
        </div>

        <%= form_for(resource, as: resource_name, url: session_path(resource_name), html: { class: "space-y-6", data: { turbo: false } }) do |f| %>
          <div>
            <%= f.label :email, class: "block text-base font-medium text-gray-800 mb-1" do %>
              Email<span class="text-red-500">*</span>
            <% end %>
            <%= f.email_field :email, autofocus: true, autocomplete: "email",
                class: "w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-brown focus:border-brown",
                required: true %>
          </div>

          <div>
            <%= f.label :password, class: "block text-base font-medium text-gray-800 mb-1" do %>
              Password<span class="text-red-500">*</span>
            <% end %>
            <div class="relative flex items-center">
              <%= f.password_field :password, autocomplete: "current-password",
                  class: "w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-brown focus:border-brown pr-12 #{flash[:alert] ? 'border-red-500' : ''}",
                  required: true,
                  id: "password-field" %>
              <button type="button" 
                      onclick="togglePassword('password-field')" 
                      class="absolute right-3 top-1/2 -translate-y-1/2 flex items-center justify-center hover:cursor-pointer">
                <svg class="h-5 w-5 text-brown show-password" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                  <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd" />
                </svg>
                <svg class="h-5 w-5 text-brown hide-password hidden" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z" clip-rule="evenodd" />
                  <path d="M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z" />
                </svg>
              </button>
            </div>
            
            <% if flash[:alert] %>
              <p class=" flex items-center gap-2 mt-2 text-sm text-red">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none">
                            <path fill="red" d="M1 21h22L12 2 1 21z" />
                            <path fill="white" d="M11 9h2v5h-2zM11 16h2v2h-2z" />
                        </svg>
                <span><%= flash[:alert] %></span>
              </p>
            <% end %>
          </div>

          <div class="flex items-center justify-between">
            <% if devise_mapping.rememberable? %>
              <div class="flex items-center">
                <%= f.check_box :remember_me, class: "w-5 h-5 border-2 border-gray-300 rounded focus:ring-brown text-brown" %>
                <%= f.label :remember_me, class: "ml-2 text-gray-800 text-base" %>
              </div>
            <% end %>
            <%= link_to "Forgot Password?", new_password_path(resource_name), class: "text-blue-600 text-base hover:underline" %>
          </div>

          <%= f.submit "Login", class: "w-full py-3 bg-brown text-white text-lg font-semibold rounded-full hover:bg-black hover:cursor-pointer transition duration-300" %>
        <% end %>

        <div class="relative flex items-center justify-center mt-8 mb-6">
          <div class="absolute inset-0 flex items-center">
            <div class="w-full border-t border-gray-300"></div>
          </div>
          <div class="relative px-4 bg-white">
            <span class="text-gray-500">Or Continue with</span>
          </div>
        </div>

        <div class="grid grid-cols-2 gap-4">
          <%= link_to user_google_oauth2_omniauth_authorize_path,
              class: "py-2 px-4 border border-gray-300 rounded-full flex items-center justify-center hover:bg-gray-50",
              data: { turbo: false } do %>
            <span class="mr-2">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
                <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" />
                <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" />
                <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" />
                <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" />
              </svg>
            </span>
            Google
          <% end %>

          <%= link_to user_facebook_omniauth_authorize_path,
              class: "py-2 px-4 border border-gray-300 rounded-full flex items-center justify-center hover:bg-gray-50",
              data: { turbo: false } do %>
            <span class="mr-2">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
                <path fill="#1877F2" d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" />
              </svg>
            </span>
            Facebook
          <% end %>
        </div>

        <!-- Back to Home link -->
        <div class="mt-6 text-center">
          <%= link_to root_path, class: "inline-flex items-center text-gray-600 hover:text-gray-900" do %>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z" clip-rule="evenodd" />
            </svg>
            Back to Home
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  function togglePassword(fieldId) {
    const passwordField = document.getElementById(fieldId);
    const button = passwordField.nextElementSibling;
    const showIcon = button.querySelector('.show-password');
    const hideIcon = button.querySelector('.hide-password');

    if (passwordField.type === 'password') {
      passwordField.type = 'text';
      showIcon.classList.add('hidden');
      hideIcon.classList.remove('hidden');
    } else {
      passwordField.type = 'password';
      showIcon.classList.remove('hidden');
      hideIcon.classList.add('hidden');
    }
  }
</script>
