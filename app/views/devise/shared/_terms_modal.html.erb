<div class="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto">
    <div class="fixed inset-0 transition-opacity bg-gray-500/80 bg-opacity-1" onclick="closeTermsModal()"></div>

    <div class="relative w-full max-w-md sm:max-w-lg md:max-w-xl lg:max-w-2xl px-4 pt-5 pb-4 sm:p-6 min-h-[50vh] overflow-hidden text-left transition-all transform bg-white rounded-4xl border-[10px] border-transparent shadow-xl sm:my-8" style="border-image: url(<%= asset_path('strip.png') %>) 10 stretch">
        <div class="absolute top-0 right-0 pt-4 pr-4">
            <button type="button" class="text-black bg-white rounded-md hover:text-gray-500 focus:outline-none" onclick="closeTermsModal()">
                <span class="sr-only hover:cursor-pointer">Close</span>
                <svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>

        <div class="sm:flex sm:items-start">
            <div class="w-full mt-3 text-center sm:mt-0 sm:text-left">
                <h3 class="text-lg font-medium leading-6 text-gray-900">Terms and Conditions</h3>

                <div class="mt-4 p-4 border border-primarybrown rounded-lg h-90 overflow-y-auto text-sm text-black">
                    <p class="mb-4">✅ You own what you create – Just make sure it's original and respectful.</p>
                    <p class="mb-4">🤝 By sharing, you let BongoHub display your work on the platform.</p>
                    <p class="mb-4">🚫 No hate, spam, or stolen stuff – Keep it clean, positive, and yours.</p>
                    <p class="mb-4">🛠️ Admins can remove anything that breaks the rules.</p>
                    <p class="mb-4">📣 See something off (like stolen work)? Flag it – we'll check it out.</p>
                    <p class="mb-4">🔍 Verification = more visibility – Meet our quality standards and shine brighter.</p>
                    <p class="mb-4">🔄 Leaving? You can delete or transfer ownership of your content.</p>
                    <p class="mb-4">
                        📚 Read our full <a href='#' class="text-primarybrown" target='blank'>Terms & Conditions</a> and 
                        <a href='#' class="text-primarybrown" target='blank'>Privacy Policy</a> for all the details.
                    </p>

                    <div class="flex items-center">
                        <input type="checkbox" 
                               id="terms-checkbox" 
                               onchange="toggleSubmitButton()"
                               class="mr-2 h-4 w-4 rounded border-gray-300 text-black focus:ring-primarybrown">
                        <label for="terms-checkbox" class="text-sm text-gray-600">
                            I agree to the terms & conditions
                        </label>
                    </div>
                </div>

                <div class="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                    <button type="button" 
                            onclick="acceptTerms()"
                            id="terms-submit-button"
                            class="w-full inline-flex justify-center rounded-3xl border border-transparent shadow-sm px-4 py-2 bg-primarybrown text-base font-medium text-white hover:bg-brownhover hover:cursor-pointer sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed">
                        Create Account
                    </button>
                    <button type="button" onclick="closeTermsModal()"
                        class="mt-3 w-full inline-flex justify-center rounded-3xl border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 hover:cursor-pointer focus:outline-none sm:mt-0 sm:w-auto sm:text-sm">
                        Cancel
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>