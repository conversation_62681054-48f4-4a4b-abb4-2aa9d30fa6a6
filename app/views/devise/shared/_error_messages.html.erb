<% if resource.errors.any? %>
<div id="error_explanation" data-turbo-cache="false" class="text-red-500 text-sm">
    <ul>
        <% resource.errors.full_messages.each do |message| %>
        <li>
            <div class="flex items-center gap-2"> <!-- Added flex container -->
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none">
                    <path fill="red" d="M1 21h22L12 2 1 21z" />
                    <path fill="white" d="M11 9h2v5h-2zM11 16h2v2h-2z" />
                </svg>
                <span><%= message %></span> <!-- Wrapped in span for better control -->
            </div>
        </li>
        <% end %>
    </ul>
</div>
<% end %>
