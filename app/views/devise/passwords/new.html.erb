<%= render "devise/shared/auth_header" %>
<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8 bg-white p-8 rounded-xl shadow-xl">
        <div>
            <h2 class="mt-6 text-center text-3xl font-bold text-primarybrown">
                Forgot your password?
            </h2>
            <p class="mt-2 text-center text-sm text-primarybrown">
                Enter your email address and we'll send you a link to reset your password.
            </p>
        </div>

        <%= form_for(resource, as: resource_name, url: password_path(resource_name), html: { method: :post, class: "mt-8 space-y-6" }) do |f| %>

        <div class="rounded-md -space-y-px">
            <div class="mb-4">
                <%= f.label :email, class: "block text-sm font-medium text-gray-700 mb-1" %>
                <%= f.email_field :email, autofocus: true, autocomplete: "email",
              class: "appearance-none rounded-xl relative block w-full px-4 py-2 border border-primarybrown placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-primarybrown focus:border-primarybrown focus:z-10 sm:text-sm" %>
            </div>
        </div>
        <% if resource.errors.any? %>
        <div id="error_explanation" data-turbo-cache="false" class="text-red-500 text-sm">
            <ul>
                <% resource.errors.full_messages.each do |message| %>
                <li>
                    <div class="flex items-center gap-2">
                        <!-- Added flex container -->
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none">
                            <path fill="red" d="M1 21h22L12 2 1 21z" />
                            <path fill="white" d="M11 9h2v5h-2zM11 16h2v2h-2z" />
                        </svg>
                        <span><%= message %></span> <!-- Wrapped in span for better control -->
                    </div>
                </li>
                <% end %>
            </ul>
        </div>
        <% end %>

        <div>
            <%= f.submit "Send Reset Instructions",
            class: "w-full flex justify-center py-3 px-4 border border-transparent rounded-xl shadow-sm text-md font-semibold text-white bg-primarybrown hover:bg-black focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primarybrown transition duration-300" %>
        </div>
        <% end %>

        <a href="/" class="mt-6 text-center">
            <p>Go Back</p>
        </a>
    </div>
</div>