<%= render "devise/shared/auth_header" %>
<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8 bg-white p-8 rounded-xl shadow-xl">
        <div>
            <h2 class="mt-6 text-center text-3xl font-bold text-primarybrown">
                Change your password
            </h2>
            <p class="mt-2 text-center text-sm text-primarybrown">
                Enter new password to continue
            </p>
        </div>

        <%= form_for(resource, as: resource_name, url: password_path(resource_name), html: { method: :put, class: "mt-8 space-y-6" }) do |f| %>
        <div class="rounded-md -space-y-px">
            <div class="mb-4">
                <%= f.label :password, "New password", class: "block text-sm font-medium text-gray-700 mb-1" %>
                <% if @minimum_password_length %>
                <span class="text-xs text-gray-500">(<%= @minimum_password_length %> characters minimum)</span>
                <% end %>
                <div class="relative">
                    <%= f.password_field :password, autofocus: true, autocomplete: "new-password",
                class: "appearance-none rounded-xl relative block w-full px-4 py-2 border border-primarybrown placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-primarybrown focus:border-primarybrown focus:z-10 sm:text-sm",
                id: "password-field" %>
                    <button type="button" onclick="togglePassword('password-field')" class="absolute inset-y-0 right-0 pr-3 flex items-center">
                        <svg class="h-5 w-5 text-primarybrown show-password" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                            <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd" />
                        </svg>
                        <svg class="h-5 w-5 text-primarybrown hide-password hidden" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z" clip-rule="evenodd" />
                            <path d="M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z" />
                        </svg>
                    </button>
                </div>
            </div>

            <div class="mb-4">
                <%= f.label :password_confirmation, "Confirm new password", class: "block text-sm font-medium text-gray-700 mb-1" %>
                <div class="relative">
                    <%= f.password_field :password_confirmation, autocomplete: "new-password",
                class: "appearance-none rounded-xl relative block w-full px-4 py-2 border border-primarybrown placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-primarybrown focus:border-primarybrown focus:z-10 sm:text-sm",
                id: "confirm-password-field" %>
                    <button type="button" onclick="togglePassword('confirm-password-field')" class="absolute inset-y-0 right-0 pr-3 flex items-center">
                        <svg class="h-5 w-5 text-primarybrown show-password" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                            <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd" />
                        </svg>
                        <svg class="h-5 w-5 text-primarybrown hide-password hidden" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z" clip-rule="evenodd" />
                            <path d="M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>
        <% if resource.errors.any? %>
        <div id="error_explanation" data-turbo-cache="false" class="text-red-500 text-sm">
            <ul>
                <% resource.errors.full_messages.each do |message| %>
                <li>
                    <div class="flex items-center gap-2">
                        <!-- Added flex container -->
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none">
                            <path fill="red" d="M1 21h22L12 2 1 21z" />
                            <path fill="white" d="M11 9h2v5h-2zM11 16h2v2h-2z" />
                        </svg>
                        <span><%= message %></span> <!-- Wrapped in span for better control -->
                    </div>
                </li>
                <% end %>
            </ul>
        </div>
        <% end %>

        <%= f.hidden_field :reset_password_token %>

        <div>
            <%= f.submit "Update password",
            class: "w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-md text-sm font-semibold text-white bg-primarybrown hover:bg-black focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primarybrown transition duration-300" %>
        </div>
        <% end %>
    </div>
</div>

<script>
    function togglePassword(fieldId) {
        const passwordField = document.getElementById(fieldId);
        const button = passwordField.nextElementSibling;
        const showIcon = button.querySelector('.show-password');
        const hideIcon = button.querySelector('.hide-password');

        if (passwordField.type === 'password') {
            passwordField.type = 'text';
            showIcon.classList.add('hidden');
            hideIcon.classList.remove('hidden');
        } else {
            passwordField.type = 'password';
            showIcon.classList.remove('hidden');
            hideIcon.classList.add('hidden');
        }
    }
</script>