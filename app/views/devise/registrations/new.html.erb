<div class="lg:hidden">
    <%= render "devise/shared/auth_header" %>
</div>
<div class="min-h-screen flex flex-col md:flex-row items-center justify-center p-4 sm:p-8 gap-8 bg-white relative overflow-hidden">
    <!-- Background image - only on md and smaller -->
    <div class="absolute inset-0 lg:hidden bg-[url('/assets/angelika.png')] bg-cover bg-center opacity-10 z-0"></div>

    <!-- Main content wrapper -->
    <div class="relative z-10 w-full flex flex-col md:flex-row items-center justify-center gap-8">
        <!-- Desktop Left Section -->
        <div class="hidden lg:flex flex-col justify-center items-center flex-1 max-w-xl xl:max-w-2xl 2xl:max-w-3xl">
            <div class="rounded-lg overflow-hidden relative">
                <div class="relative h-[600px] w-[450px] 2xl:h-[800px] 2xl:w-[650px] bg-primaryorange">
                    <!-- Border layer with image -->
                    <div class="absolute inset-0 pointer-events-none border-[10px] border-transparent" style="border-image: url(<%= asset_path('angelika.png') %>) 50 round;"></div>

                    <div class="p-6 z-10 relative flex flex-col h-full">
                        <a href="/" class="self-start flex items-center text-black bg-opacity-20 rounded-full p-2 hover:bg-opacity-30 transition mb-6">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M19 12H5M12 19l-7-7 7-7" />
                            </svg>
                            <span class="ml-2">Go Back to website</span>
                        </a>

                        <div class="flex-grow flex flex-col items-center text-center">
                            <%= image_tag "logo.png", alt: "Logo", class: "h-20 2xl:h-30 w-auto mb-6" %>
                            <h2 class="text-2xl 2xl:text-4xl text-[#1B0E01] font-semibold mb-4">
                                Become A BongoHubber
                            </h2>
                            <p class="text-base 2xl:text-lg px-4 mb-auto">
                                Create. Connect. Grow. Join the tribe building Africa's tomorrow.
                                Access events, mentorship, workspaces and the community that gets you.
                            </p>
                        </div>

                        <div class="absolute bottom-0 left-0 w-full h-1/2 pointer-events-none">
                            <%= image_tag "happyafroamerican.png", alt: "BongoHub Community", class: "w-auto h-auto object-cover" %>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Form Section -->
        <div class="w-full md:w-auto flex-1 max-w-md lg:max-w-lg xl:max-w-xl 2xl:max-w-2xl rounded-xl p-6 sm:p-8 lg:p-10 mb-20 mt-20">
            <div class="relative w-full space-y-8">
                <div class="text-center">
                    <h2 class="text-3xl font-bold text-primarybrown">Create Your Account</h2>
                    <p class="mt-2 text-sm text-gray-600">
                        Already have an account? <%= link_to "Log in", new_session_path(resource_name), class: "text-blue-600 font-medium text-base hover:underline" %>
                    </p>
                </div>

                <%= form_for(resource, as: resource_name, url: registration_path(resource_name), html: { class: "space-y-6" }) do |f| %>

                <div class="space-y-6">
                    <!-- Name Row -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <%= f.label :first_name, "First Name *", class: "block mb-1 text-sm font-medium text-gray-700" %>
                            <%= f.text_field :first_name, autofocus: true, 
                                class: "w-full px-4 py-2 border #{resource.errors[:first_name].any? ? 'border-red-500' : 'border-black'} rounded-xl focus:ring-indigo-500 focus:border-indigo-500" %>
                            <% if resource.errors[:first_name].any? %>
                            <p class="mt-1 text-sm text-red-600"><%= resource.errors[:first_name].join(", ") %></p>
                            <% end %>
                        </div>
                        <div>
                            <%= f.label :last_name, "Last Name *", class: "block mb-1 text-sm font-medium text-gray-700" %>
                            <%= f.text_field :last_name, 
                                class: "w-full px-4 py-2 border #{resource.errors[:last_name].any? ? 'border-red-500' : 'border-black'} rounded-xl focus:ring-indigo-500 focus:border-indigo-500" %>
                            <% if resource.errors[:last_name].any? %>
                            <p class="mt-1 text-sm text-red-600"><%= resource.errors[:last_name].join(", ") %></p>
                            <% end %>
                        </div>
                    </div>

                    <!-- Email -->
                    <div>
                        <%= f.label :email, "Email *", class: "block mb-1 text-sm font-medium text-gray-700" %>
                        <%= f.email_field :email, 
                            class: "w-full px-4 py-2 border #{resource.errors[:email].any? ? 'border-red-500' : 'border-black'} rounded-xl focus:ring-indigo-500 focus:border-indigo-500" %>
                        <% if resource.errors[:email].any? %>
                        <p class="mt-1 text-sm text-red-600"><%= resource.errors[:email].join(", ") %></p>
                        <% end %>
                    </div>

                    <!-- Password Fields -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="relative">
                            <%= f.label :password, "Password *", class: "block mb-1 text-sm font-medium text-gray-700" %>
                            <div class="relative">
                                <%= f.password_field :password, 
                                    class: "w-full px-4 py-2 border #{resource.errors[:password].any? ? 'border-red-500' : 'border-black'} rounded-xl focus:ring-indigo-500 focus:border-indigo-500 pr-10",
                                    id: "password-field" %>
                                <button type="button" onclick="togglePassword('password-field')" class="absolute hover:cursor-pointer right-3 top-1/2 -translate-y-1/2">
                                    <svg class="h-5 w-5 text-primarybrown show-password" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                                        <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd" />
                                    </svg>
                                </button>
                            </div>
                            <% if resource.errors[:password].any? %>
                            <p class="mt-1 text-sm text-red-600"><%= resource.errors[:password].join(", ") %></p>
                            <% end %>
                        </div>

                        <div class="relative">
                            <%= f.label :password_confirmation, "Confirm Password *", class: "block mb-1 text-sm font-medium text-gray-700" %>
                            <div class="relative">
                                <%= f.password_field :password_confirmation, 
                                    class: "w-full px-4 py-2 border #{resource.errors[:password_confirmation].any? ? 'border-red-500' : 'border-black'} rounded-xl focus:ring-indigo-500 focus:border-indigo-500 pr-10",
                                    id: "confirm-password-field" %>
                                <button type="button" onclick="togglePassword('confirm-password-field')" class="absolute hover:cursor-pointer right-3 top-1/2 -translate-y-1/2">
                                    <svg class="h-5 w-5 text-primarybrown show-password" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                                        <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd" />
                                    </svg>
                                </button>
                            </div>
                            <% if resource.errors[:password_confirmation].any? %>
                            <p class="mt-1 text-sm text-red-600"><%= resource.errors[:password_confirmation].join(", ") %></p>
                            <% end %>
                        </div>
                    </div>
                </div>

                <p class="text-xs text-gray-500">Password must be at least 8 characters</p>

                <!-- Terms Modal -->
                <div id="terms-modal" class="hidden fixed inset-0 z-50 overflow-y-auto">
                    <%= render "devise/shared/terms_modal" %>
                </div>

                <div>
                    <button type="button" onclick="openTermsModal()" class="w-full py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primarybrown hover:bg-brownhover hover:cursor-pointer">
                        Create Account
                    </button>
                </div>

                <!-- Hidden submit button that will be triggered after terms acceptance -->
                <%= f.submit "Sign up", id: "actual-submit", class: "hidden" %>

                <div class="flex items-center justify-center gap-4 my-6">
                    <div class="flex-grow border-t border-gray-300"></div>
                    <span class="text-sm font-semibold text-gray-500">Or Register with</span>
                    <div class="flex-grow border-t border-gray-300"></div>
                </div>
                <% end %>

                <!-- Social Login Buttons -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Hidden OAuth forms -->
                    <%= button_to user_google_oauth2_omniauth_authorize_path,
                        method: :post,
                        data: { turbo: false },
                        class: "hidden",
                        id: "google-oauth-form" do %>
                    <button type="submit"></button>
                    <% end %>

                    <%= button_to user_facebook_omniauth_authorize_path,
                        method: :post,
                        data: { turbo: false },
                        class: "hidden",
                        id: "facebook-oauth-form" do %>
                    <button type="submit"></button>
                    <% end %>

                    <!-- Visible buttons that trigger terms modal -->
                    <button type="button" onclick="showTermsForProvider('google')" class="w-full py-2 px-4 border border-black rounded-xl text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 hover:cursor-pointer flex items-center justify-center gap-2">
                        <span class="mr-2">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
                                <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" />
                                <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" />
                                <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" />
                                <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" />
                            </svg>
                        </span>
                        Google
                    </button>

                    <button type="button" onclick="showTermsForProvider('facebook')" class="w-full py-2 px-4 border border-black rounded-xl text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 hover:cursor-pointer flex items-center justify-center gap-2">
                        <span class="mr-2">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
                                <path fill="#1877F2" d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" />
                            </svg>
                        </span>
                        Facebook
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Javascript for password toggle and terms modal -->
<script>
    let currentProvider = null;

    // Initialize button state on page load
    document.addEventListener('DOMContentLoaded', function() {
        const submitButton = document.getElementById('terms-submit-button');
        if (submitButton) {
            submitButton.disabled = true;
        }
    });

    function togglePassword(fieldId) {
        const field = document.getElementById(fieldId);
        const button = field.nextElementSibling;
        const icon = button.querySelector('svg');

        if (field.type === 'password') {
            field.type = 'text';
            icon.innerHTML = `<path fill-rule="evenodd" d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z" clip-rule="evenodd" /><path d="M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z" />`;
        } else {
            field.type = 'password';
            icon.innerHTML = `<path d="M10 12a2 2 0 100-4 2 2 0 000 4z" /><path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd" />`;
        }
    }

    function showTermsForProvider(provider) {
        currentProvider = provider;
        openTermsModal();
        const submitButton = document.getElementById('terms-submit-button');
        const buttonText = provider === 'google' ? 'Continue with Google' : 'Continue with Facebook';
        submitButton.textContent = buttonText;
        submitButton.disabled = true;
    }

    function openTermsModal() {
        const modal = document.getElementById('terms-modal');
        const checkbox = document.getElementById('terms-checkbox');
        const submitButton = document.getElementById('terms-submit-button');

        // Reset state when opening modal
        modal.classList.remove('hidden');
        checkbox.checked = false;
        submitButton.disabled = true;
    }

    function closeTermsModal() {
        const modal = document.getElementById('terms-modal');
        const checkbox = document.getElementById('terms-checkbox');
        const submitButton = document.getElementById('terms-submit-button');

        modal.classList.add('hidden');
        checkbox.checked = false;
        submitButton.disabled = true;
        submitButton.textContent = 'Create Account'; // Reset button text
        currentProvider = null;
    }

    function acceptTerms() {
        const checkbox = document.getElementById('terms-checkbox');

        if (!checkbox.checked) {
            return; // Don't proceed if checkbox isn't checked
        }

        if (currentProvider) {
            const formId = `${currentProvider}-oauth-form`;
            const form = document.getElementById(formId);

            if (form) {
                // Trigger the form submission using a click event
                const submitButton = form.querySelector('button[type="submit"]');
                if (submitButton) {
                    submitButton.click();
                } else {
                    // If no submit button exists, create and trigger one
                    const button = document.createElement('button');
                    button.type = 'submit';
                    form.appendChild(button);
                    button.click();
                    form.removeChild(button);
                }
            } else {
                console.error(`OAuth form with ID '${formId}' not found`);
            }
        } else {
            document.getElementById('actual-submit').click();
        }
        closeTermsModal();
    }

    function toggleSubmitButton() {
        const checkbox = document.getElementById('terms-checkbox');
        const submitButton = document.getElementById('terms-submit-button');

        if (!checkbox || !submitButton) return;

        // Enable/disable button based on checkbox state
        submitButton.disabled = !checkbox.checked;

        // Update button styles
        if (checkbox.checked) {
            submitButton.classList.remove('opacity-50', 'cursor-not-allowed');
            submitButton.classList.add('hover:bg-brownhover');
        } else {
            submitButton.classList.add('opacity-50', 'cursor-not-allowed');
            submitButton.classList.remove('hover:bg-brownhover');
        }
    }
</script>