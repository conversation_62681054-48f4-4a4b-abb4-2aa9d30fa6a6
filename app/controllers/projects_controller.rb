# app/controllers/projects_controller.rb
class ProjectsController < ApplicationController
  def index
    @projects = Project.with_attached_images.includes(:location, :taggings, project_memberships: { user: { profile: { avatar_attachment: :blob } } })

    bookmarked_ids = current_user ?
      current_user.bookmarks.where(bookmarkable_type: "Project").pluck(:bookmarkable_id) : []

    render inertia: "Projects/Index", props: {
      projects: @projects.map do |project|
        {
          id: project.id,
          name: project.name,
          description: project.description,
          images: project.images.map { |img| url_for(img) },
          tags: project.category_list,
          services: project.service_list,
          location: project.location,
          views: project.views,
          owner: User.find(project.owner_id),
          is_member: project.has_member?(current_user),
          collaborators: project.project_memberships.map do |membership|
            user = membership.user
            {
              id: user.id,
              name: "#{user.first_name} #{user.last_name}",
              image: if user.profile&.avatar&.attached?
                      url_for(user.profile.avatar)
                     else
                      "https://i.pravatar.cc/150?u=#{user.id}"
                     end

            }
          end

        }
      end,
      bookmarkedProjectIds: bookmarked_ids,
      user: current_user_with_avatar
    }
  end

  def projectsingleshow
    project = Project.with_attached_images.includes(:location, :taggings, project_memberships: { user: { profile: { avatar_attachment: :blob } } }).find(params[:id])
    bookmarked_ids = current_user ?
      current_user.bookmarks.where(bookmarkable_type: "Project").pluck(:bookmarkable_id) : []
    track_action "Viewed Project", track_action_hash
    render inertia: "Projectpublic/Index", props: {
      project: {
        id: project.id,
        name: project.name,
        owner: "#{project.owner.profile.first_name} #{project.owner.profile.last_name}",
        ownerId: project.owner_id,
        description: project.description,
        images: project.images.map { |img| url_for(img) },
        tags: project.category_list,
        services: project.service_list,
        location: project.location,
        is_member: project.has_member?(current_user),
        collaborators: project.project_memberships.map do |membership|
          user = membership.user
          {
            id: user.id,
            name: "#{user.first_name} #{user.last_name}",
            image: if user.profile&.avatar&.attached?
                    url_for(user.profile.avatar)
                   else
                    "https://i.pravatar.cc/150?u=#{user.id}"
                   end
          }
        end
      },
      bookmarkedProjectIds: bookmarked_ids,
      user: current_user_with_avatar
    }
  end

  def recomendation
    @projects = Project.with_attached_images.includes(project_memberships: { user: { profile: { avatar_attachment: :blob } } }).limit(4)

    render json: {
      projects: @projects.map do |project|
        {
          id: project.id,
          name: project.name,
          description: project.description,
          services: project.service_list,
          images: project.images.map { |img| url_for(img) },
          collaborators: project.project_memberships.map do |membership|
            user = membership.user
            {
              id: user.id,
              name: "#{user.first_name} #{user.last_name}",
              image: if user.profile&.avatar&.attached?
                      url_for(user.profile.avatar)
                     else
                      "https://i.pravatar.cc/150?u=#{user.id}"
                     end
            }
          end
        }
      end
    }
  end

  def user_projects
    @projects = current_user.projects.with_attached_images.includes(:location, project_memberships: { user: { profile: { avatar_attachment: :blob } } })
    render json: {
      projects: @projects.map do |project|
        {
          id: project.id,
          name: project.name,
          description: project.description,
          location: project.location,
          tags: project.category_list,
          services: project.service_list,
          images: project.images.map { |img| url_for(img) },
          collaborators: project.project_memberships.map do |membership|
            user = membership.user
            {
              id: user.id,
              name: "#{user.first_name} #{user.last_name}",
              image: if user.profile&.avatar&.attached?
                      url_for(user.profile.avatar)
                     else
                      "https://i.pravatar.cc/150?u=#{user.id}"
                     end
            }
          end
        }
      end
    }
  end

  def search
    query = params[:query]
    @projects = Project.search_all_fields(query).with_attached_images.includes(:location, :taggings, project_memberships: { user: { profile: { avatar_attachment: :blob } } })
        render inertia: "Projects/Index", props: {
        projects: @projects.map do |project|
        {
          id: project.id,
          name: project.name,
          description: project.description,
          images: project.images.map { |img| url_for(img) },
          location: project.location,
          services: project.service_list,
          collaborators: project.project_memberships.map do |membership|
            user = membership.user
            {
              id: user.id,
              name: "#{user.first_name} #{user.last_name}",
              image: if user.profile&.avatar&.attached?
                      url_for(user.profile.avatar)
                     else
                      "https://i.pravatar.cc/150?u=#{user.id}"
                     end
            }
          end
        }
      end,
      user: current_user_with_avatar
    }
    end

  def new_project
      tags = JSON.parse(params[:tags]) rescue []
      services = JSON.parse(params[:whatWeDo]) rescue []

      project = current_user.projects.create(
        name: params[:name],
        description: params[:description],
      )

      if params[:profile_image].present?
        project.profile_image.attach(params[:profile_image])
      end

      # Attach gallery images if present
      if params[:gallery_images].present?
        params[:gallery_images].each do |img|
          project.images.attach(img)
        end
      end

      project.category_list = tags.presence || []
      project.service_list = services.presence || []
      project.save

      if params[:location]
        if project.location
          project.location.update!(address: params[:location])
        else
          location = project.build_location(address: params[:location])
          location.save!
        end
      end

      if project.persisted?
        redirect_to profile_path
      else
        redirect_to profile_path, status: :unprocessable_entity
      end
  end

  def bookmarked
    bookmarks = current_user.bookmarks.where(bookmarkable_type: "Project")
    project_ids = bookmarks.pluck(:bookmarkable_id)

    projects = Project
      .where(id: project_ids)
      .with_attached_images
      .includes(project_memberships: { user: { profile: { avatar_attachment: :blob } } })

    render json: {
      projects: projects.map do |project|
        {
          id: project.id,
          name: project.name,
          description: project.description,
          services: project.service_list,
          images: project.images.map { |img| url_for(img) },
          collaborators: project.project_memberships.map do |membership|
            user = membership.user
            {
              id: user.id,
              name: "#{user.first_name} #{user.last_name}",
              image: if user.profile&.avatar&.attached?
                      url_for(user.profile.avatar)
                     else
                      "https://i.pravatar.cc/150?u=#{user.id}"
                     end
            }
          end
        }
      end
    }
  end

  private
  def track_action_hash
      {
        user: current_user&.display_name || "Guest",
        time: Time.now
      }
  end
end
