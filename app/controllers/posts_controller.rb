class PostsController < ApplicationController
    include ActionView::Helpers::<PERSON><PERSON><PERSON><PERSON>
    def show
        forum = Forum.find(params[:id])
        posts = forum.posts

        post_data = posts.map do |post|
            {
                id: post.id,
                title: post.title,
                content: post.content,
                owner: post.owner,
                avatar: url_for(post.owner.profile.avatar),
                created_ago: time_ago_in_words(post.created_at).gsub(/^about /, "") + " ago",
                likes: post.likes
            }
        end

        render json: { data: post_data }
    end

    def create
        forum = Forum.kept.find(params[:forumId])

        if params[:title].present? && params[:content].present? && params[:userId].present?
            post = forum.posts.create(title: params[:title], content: params[:content], owner_id: params[:userId])
            if post.persisted?
            render json: { data: post.as_json }
            else
            render inertia: {
                errors: post.errors.full_messages
            }, status: :unprocessable_entity
            end
        else
            render inertia: {
            error: "Missing parameters"
            }, status: :bad_request
        end
    end

    def add_comment
        post = Post.find(params[:postId])
        user = User.find(params[:author])
        post.comments.create(comment: params[:content], commenter: user)
        redirect_back(fallback_location: root_path)
    end
end
