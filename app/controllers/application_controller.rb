class ApplicationController < ActionController::Base
  include Pundit::Authorization
  include Trackable

  protect_from_forgery with: :exception
  rescue_from Pundit::NotAuthorizedError, with: :user_not_authorized
  before_action :configure_permitted_parameters, if: :devise_controller?

  def after_sign_in_path_for(resource)
    if resource.has_role?(:admin)
      admin_dashboard_path
    else
      if resource.profile&.bio.blank?
        profile_path
      else
        root_path
      end
    end
  end

  allow_browser versions: :modern

  def not_found
    respond_to do |format|
      format.html { render file: "#{Rails.root}/public/404.html", status: :not_found, layout: false }
      format.json { render json: { error: "Not Found" }, status: :not_found }
      format.any  { head :not_found }
    end
  end

  private

  def configure_permitted_parameters
    devise_parameter_sanitizer.permit(:sign_up, keys: [ :first_name, :last_name ])
    devise_parameter_sanitizer.permit(:accept_invitation, keys: [ :first_name, :last_name ])
  end

  def user_not_authorized
    respond_to do |format|
      format.html do
        redirect_to root_path, alert: "You are not authorized to perform this action."
      end
    end
  end

  def authenticate_user!
    unless current_user
      redirect_to user_session_path, alert: "You must be logged in to access this page."
    end
  end

  def current_user_with_avatar
    return nil unless current_user
    user_data = current_user.as_json(only: [ :id, :email, :first_name, :last_name ])
    if current_user.profile&.avatar&.attached?
      user_data[:avatar_url] = url_for(current_user.profile.avatar)
    end
    user_data
  end
end
