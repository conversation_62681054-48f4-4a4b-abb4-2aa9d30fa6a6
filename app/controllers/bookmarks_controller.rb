# app/controllers/bookmarks_controller.rb
class BookmarksController < ApplicationController
  before_action :authenticate_user!

def create
  bookmarkable = find_bookmarkable
  bookmark = current_user.bookmarks.new(bookmarkable: bookmarkable)

  if bookmark.save
    # Create notification for resource owner
    if %w[Place Project Forum].include?(bookmarkable.class.name) &&
       bookmarkable.owner != current_user
      BookmarkNotification.with(
        bookmark: bookmark,
        actor: current_user,
        bookmarkable: bookmarkable
      ).deliver_later(bookmarkable.owner)
    end
    render json: { success: true }, status: :created
  else
    render json: { error: bookmark.errors.full_messages }, status: :unprocessable_entity
  end
end

  def destroy
    bookmarkable = find_bookmarkable
    bookmark = current_user.bookmarks.find_by(bookmarkable: bookmarkable)

    if bookmark&.destroy
      render json: { success: true }, status: :ok
    else
      render json: { error: "Not found" }, status: :not_found
    end
  end

  private

def find_bookmarkable
  allowed_classes = {
    "Profile" => Profile,
    "Project" => Project,
    "Place" => Place,
    "Forum" => Forum
  }

  klass = allowed_classes[params[:bookmarkable_type]]
  return render json: { error: "Invalid resource" }, status: :bad_request unless klass

  klass.find(params[:bookmarkable_id])
rescue ActiveRecord::RecordNotFound
  render json: { error: "Invalid resource" }, status: :bad_request
end
end
