# app/controllers/people_controller.rb
class PeopleController < ApplicationController
  def index
    profiles = Profile.with_attached_avatar
    profiles = profiles.where(verified: true) if params[:verified] == "true"
    profiles = profiles.includes(:location, :taggings)

    bookmarked_ids = current_user ?
      current_user.bookmarks.where(bookmarkable_type: "Profile").pluck(:bookmarkable_id) : []

    render inertia: "People/Index", props: {
      profiles: profiles.map do |profile|
        {
          id:             profile.id,
          user_id:        profile.user_id,
          name:           "#{profile.first_name} #{profile.last_name}",
          bio:            profile.bio,
          date_of_birth:  profile.date_of_birth,
          profession:     profile.profession,
          verified:       profile.verified,
          avatar_url:     profile.avatar.attached? ? url_for(profile.avatar) : "https://i.pravatar.cc/150?u=#{profile.id}",
          skills:         profile.skill_list,
          address:        profile.location&.address,
          location:       profile.location,
          views:          profile.views
        }
      end,
      bookmarkedProfileIds: bookmarked_ids,
      user: current_user_with_avatar
    }
  end


def people_recomendataion
  profiles = Profile.with_attached_avatar
  if params[:verified].to_s == "true"
    profiles = profiles.where(verified: true).where.not(id: current_user.id)
  end

  render json: {
    profiles: profiles.map do |profile|
      {
          id: profile.id,
          name: "#{profile.first_name} #{profile.last_name}",
          bio: profile.bio,
          date_of_birth: profile.date_of_birth,
          profession: profile.profession,
          verified: profile.verified,
          avatar_url: profile.avatar.attached? ? url_for(profile.avatar) : nil,
          skills: profile.user.skill_list,
          address: profile.location&.address
      }
    end
  }
end

def search
    query = params[:query]
    @profiles = Profile.search_all_fields(query).with_attached_avatar.includes(:location)

    render inertia: "People/Index", props: {
      profiles: @profiles.map do |profile|
        {
          id: profile.id,
          bio: profile.bio,
          verified: profile.verified,
          name: "#{profile.first_name} #{profile.last_name}",
          date_of_birth: profile.date_of_birth,
          profession: profile.profession,
          avatar_url: profile.avatar.attached? ? url_for(profile.avatar) : "https://i.pravatar.cc/150?u=#{profile.id}",
          skills: profile.user.skill_list,
          address: profile.location&.address
        }
      end,
      query: query,
      user: current_user_with_avatar
    }
  end
  def bookmarked
  bookmarked_profiles = current_user.bookmarks
    .where(bookmarkable_type: "Profile")
    .includes(bookmarkable: [ :location, avatar_attachment: :blob ])
    .map(&:bookmarkable)

  render json: {
    profiles: bookmarked_profiles.map do |profile|
      {
        id: profile.id,
        name: "#{profile.first_name} #{profile.last_name}",
        bio: profile.bio,
        date_of_birth: profile.date_of_birth,
        profession: profile.profession,
        verified: profile.verified,
        avatar_url: profile.avatar.attached? ? url_for(profile.avatar) : "https://i.pravatar.cc/150?u=#{profile.id}",
        address: profile.location&.address
      }
    end
  }
end
end
