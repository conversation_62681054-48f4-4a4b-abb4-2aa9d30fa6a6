class Users::RegistrationsController < Devise::RegistrationsController
  def create
    build_resource(sign_up_params)

    if resource.save
      resource.create_profile(first_name: "#{resource.first_name}", last_name: "#{resource.last_name}")
      super
    else
      super
    end
  end


  private

  def sign_up_params
    params.require(:user).permit(:first_name, :last_name, :email,  :password, :password_confirmation)
  end
end
