# app/controllers/dashboard_controller.rb
class DashboardController < ApplicationController
  before_action :authenticate_user!

  def index
    avatar_url = url_for(current_user.profile.avatar) if current_user&.profile&.avatar&.attached?
    user_data = current_user.as_json(only: [ :id, :email ])

    user_data[:unread_notifications_count] = current_user.notifications.unread.count
    user_data[:notifications] = current_user.notifications
                          .newest_first
                          .limit(10)
                          .map do |n|
      {
        id: n.id,
        message: n.event.message,
        created_at: n.created_at,
        read: n.read?,
        notifiable_id: n.params[:bookmarkable]&.id,
        notifiable_type: n.params[:bookmarkable]&.class&.name,
        actor: {
          id: n.params[:actor]&.id,
          name: n.params[:actor]&.profile&.display_name
        },
        url: n.event.url
      }
    end

    user_data[:skills] = current_user.profile.skill_list
    user_data[:categories] = current_user.profile.category_list
    user_data[:avatar_url] = avatar_url

    user_data[:projects] = current_user.projects.includes([ :location, :profile_image_attachment, :images_attachments ]).map do |project|
      {
        id: project.id,
        name: project.name,
        description: project.description,
        location: project.location,
        profile_image_url: project.profile_image.attached? ? url_for(project.profile_image) : nil,
        gallery_images: project.images.map { |img| url_for(img) }
      }
    end

    user_data[:project_memberships] = current_user.project_memberships.map do |membership|
      {
        id: membership.id,
        name: membership.project.name
      }
    end

    # Check if profile exists
    unless current_user.profile
      current_user.create_profile(
        first_name: current_user.first_name,
        last_name: current_user.last_name
      )
    end

    # Assign the profile data
    user_data[:profile] = current_user.profile.as_json(only: [
      :date_of_birth, :bio, :profession, :first_name, :last_name, :phone, :portfolio, :display_name, :public_status
    ]).merge({
      gallery_images: current_user.profile.gallery_images.map { |img| url_for(img) }, location: current_user.profile.location
    })


    user_data[:forums] = current_user.forums.includes([ :avatar_attachment ]).map do |forum|
      {
        id: forum.id,
        title: forum.title,
        description: forum.description,
        avatar_url: forum.avatar.attached? ? url_for(forum.avatar) : nil
      }
    end
    user_data[:places] = current_user.places.includes([ :avatar_attachment ]).map do |place|
      {
        id: place.id,
        name: place.name,
        description: place.description,
        avatar_url: place.avatar.attached? ? url_for(place.avatar) : nil
      }
    end
    render inertia: "Dashboard/Index", props: { user: user_data }
  end
end
