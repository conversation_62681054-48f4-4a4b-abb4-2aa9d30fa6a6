# app/controllers/ubuntu_controller.rb
class ForumsController < ApplicationController
  include ActionView::Helpers::Date<PERSON>elper
   before_action :authenticate_user!, except: [ :index ]
   def show
    forum = Forum.kept.find(params[:id])
      owner = forum.owner.profile
      avatar_url = forum.avatar.attached? ? url_for(forum.avatar) : nil
      forum_members = User.where(id: forum.forum_memberships.pluck(:user_id))
      track_action "Viewed Forum", track_action_hash
      bookmarked_ids = current_user ?
      current_user.bookmarks.where(bookmarkable_type: "Forum").pluck(:bookmarkable_id) : []

      posts = forum.posts.includes([ :likes,  owner: { profile: :avatar_attachment }, comments: [ :commenter, :likes, replies: [ :commenter, :likes ] ] ])
      post_data = posts.map do |post|
            {
                id: post.id,
                title: post.title,
                content: post.content,
                owner: post.owner,
                avatar: url_for(post.owner.profile.avatar),
                created_ago: time_ago_in_words(post.created_at).gsub(/^about /, "") + " ago",
                likes: post.likes,
                comments: post.comments.map do |comment|
                  {
                    id: comment.id,
                    author: comment.commenter.profile,
                    avatar: url_for(comment.commenter.profile.avatar),
                    content: comment.comment,
                    likes: comment.likes,
                    timeAgo: time_ago_in_words(comment.created_at).gsub(/^about /, "") + " ago",
                    replies: comment.replies.map do |reply|
                      {
                        id: reply.id,
                        content: reply.content,
                        commenter: reply.commenter,
                        timeAgo: time_ago_in_words(reply.created_at).gsub(/^about /, "") + " ago",
                        avatar_url: url_for(reply.commenter.profile.avatar),
                        likes: reply.likes
                      }
                    end
                  }
                end
            }
        end
    render inertia: "UbuntuForum/Index", props: { forumData: {
      owner: "#{owner.first_name} #{owner.last_name}",
      avatar_url: avatar_url,
      forum: forum,
      isMember: current_user ? forum.forum_memberships.exists?(user_id: current_user.id) : false,
      owner_id: forum.owner_id,
      bookmarkedForumIds: bookmarked_ids,
      forum_members: forum_members.map do |member|
          {
            id: member.id,
            name: member.first_name,
            avatar: member.profile.avatar.attached? ? url_for(member.profile.avatar) : nil,
            location: member.profile.location
          }
        end,
      user: current_user ? current_user_with_avatar : {},
      posts: post_data
      },
      user: current_user ? current_user_with_avatar : {}
      }
   end

  def index
  forums = Forum.kept.with_attached_avatar.includes(:categories)
  forums = forums.where(verified: true) if params[:verified] == "true"
  bookmarked_ids = current_user ?
      current_user.bookmarks.where(bookmarkable_type: "Forum").pluck(:bookmarkable_id) : []

  render inertia: "Ubuntu/Index", props: {
  forums: forums.map do |forum|
    forum_members = User.where(id: forum.forum_memberships.pluck(:user_id))
    {
      id: forum.id,
      title: forum.title,
      description: forum.description,
      avatar_url: forum.avatar.attached? ? url_for(forum.avatar) : nil,
      categories: forum.categories,
      views: forum.views,
      forum_members: forum_members.map do |member|
        {
          id: member.id,
          name: member.first_name,
          avatar: member.profile.avatar.attached? ? url_for(member.profile.avatar) : nil
        }
      end
    }
  end,
    bookmarkedForumIds: bookmarked_ids,
    user: current_user_with_avatar
  }
end

  def search
    query = params[:query]
    @forums = Forum.kept.search_all_fields(query).with_attached_avatar
    render inertia: "Ubuntu/Index", props: {
    forums: @forums.map do |forum|
      forum_members = User.where(id: forum.forum_memberships.pluck(:user_id))
        {
          id: forum.id,
          title: forum.title,
          description: forum.description,
          avatar_url: forum.avatar.attached? ? url_for(forum.avatar) : nil,
          forum_members: forum_members.map do |member|
          {
            id: member.id,
            name: member.first_name,
            avatar: member.profile.avatar.attached? ? url_for(member.profile.avatar) : nil
          }
      end
        }
      end,
    query: query,
    user: current_user_with_avatar
    }
  end

  def bookmarked
    if current_user.nil?
      render json: { forums: [] }, status: :ok
      return
    end

    bookmarks = current_user.bookmarks.where(bookmarkable_type: "Forum")
    forums = Forum.kept.where(id: bookmarks.pluck(:bookmarkable_id)).with_attached_avatar

    render json: {
      ubuntu: forums.map do |forum|
        forum_members = User.where(id: forum.forum_memberships.pluck(:user_id))
        {
          id: forum.id,
          title: forum.title,
          description: forum.description,
          avatar_url: forum.avatar.attached? ? url_for(forum.avatar) : nil,
          forum_members: forum_members.map do |member|
          {
            id: member.id,
            name: member.first_name,
            avatar: member.profile.avatar.attached? ? url_for(member.profile.avatar) : nil
          }
        end
        }
      end
    }
  end


  def recommendations
    forums = Forum.kept.where(verified: true)
                  .order(popularity: :desc)
                  .limit(4)
                  .with_attached_avatar

    render json: {
      forums: forums.map do |forum|
        forum_members = User.where(id: forum.forum_memberships.pluck(:user_id))
        {
          id: forum.id,
          title: forum.title,
          description: forum.description,
          # profile_image: forum.profile_image.map { |img| url_for(img) },
          profileImage: forum.avatar.attached? ? url_for(forum.avatar) : nil,
          forum_members: forum_members.map do |member|
          {
            id: member.id,
            name: member.first_name,
            avatar: member.profile.avatar.attached? ? url_for(member.profile.avatar) : nil
          }
        end
        }
      end
    }
  end

  def user_channels
    forums = current_user.forums.kept.with_attached_avatar.includes(:categories)

    render json: {
      forums: forums.map do |forum|
        {
          id: forum.id,
          title: forum.title,
          description: forum.description,
          gallery_images: forum.images.map { |img| url_for(img) },
          profileImage: forum.avatar.attached? ? url_for(forum.avatar) : nil,
          categories: forum.categories,
          members: forum.members
        }
      end
    }
  end

def create
  # Initialize forum with permitted params (excluding collaborators)
  forum = current_user.forums.new(forum_params)
  forum.avatar.attach(params[:forum][:avatar]) if params[:forum][:avatar].present?
  forum.public = true if params[:status] == "public"
  collaborators_ids = params[:forum][:collaborators]
  forum.category_list = params[:forum][:category]

  if forum.save
    # Associate collaborators if provided
    collaborators_ids.reject(&:blank?).each do |collaborator_id|
      user = User.find_by(id: collaborator_id)
      forum.forum_memberships.create(user: user) if user
    end

    redirect_to profile_path, status: :see_other
  else
    # Render form with errors and submitted values
    render inertia: "Forums/New",
      props: {
        errors: forum.errors,
        values: params.slice(:title, :description) # Add other permitted params
      },
      status: :unprocessable_entity
  end
end


  def join
    forum = Forum.kept.find(params[:id])
    ForumMembership.find_or_create_by(user_id: current_user.id, forum_id: forum.id)
    redirect_to forum_path(forum), notice: "You've joined the forum!"
  end

  def leave_forum
    forum = Forum.kept.find(params[:id])
    membership = current_user.forum_memberships.find_by(forum_id: forum.id)

    if membership
      membership.destroy
      redirect_to forum_path(forum), notice: "You have left the forum."
    else
      redirect_to forum_path(forum), alert: "You are not a member of this forum."
    end
  end

  def delete
    forum = Forum.kept.find(params[:id])

    if forum.owner == current_user || current_user.admin?
      forum.discard
      redirect_to forums_path, notice: "Forum marked as deleted."
    else
      redirect_to forum_path(forum), alert: "You are not authorized to delete this forum."
    end
  end

private

def forum_params
  params.require(:forum).permit(:title, :description)
end
def track_action_hash
      {
        user: current_user ? "#{current_user.first_name} #{current_user.last_name}" : "Guest",
        time: Time.now
      }
  end
end
