class LikesController < ApplicationController
  before_action :authenticate_user!

    def create
        @likeable = find_likeable
        return render json: { error: "Likeable not found" }, status: :not_found unless @likeable

        user = current_user
        @like = @likeable.likes.find_by(user: user)

        if @like
            # Unlike
            @like.destroy
        else
            # Like
            @like = @likeable.likes.build(user: user)
            @like.save
        end

        # Return total likes count
        total_likes = @likeable.likes.count

        render json: { total_likes: total_likes }
    end

    def post_likes
        @likeable = find_likeable
        total_likes = @likeable.likes.count

        render json: { total_likes: total_likes }
    end

  def destroy
    @likeable = find_likeable
    @like = @likeable.likes.find_by(user: current_user)

    if @like&.destroy
      redirect_back fallback_location: root_path, notice: "Unliked!"
    else
      redirect_back fallback_location: root_path, alert: "Unable to unlike."
    end
  end

  private

  def find_likeable
    params.each do |name, value|
      if name =~ /(.+)_id$/
        return $1.classify.constantize.find(value)
      end
    end
    nil
  end
end
