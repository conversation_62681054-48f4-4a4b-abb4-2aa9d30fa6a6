class HomeController < ApplicationController
  def index
    forums = Forum.with_attached_avatar
    forums = forums.where(verified: true) if params[:verified] == "true"

    bookmarked_ids = current_user ?
      current_user.bookmarks.where(bookmarkable_type: "Forum").pluck(:bookmarkable_id) : []

    render inertia: "Home/Index", props: {
      user: current_user_with_avatar,
      forums: forums.map do |forum|
      {
        id: forum.id,
        title: forum.title,
        description: forum.description,
        # profile_image: forum.profile_image.map { |img| url_for(img) },
        avatar_url: forum.avatar.attached? ? url_for(forum.avatar) : nil
      }
    end,
    bookmarkedForumIds: bookmarked_ids
    }
     track_action "Viewed Landing Page", track_action_hash
  end

  private

  def track_action_hash
      {
        visitor: "Guest",
        time: Time.now
      }
  end
end
