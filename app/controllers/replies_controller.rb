# app/controllers/replies_controller.rb
class RepliesController < ApplicationController
  before_action :set_comment

  def index
    @replies = @comment.replies
  end

  def create
    @reply = @comment.replies.build(reply_params)
    @reply.commenter = current_user
    if @reply.save
    else
    end
  end

  private

  def set_comment
    @comment = Comment.find(params[:comment_id])
  end

  def reply_params
    params.require(:reply).permit(:content)
  end
end
