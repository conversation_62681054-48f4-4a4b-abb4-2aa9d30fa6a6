class AdminController < ApplicationController
  before_action :authenticate_user!

  def index
    authorize :admin, :panel?

    users = User.includes(:roles).select(:id, :email, :first_name, :last_name)
    users_data = users.map do |user|
      {
        id: user.id,
        first_name: user.first_name,
        last_name: user.last_name,
        email: user.email,
        roles: user.roles.map(&:name)
      }
    end

    render inertia: "Admin", props: {
      user: current_user.as_json(only: [ :id, :email ]),
      users: users_data
    }
  end

  def users
    users = User.includes(:roles).select(:id, :email, :first_name, :last_name)
    users_data = users.map do |user|
      {
        id: user.id,
        first_name: user.first_name,
        last_name: user.last_name,
        email: user.email,
        roles: user.roles.map(&:name)
      }
    end

    render json: { users: users_data }
  end

  def remove_role
    authorize :admin, :panel?

    user = User.find(params[:id])
    if user.roles.count <= 1
      render json: { error: "Cannot remove user default role (user must have atleast one role)." }, status: :forbidden
    elsif !params[:role].eql?("regular_user")
      user.remove_role(params[:role])
      render json: { success: true }
    else
      render json: { error: "User does not have this role" }, status: :forbidden
    end
  end

  def add_role
    authorize :admin, :panel?
      user = User.find(params[:id])
    if user.has_role?(params[:role])
      render json: { error: "User already has this role" }, status: :forbidden
    else
      user.add_role(params[:role])
      render json: { success: true }
    end
  end
end
