# app/controllers/notifications_controller.rb
class NotificationsController < ApplicationController
  before_action :authenticate_user!
  protect_from_forgery with: :exception

  def index
    notifications = current_user.notifications
                      .includes(:actor, :notifiable)
                      .order(created_at: :desc)
    render json: notifications
  end

  def mark_as_read
    notification = current_user.notifications.find(params[:id])
    notification.mark_as_read
    render json: { unread_count: current_user.notifications.unread.count }
  end
end
