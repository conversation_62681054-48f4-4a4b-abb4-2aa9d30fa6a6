# app/controllers/profile_controller.rb
class ProfileController < ApplicationController
  before_action :authenticate_user!, only: [ :update_avatar ]

  def update_avatar
    profile = current_user.profile || current_user.create_profile

    if params[:profile][:avatar].present?
      profile.avatar.attach(params[:profile][:avatar])
    end

    # Assign user info
    profile.first_name = current_user.first_name
    profile.last_name  = current_user.last_name

    # Update other profile attributes
    profile_params_filtered = profile_params.except(:skills, :domain, :location, :gallery_images)

    ActiveRecord::Base.transaction do
      profile.update!(profile_params_filtered)

      # Assign tags/lists
      profile.skill_list    = profile_params[:skills] if profile_params[:skills]
      profile.category_list = profile_params[:domain] if profile_params[:domain]
      # Append new gallery images
      if profile_params[:gallery_images]
        profile.gallery_images.attach(profile_params[:gallery_images])
      end

      profile.save!

      # Handle location
      if profile_params[:location]
        if profile.location
          profile.location.update!(address: params[:profile][:location])
        else
          profile.build_location(address: params[:profile][:location])
          profile.save!
        end
      end
    end

    redirect_to profile_path
  rescue ActiveRecord::RecordInvalid => e
    render inertia: "Dashboard", props: { success: false, message: e.record.errors.full_messages.join(", ") }
  end

  def show
    user    = User.find(params[:id])
    profile = user.profile

    bookmarked_ids = current_user ?
      current_user.bookmarks.where(bookmarkable_type: "Profile").pluck(:bookmarkable_id) : []
    track_action "Viewed User Profile", track_action_hash
    render inertia: "Profile/Index", props: {
      userData: {
      projects: user.projects.map do |project|
        {
          id: project.id,
          title: project.name,
          description: project.description,
          image: project.images.map do |img|
            {
              im:  url_for(img)
            }
          end
        }
      end,
      places: user.places.with_attached_avatar.map do |place|
        {
          id: place.id,
          name: place.name,
          image: place.avatar.attached? ? url_for(place.avatar) : nil
        }
      end,
      forums: user.forums.with_attached_avatar.with_attached_images.map do |forum|
        {
          id: forum.id,
          title: forum.title,
          image: if forum.avatar.attached?
                    url_for(forum.avatar)
                 elsif forum.images.attached? && forum.images.first.present?
                    url_for(forum.images.first)
                 else
                    nil
                 end

        }
      end,
      id: profile.id,
      user_id: profile.user_id,
      name: profile.display_name,
      bio: profile.bio,
      date_of_birth: profile.date_of_birth,
      profession: profile.profession,
      verified: profile.verified,
      avatar_url: profile.avatar.attached? ? url_for(profile.avatar) : "https://i.pravatar.cc/150?u=#{profile.id}",
      skills: profile.skill_list,
      avatar: profile.avatar.attached? ? url_for(profile.avatar) : nil,
      professionalFocus: profile.category_list,
      address: profile.location&.address,
      gallery_images: profile.gallery_images.attached? ? profile.gallery_images.map { |img| url_for(img) } : []

      },
      bookmarkedProfileIds: bookmarked_ids,
      user: current_user_with_avatar
    }

  rescue ActiveRecord::RecordNotFound
    redirect_to root_path, alert: "Profile not found"
  end

  private

  def profile_params
    params.require(:profile).permit(
      :public_status,
      :profession,
      :phone,
      :portfolio,
      :display_name,
      :bio,
      :location,
      domain:   [],
      skills:   [],
      gallery_images: []
    )
  end

    def track_action_hash
      {
        user: current_user&.display_name || "Guest",
        time: Time.now
      }
    end
end
