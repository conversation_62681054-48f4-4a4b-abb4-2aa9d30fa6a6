class JoinProjectRequestController < ApplicationController
   def create
  project_membership = ProjectMembership.new(project_id: params["join_project_request"]["project_id"], user_id: current_user.id)
  project = Project.find(params["join_project_request"]["project_id"])
  if project_membership.save
    JoinProjectNotifier.with(record: project).deliver(project.owner)

    redirect_to request.referrer, status: :see_other, notice: "You've joined the forum!"
    # redirect_to forum_path, status: :see_other
  else
    # Failure: Render form with errors and submitted values
    redirect_to request.referrer,
      status: :unprocessable_entity
  end
end
end
