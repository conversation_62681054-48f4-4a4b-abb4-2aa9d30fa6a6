# app/controllers/places_controller.rb
class PlacesController < ApplicationController
  def index
    places = Place.with_attached_avatar.includes([ :tags, :gallery_images_attachments, :location ])
    places = places.where(verified: true) if params[:verified] == "true"

    bookmarked_ids = current_user ?
      current_user.bookmarks.where(bookmarkable_type: "Place").pluck(:bookmarkable_id) : []

    render inertia: "Places/Index", props: {
      places: places.map do |place|
        {
          id: place.id,
          name: place.name,
          description: place.description,
          avatar_url: place.avatar.attached? ? url_for(place.avatar) : nil,
          views: place.views,
          images: place.gallery_images.attached? ? place.gallery_images.map { |img| url_for(img) } : [],
          location: place.location,
          tags: place.tags.map(&:name)
        }
      end,
      bookmarkedPlaceIds: bookmarked_ids,
      user: current_user_with_avatar
    }
  end

  def placesingleshow
    place = Place.includes(:location, :taggings, place_memberships: { user: { profile: { avatar_attachment: :blob } } }).find(params[:id])
    bookmarked_ids = current_user ?
      current_user.bookmarks.where(bookmarkable_type: "Place").pluck(:bookmarkable_id) : []
    track_action "Viewed Place", track_action_hash
    render inertia: "Placepublic/Index", props: {
      pageData: {
        theme: "places",
        id: place.id,
        title: place.name,
        description: place.description,
        location: place.location.address,
        images: place.gallery_images.attached? ? place.gallery_images.map { |img| url_for(img) } : [],
        profileImage: place.avatar.attached? ? url_for(place.avatar) : nil,
        tags: place.tags.map(&:name),
        what_we_do: place.service_list,
        collaborators: place.place_memberships.map do |membership|
          user = membership.user
            {
              id: user.id,
              name: "#{user.first_name} #{user.last_name}",
              image: if user.profile&.avatar&.attached?
                      url_for(user.profile.avatar)
                     else
                      "https://i.pravatar.cc/150?u=#{user.id}"
                     end

            }
          end,
        owner: User.find(place.owner_id),
        ownerId: place.owner_id
      },
      bookmarkedPlaceIds: bookmarked_ids,
      user: current_user_with_avatar
    }
  end

  def locations_recomentation
    places = Place.with_attached_avatar.where_not(owner_id: current_user.id).limit(8)
    places = places.where(verified: true) if params[:verified] == "true"

    render json: {
      places: places.map do |place|
        {
          id: place.id,
          name: place.name,
          description: place.description,
          avatar_url: place.avatar.attached? ? url_for(place.avatar) : nil
        }
      end
    }
  end

  def search
    query = params[:query]
    @places = Place.search_all_fields(query).with_attached_avatar
    render inertia: "Places/Index", props: {
    places: @places.map do |place|
        {
          id: place.id,
          name: place.name,
          description: place.description
          # avatar_url: place.avatar.attached? ? url_for(place.avatar) : nil
        }
      end,
    query: query,
    user: current_user_with_avatar
    }
  end

  def user_places
  places = current_user.places.includes(:location, gallery_images_attachments: :blob).with_attached_avatar

  render json: {
    places: places.map { |place|
      {
        id: place.id,
        name: place.name,
        description: place.description,
        avatar_url: place.avatar.attached? ? url_for(place.avatar) : nil,
        location: place.location,
        images: place.gallery_images.attached? ? place.gallery_images.map { |img| url_for(img) } : [],
        tags: place.tags.map(&:name),
        associates: []
      }
    }
  }
  end


def create
  @place = current_user.places.new(place_params)
  if params[:place][:avatar]
    @place.avatar.attach(params[:place][:avatar])
  end

  # Attach gallery images
  if params[:place][:gallery_images].present?
    params[:place][:gallery_images].each do |image|
      @place.gallery_images.attach(image)
    end
  end

  # Add location
  if params[:place][:location].present?
    if @place.location.present?
      @place.location.update(address: params[:place][:location])
    else
      @place.build_location(address: params[:place][:location])
    end
  end


  # Add tags
  @place.tag_list = params[:place][:tags]
  @place.service_list = params[:place][:what_we_do]
  if @place.save
    redirect_to profile_path
  else
    Rails.logger.error(@place.errors.full_messages)
    render inertia: "Dashboard/Index", props: {
      success: false,
      errors: @place.errors.full_messages
    }
  end
end

  def bookmarked
  if current_user.nil?
    render json: { places: [] }, status: :ok
    return
  end

  bookmarks = current_user.bookmarks.where(bookmarkable_type: "Place")
  places = Place.where(id: bookmarks.pluck(:bookmarkable_id)).with_attached_avatar

  render json: {
    places: places.map do |place|
      {
        id: place.id,
        name: place.name,
        description: place.description,
        avatar_url: place.avatar.attached? ? url_for(place.avatar) : nil
      }
    end
  }
  end


private

def place_params
  params.require(:place).permit(:name, :description,)
end


  def track_action_hash
      {
        user: current_user&.display_name || "Guest",
        time: Time.now
      }
  end
end
