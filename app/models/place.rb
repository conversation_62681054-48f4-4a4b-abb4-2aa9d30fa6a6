# app/models/place.rb
class Place < ApplicationRecord
  include PgSearch::Model

  pg_search_scope :search_all_fields,
    against: [ :name, :description ],
    using: {
      tsearch: {
        prefix: true,
        any_word: true
      }
    }

  has_many :place_memberships, dependent: :destroy
  has_many :members, through: :place_memberships, source: :user
  has_many :categories
  has_many :focus_areas
  acts_as_taggable_on :categories, :services
  has_many :comments, as: :commentable
  has_many_attached :gallery_images
  acts_as_taggable_on :tags
  has_one :location, as: :locatable

  # Associations
  belongs_to :owner, class_name: "User"
  has_one_attached :avatar
  has_many :bookmarks, as: :bookmarkable, dependent: :destroy

  validates :name, presence: true
  validates :description, presence: true
end
