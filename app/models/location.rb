class Location < ApplicationRecord
  include PgSearch::Model
  belongs_to :project, optional: true
  has_one_attached :avatar

  belongs_to :locatable, polymorphic: true

  has_many :place_memberships
  has_many :users, through: :place_memberships
  has_many_attached :images
  pg_search_scope :search_all_fields,
    against: [ :location, :tags, :description, :name ],
  using: {
    tsearch: {
      prefix: true,
      any_word: true
    }
  }
end
