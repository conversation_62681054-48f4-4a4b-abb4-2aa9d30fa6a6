class Ahoy::Event < ApplicationRecord
  after_create :update_views_count

  include Ahoy::QueryMethods

  self.table_name = "ahoy_events"
  self.implicit_order_column = "time"

  belongs_to :visit
  belongs_to :user, optional: true

  private
  def update_views_count
    if self.name == "Viewed User Profile"
      profile = User.find(self.properties["id"]).profile
      profile.views = profile.views + 1
      profile.save
    elsif self.name == "Viewed Project"
      project = Project.find(self.properties["id"])
      project.views = project.views + 1
      project.save
    elsif self.name == "Viewed Place"
      place = Place.find(self.properties["id"])
      place.views = place.views + 1
      place.save
    elsif self.name == "Viewed Forum"
      forum = Forum.find(self.properties["id"])
      forum.views = forum.views + 1
      forum.save
    else
    end
  end
end
