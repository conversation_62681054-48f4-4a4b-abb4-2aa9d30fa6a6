class Rating < ApplicationRecord
  # Associations
  belongs_to :user  # User who created the rating
  belongs_to :rateable, polymorphic: true  # What is being rated (User or Place)

  # Validations
  validates :score, presence: true,
                    numericality: { only_integer: true,
                                    greater_than_or_equal_to: 1,
                                    less_than_or_equal_to: 5 }
  # Ensure a user can only rate something once
  validates :user_id, uniqueness: { scope: [ :rateable_type, :rateable_id ],
                                   message: "has already rated this item" }

  # Prevent users from rating themselves
  validate :cannot_rate_self

  private

  def cannot_rate_self
    if user_id == rateable_id && rateable_type == "User"
      errors.add(:base, "You cannot rate yourself")
    end
  end
end
