class Profile < ApplicationRecord
  include PgSearch::Model
  belongs_to :user
  has_one_attached :avatar
  acts_as_taggable_on :skills, :categories
  has_one :location, as: :locatable
  has_many :bookmarks, as: :bookmarkable, dependent: :destroy
  has_many_attached :gallery_images

  # Full-text search across User and associated models.
  pg_search_scope :search_all_fields,
  against: [ :first_name, :last_name, :bio, :profession ],
  associated_against: {
      location: [ :address ]
  },
  using: {
    tsearch: {
      prefix: true,
      any_word: true
    }
  }

  # Then define a custom scope for tags
  scope :with_skill, ->(skill_name) {
    joins(:user).where(
      User.arel_table[:id].in(
        User.tagged_with(skill_name, on: :skills).select(:id)
      )
    )
  }

  def display_name
    "#{first_name} #{last_name}"
  end
end
