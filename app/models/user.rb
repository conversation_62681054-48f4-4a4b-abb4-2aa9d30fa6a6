# app/models/user.rb
class User < ApplicationRecord
  include Rateable
  rolify
  has_many :place_memberships
  has_many :projects, class_name: "Project", foreign_key: "owner_id", dependent: :destroy
  has_many :places, class_name: "Place", foreign_key: "owner_id", dependent: :destroy
  has_many :place_memberships, dependent: :destroy
  has_many :comments, class_name: "Comment", foreign_key: "commenter_id", dependent: :destroy
  has_many :project_memberships, dependent: :destroy
  has_many :member_projects, through: :project_memberships, source: :project
  has_one  :profile, dependent: :destroy
  has_many :given_ratings, class_name: "Rating", foreign_key: "user_id"
  has_many :categories
  has_many :contacts, as: :contactable
  has_many :bookmarks, dependent: :destroy
  has_many :posts, class_name: "Post", foreign_key: "owner_id", dependent: :destroy
  has_many :likes
  has_many :liked_posts, through: :likes, source: :likeable, source_type: "Post"


  has_many :notifications, as: :recipient, class_name: "Noticed::Notification", dependent: :destroy
  has_many :forums, class_name: "Forum", foreign_key: "owner_id", dependent: :destroy
  has_many :forum_memberships, dependent: :destroy

  delegate :display_name, to: :profile, allow_nil: true
  acts_as_voter


  # Include default devise modules. Others available are:
  # :confirmable, :lockable, :timeoutable, :trackable and :
  validates :email, :first_name, :last_name, presence: true

  devise :invitable, :database_authenticatable, :registerable,
         :recoverable, :rememberable, :validatable,
         :confirmable, :trackable,
         :omniauthable, omniauth_providers: [ :facebook, :google_oauth2 ]

  def self.from_omniauth(auth)
    where(provider: auth.provider, uid: auth.uid).first_or_create do |user|
      user.email = auth.info.email
      user.password = Devise.friendly_token[0, 20]
      user.first_name = auth.info.name.split(" ").first
      user.last_name = auth.info.name.split(" ").last
    end
  end

  # Rate another user or item
  def rate(rateable, score, comment = nil)
    given_ratings.create(
      rateable: rateable,
      score: score,
      comment: comment
    )
  end

  # Check if user has already rated something
  def has_rated?(rateable)
    given_ratings.exists?(rateable: rateable)
  end

  # Update existing rating
  def update_rating(rateable, score, comment = nil)
    rating = given_ratings.find_by(rateable: rateable)
    rating.update(score: score, comment: comment) if rating
  end
end
