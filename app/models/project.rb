# app/models/project.rb
class Project < ApplicationRecord
  include Rateable
  include PgSearch::Model
  has_many :categories
  has_many :focus_areas
  has_many :portfolios
  # Owner association
  belongs_to :owner, class_name: "User"

  # Member association
  has_many :project_memberships, dependent: :destroy
  has_many :members, through: :project_memberships, source: :user
  has_many :contacts, as: :contactable
  has_many_attached :images
  has_one_attached :profile_image
  acts_as_taggable_on :categories, :services
  has_one :location, as: :locatable
  has_many :bookmarks, as: :bookmarkable, dependent: :destroy

  pg_search_scope :search_all_fields,
  against: [ :name, :description ],
  associated_against: {
      location: [ :address ]
    },
  using: {
    tsearch: {
      prefix: true
    }
  }

   def has_member?(user)
     members.exists?(user.id)
   end
end
