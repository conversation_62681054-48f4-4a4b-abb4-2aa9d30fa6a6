# app/models/forum.rb
class Forum < ApplicationRecord
  include PgSearch::Model
  include Discard::Model
  belongs_to :owner, class_name: "User"
  has_one_attached :avatar
  has_many :comments, as: :commentable
  has_many :bookmarks, as: :bookmarkable, dependent: :destroy
  has_many :posts
  acts_as_taggable_on :categories
  has_many :forum_memberships, dependent: :destroy
  has_many :members, through: :forum_memberships, source: :user

  # serialize :categories, Array

  validates :title, :description, presence: true


  has_many_attached :images
  pg_search_scope :search_all_fields,
    against: [ :title, :description ],
  using: {
    tsearch: {
      prefix: true,
      any_word: true
    }
  }
end
