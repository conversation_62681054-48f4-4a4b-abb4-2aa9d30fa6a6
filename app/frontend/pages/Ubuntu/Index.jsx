import Layout from '../../components/shared/Layout'
import Banner from '../../components/shared/Banner'
import UbuntuCardsSection from '../../components/pageSpecific/ubuntu/UbuntuCardsSection';
import DiscussionForum from '../../components/pageSpecific/ubuntu/DiscussionForum';
import placesImg1 from '/assets/places_carousel/places1.png';
import placesImg2 from '/assets/places_carousel/places2.png';
import placesImg3 from '/assets/places_carousel/places3.png';
import placesImg4 from '/assets/places_carousel/places4.png';
import placesFrame from '/assets/ubuntu_assets/frame.png';
import Search from '../../components/shared/Search';

const placesImages = [placesImg1, placesImg2, placesImg3, placesImg4];

const Ubuntu = ({ forums }) => {
 
    return (
        <>
            <Banner
                bgColor="bg-primarymaroon"
                title="Ubuntu"
                description="I am because you are!
                Conventional approaches have often not led to the desired results, so it needs  visions of future scenarios and spaces to develop and prototype them.  Engage with our unconventional community of artists, co-creators and  living labs, build networks, collaborate, discuss and share food for  thought - join an existing forum or start your own!"
                images={placesImages}
                frame={placesFrame}
                fadeDuration={500}
                imageInterval={3000}
            />
            <Search
                color="maroon"
                defaultPlaceholder="Search for discussions on design, development, art, music, tech, and more..."
                mostSearchedItems={[
                    'UI/UX Design',
                    'Education',
                    'Visual Art',
                    'Performing Art',
                    'Video & Film',
                    'Music',
                    'Craft',
                    'Technology',
                    'Journalism',
                ]}
                filterOptions={{
                    'Category': [
                        'Announcements', 'General Discussion', 'Partnerships',
                        'Critiques', 'Collaboration', 'Feedback',
                        'Resources', 'Events', 'Opportunities',
                        'Project Showcases', 'Mentorship', 'Innovation',
                        'Networking', 'Technology', 'Creative Processes',
                        'Community Initiatives', 'Urban Development', 'Sustainability',
                        'Climate Action',
                    ],
                    'Skill Tags': [
                        'Activism', 'Architecture', 'Climate Action',
                        'Community Building', 'Content Creation', 'Crafts',
                        'Digital Marketing', 'Education', 'Event Management',
                        'Facilitation', 'Filmmaking', 'Gaming',
                        'Graphic Design', 'Music Production', 'Photography',
                        'Project Management', 'Social Entrepreneurship', 'Storytelling',
                        'Training', 'UX/UI Design', 'Writing'
                    ]
                }}
            />
            <UbuntuCardsSection forums={forums} />
            {/* <DiscussionForum /> */}
        </>
    )
}

Ubuntu.layout = (page) => <Layout children={page} title="Ubuntu Page" userData={page.props.user} />

export default Ubuntu
