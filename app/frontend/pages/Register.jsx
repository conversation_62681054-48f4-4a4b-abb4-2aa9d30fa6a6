// app/frontend/pages/Register.jsx
import React, { useState } from 'react';
import { useForm } from '@inertiajs/react';

export default function Register() {
  const { data, setData, post, errors } = useForm({
    email: '',
    password: '',
    password_confirmation: '',
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    post('/users');
  };

  return (
    <form onSubmit={handleSubmit}>
      <h1>Sign Up</h1>
      <input type="email" value={data.email} onChange={e => setData('email', e.target.value)} placeholder="Email" />
      <input type="password" value={data.password} onChange={e => setData('password', e.target.value)} placeholder="Password" />
      <input type="password" value={data.password_confirmation} onChange={e => setData('password_confirmation', e.target.value)} placeholder="Confirm Password" />
      {errors.email && <div>{errors.email}</div>}
      {errors.password && <div>{errors.password}</div>}
      <button type="submit">Register</button>
    </form>
  );
}
