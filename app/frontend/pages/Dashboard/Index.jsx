// app/frontend/pages/Dashboard/Index.jsx
import { useState, useEffect } from 'react';
import { Inertia } from "@inertiajs/inertia";
import { toast } from 'react-hot-toast'
import Layout from '../../components/shared/Layout';
import DashboardSidebar from '../../components/pageSpecific/dashboard/DashboardSidebar';
import ProfileUpdate from '../../components/pageSpecific/dashboard/ProfileUpdate';
import PeopleSection from '../../components/pageSpecific/dashboard/PeopleSection';
import ProjectsSection from '../../components/pageSpecific/dashboard/ProjectsSection';
import PlacesSection from '../../components/pageSpecific/dashboard/PlacesSection';
import NewsAlertsSection from '../../components/pageSpecific/dashboard/NewsAlertsSection';
import UbuntuSection from '../../components/pageSpecific/dashboard/UbuntuSection';
import MessagesSection from '../../components/pageSpecific/dashboard/MessagesSection';
import ChatSidebar from '../../components/pageSpecific/dashboard/ChatSidebar';
import ChatView from '../../components/pageSpecific/dashboard/ChatView';
import SettingsSection from '../../components/pageSpecific/dashboard/SettingsSection';

// Main Dashboard Component
const Dashboard = ({ user }) => {
  const profile = user.profile || {};
  const [avatar, setAvatar] = useState(null);
  const [bio, setBio] = useState(profile.bio || '');
  const [location, setLocation] = useState(profile.location || '');
  const [domain, setDomain] = useState(profile.domain || []);
  const [previewUrl, setPreviewUrl] = useState(null);
  const [phone, setPhone] = useState(profile.phone || '');
  const [skills, setSkills] = useState(user.skills || []);
  const [message, setMessage] = useState("");
  const [displayName, setDisplayName] = useState(profile.display_name || '');
  const [publicStatus, setPublicStatus] = useState(user.profile.public_status || true);
  const [portfolio, setPortfolio] = useState(profile.portfolio || '')
  const [profession, setProfession] = useState(profile.profession || '')
  const [galleryImages, setGalleryImages] = useState(profile.gallery_images)

  // State for active section and chat
  const [activeSection, setActiveSection] = useState("profile");
  const [view, setView] = useState(""); // e.g., "create-project", "create-place", etc.
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [currentChatId, setCurrentChatId] = useState(null);
  const [unreadCount, setUnreadCount] = useState(user.unread_notifications_count || 0);

  const handleNotificationRead = (newCount) => {
    setUnreadCount(newCount);
  };

  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const section = urlParams.get('section');
    const viewParam = urlParams.get('view');
    const chatId = urlParams.get('chat_id');

    if (section) {
      setActiveSection(section);
      if (section === "messages" && chatId) {
        setIsChatOpen(true);
        setCurrentChatId(parseInt(chatId));
      }
    }

    setView(viewParam || "");
  }, [window.location.search]);

  // Update URL when section changes (optional - for browser history)
  const handleSectionChange = (section) => {
    setActiveSection(section);

    // Update URL without page reload
    const params = new URLSearchParams(window.location.search);
    params.set('section', section);

    params.delete('view');

    // Remove chat-specific params if not in messages section
    if (section !== 'messages') {
      params.delete('chat_id');
      setIsChatOpen(false);
      setCurrentChatId(null);
    }

    const newUrl = `${window.location.pathname}?${params.toString()}`;
    window.history.replaceState({}, '', newUrl);
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    setAvatar(file);
    setPreviewUrl(file ? URL.createObjectURL(file) : null);
  };

  const handleRemoveImage = () => {
    setPreviewUrl(null);
    setAvatar(null);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    const formData = new FormData();
    if (avatar) formData.append('profile[avatar]', avatar);
    formData.append('profile[bio]', bio);
    formData.append('profile[location]', location);
    formData.append('profile[phone]', phone);
    formData.append('profile[portfolio]', portfolio);
    formData.append('profile[display_name]', displayName);
    formData.append('profile[profession]', profession)
    formData.append('profile[public_status]', publicStatus);
    galleryImages.forEach((img) => {
      if (img instanceof File) {
        formData.append('profile[gallery_images][]', img);
      }
    });
    domain.forEach((item) => formData.append('profile[domain][]', item));
    skills.forEach((item) => formData.append('profile[skills][]', item));
    Inertia.post('/profile/update', formData, {
      forceFormData: true,
      preserveScroll: true,
      onSuccess: (
        toast.success("Profile updated successfully!")
      )
    });
  };

  const handleOpenChat = (chatId) => {
    setActiveSection("messages");
    setIsChatOpen(true);
    setCurrentChatId(chatId);

    // Update URL to include chat_id
    const params = new URLSearchParams(window.location.search);
    params.set('section', 'messages');
    params.set('chat_id', chatId);
    const newUrl = `${window.location.pathname}?${params.toString()}`;
    window.history.replaceState({}, '', newUrl);
  };

  const handleCloseChat = () => {
    setIsChatOpen(false);
    setCurrentChatId(null);

    // Remove chat_id from URL
    const params = new URLSearchParams(window.location.search);
    params.delete('chat_id');
    const newUrl = `${window.location.pathname}?${params.toString()}`;
    window.history.replaceState({}, '', newUrl);
  };

  const dispalymessage = (message) => {
    return (
      <div>{message}</div>
    )
  }

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [activeSection, isChatOpen]);

  return (
    <div className="relative flex min-h-screen font-inter px-6 2xl:px-20 bg-white pt-24">
      {/* LEFT PANEL */}
      {/* Conditionally render DashboardSidebar or ChatSidebar */}
      {isChatOpen ? (
        <ChatSidebar
          user={user}
          currentChatId={currentChatId}
          onCloseChat={handleCloseChat}
          onOpenChat={handleOpenChat}
        />
      ) : (
        <DashboardSidebar
          user={{
          ...user,
          unread_notifications_count: unreadCount // Pass the updated count
        }}
          activeSection={activeSection}
          setActiveSection={handleSectionChange} // Use the new handler
        />
      )}

      {/* RIGHT PANEL */}
      <div className="p-4 right overflow-x-hidden w-full m-4 bg-gray-100 border-gray-200 border-2 rounded-xl shadow-lg">
        {message && (
          <div className="fixed top-15 left-1/2 transform -translate-x-1/2 z-50 bg-green-500 text-white p-4 shadow rounded">
            {dispalymessage(message)}
          </div>
        )}
        {activeSection === "profile" && (
          <ProfileUpdate
            user={user}
            bio={bio}
            location={location}
            avatar={avatar}
            previewUrl={previewUrl}
            handleFileChange={handleFileChange}
            handleRemoveImage={handleRemoveImage}
            handleSubmit={handleSubmit}
            setBio={setBio}
            setLocation={setLocation}
            domain={domain}
            setDomain={setDomain}
            phone={phone}
            setPhone={setPhone}
            skills={skills}
            setSkills={setSkills}
            portfolio={portfolio}
            publicStatus={publicStatus}
            displayName={displayName}
            setPortfolio={setPortfolio}
            setPublicStatus={setPublicStatus}
            setDisplayName={setDisplayName}
            profession={profession}
            setProfession={setProfession}
            galleryImages={galleryImages}
            setGalleryImages={setGalleryImages}
          />
        )}

        {activeSection === "projects" && <ProjectsSection user={user} />}
        {activeSection === "people" && <PeopleSection />}
        {activeSection === "places" && <PlacesSection />}
        {activeSection === "ubuntu" && <UbuntuSection />}
        {activeSection === "newsfeed" && (
          <NewsAlertsSection 
            notifications={user.notifications || []}
            onNotificationRead={handleNotificationRead}
          />
          )}
        {activeSection === "messages" && !isChatOpen && (
          <MessagesSection
            user={user}
            onOpenChat={handleOpenChat}
            isChatOpen={isChatOpen}
            currentChatId={currentChatId}
          />
        )}
        {activeSection === "messages" && isChatOpen && (
          <ChatView
            currentChatId={currentChatId}
            user={user}
          />
        )}
        {activeSection === "settings" && <SettingsSection />}
      </div>
    </div>
  );
};

Dashboard.layout = (page) => <Layout children={page} title="Dashboard" userData={page.props.user} />

export default Dashboard;