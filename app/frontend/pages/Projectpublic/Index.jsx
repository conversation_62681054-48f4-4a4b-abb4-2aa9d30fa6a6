import React from 'react';
import Layout from '../../components/shared/Layout';
import ProfilePage from '../../components/pageSpecific/places&projects/Base';
// Main Profile Page Component
const ProjectPublicPage = ({ project, user, bookmarkedProjectIds }) => {
  console.log(project)
  const Data = {
    theme: 'projects',
    profile: {
      id: project.id,
      title: project.name,
      description: project.description,
      location: project.location?.address || 'Location not specified',
      rating: 4.3, // TODO: Add rating to project data
      profileImage: project.images[0], // Using first image as profile image
      contributors: project.collaborators.map(collaborator => collaborator.image),
      owner: project.owner,
      ownerId: project.ownerId,
      ismember: project.is_member
    },
    categories: project.tags.map(tag => ({
      name: tag,
      icon: '🏷️'
    })) || [],
    activities: project.services.map(tag => ({
      name: tag
    })) || [],
    gallery: project.images || [],
    bookmarkedProfileIds: bookmarkedProjectIds
  };
  
  return (
    <>
      <ProfilePage pageData={Data} user={user} />
    </>
  );
};

ProjectPublicPage.layout = (page) => <Layout children={page} title="Project Details" userData={page.props.user} />;

export default ProjectPublicPage;