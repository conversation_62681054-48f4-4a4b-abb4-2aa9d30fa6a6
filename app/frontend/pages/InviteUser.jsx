import React, { useState } from "react";
import { Inertia } from '@inertiajs/inertia';

export default function InviteUserModal({ isOpen, onClose }) {
    const [email, setEmail] = useState("");

    function handleSubmit(e) {
        e.preventDefault();
        Inertia.post('/users/invitation', { user: { email } }, {
            onFinish: () => {
                alert("Invite sent");
                onClose()
            },
            onError: () => alert('Error inviting user'),
        });
      }

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
            <div className="bg-white p-6 rounded shadow max-w-md w-full">
                <h2 className="text-xl mb-4">Invite User</h2>
                <form onSubmit={handleSubmit}>
                    <input
                        type="email"
                        placeholder="User email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        required
                        className="border p-2 mb-4 w-full"
                    />
                    <button
                        type="submit"
                        className="bg-blue-600 text-white px-4 py-2 rounded"
                    >
                        Send Invite
                    </button>
                    <button
                        type="button"
                        className="ml-4 text-gray-500 hover:text-gray-800"
                        onClick={onClose}
                    >
                        Cancel
                    </button>
                </form>
            </div>
        </div>
    );
}
