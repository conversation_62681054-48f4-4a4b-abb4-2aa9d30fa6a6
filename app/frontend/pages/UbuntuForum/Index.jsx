import React, { useEffect, useState } from 'react';
import Layout from '../../components/shared/Layout';
import Toggle from '../../components/shared/Toggle'
import Posts from '../../components/pageSpecific/ubuntu/Posts';
import Discussions from '../../components/pageSpecific/ubuntu/Discussions';
import Resources from '../../components/pageSpecific/ubuntu/Resources';
import Collaborators from '../../components/pageSpecific/ubuntu/Collaborators';
import { Search, ChevronDown, Heart, MessageCircle, Plus, User, MapPin, ArrowUpRight, ThumbsUp } from 'lucide-react';
import ProfileImage from '/assets/profile3.jpg';
import { router } from '@inertiajs/react';
import { Inertia } from '@inertiajs/inertia';

const ForumPage = (forumData, user) => {
    const [activeTab, setActiveTab] = useState('posts');
    const [selectedPost, setSelectedPost] = useState(null);
    const [newComment, setNewComment] = useState('');
    // Get the correct nested object
    const data = forumData.forumData || {};
    const forumObj = data.forum || {};
    
    const forum = {
        id: forumObj.id,
        title: forumObj.title,
        description: forumObj.description,
        owner: data.owner,
        avatar: data.avatar_url,
        createdAt: forumObj.created_at ? new Date(forumObj.created_at).toLocaleDateString() : '',
        tags: forumObj.category_list || [],
        isMember: data.isMember,
        currentUserId: data.user.id,
        ownerId: data.owner_id,
        members: data.forum_members,
        posts: data.posts,
    }

    const toogledata = {
            profile: forum,
            theme: "ubuntu",
            bookmarkedProfileIds: forumData.forumData.bookmarkedForumIds
     };
        
    const posts = forum.posts.map((post) => ({
            id: post.id,
            title: post.title,
            content: post.content,
            author: post.owner.first_name,
            timeAgo: post.created_ago,
            comments: post.comments.length,
            likes: post.likes.length,
            avatar: post.avatar,
            tags: ["Open Discussion", "Idea Exchange", "Group Dialogue"],
            comments_list: post.comments.map((comment) => (
                {
                    id: comment.id,
                    author: comment.author.first_name,
                    content: comment.content,
                    timeAgo: comment.timeAgo,
                    avatar: comment.avatar,
                    likes: comment.likes.length,
                    replies: comment.replies.map((reply) => (
                        {
                            id: reply.id,
                            author: reply.commenter.first_name,
                            replyToAuthor: comment.author.first_name,
                            content: reply.content,
                            timeAgo: reply.timeAgo,
                            likes: reply.likes.length,
                            avatar_url: reply.avatar_url
                            // replies: [
                            //     {
                            //         id: 6,
                            //         author: "Best",
                            //         replyToAuthor: "Farida",
                            //         content: "Love this! I'd add that accessible tech tools—like captioning, screen reader compatibility, and mobile-friendly formats—are also part of inclusive advocacy. Digital equity matters too.",
                            //         timeAgo: "1 day ago",
                            //         likes: 1,
                            //         replies: []
                            //     }
                            // ]
                        }
                    ))
                }
            ))
        }
    ));

    const otherForums = [
        { name: "Community Action & Organizing", members: "6.7k members", posts: "31k" },
        { name: "Policy, Advocacy & Civic Engagement", members: "4.2k members", posts: "18k" },
        { name: "Community Action & Organizing", members: "3.8k members", posts: "22k" },
        { name: "Stories of Impact & Change", members: "2.9k members", posts: "15k" },
        { name: "Resources, Tools & Collaborations", members: "5.1k members", posts: "41k" }
    ];

    const handlePostClick = (post) => {
        setSelectedPost(post);
        setActiveTab('discussions');
    };

    const handleAddComment = () => {
        if (newComment.trim() && selectedPost) {
            const newCommentObj = {
                postId: selectedPost.id,
                author: data.user.id,
                content: newComment,
            };

            Inertia.post("/posts/addComment", newCommentObj, {preserveScroll: true})
            setSelectedPost({
                ...selectedPost,
                comments_list: [...selectedPost.comments_list, newCommentObj]
            });
            console.log(newCommentObj)
            setNewComment('');
        }
    };


    const handleJoinForum = () => {
        router.post(`/forums/join/${forum.id}`)
    }

    return (
        <div className="min-h-screen bg-white relative">
            {/* Top red header */}
            <div className="absolute top-0 left-0 w-full bg-red-600 z-0" style={{ minHeight: '50vh', height: '50vh' }} aria-hidden="true"></div>
            {/* Main content, add pt-32 to push content below header, remove -mt-95 */}
            <div className="relative max-w-8xl mx-auto p-4 pt-32 z-10">
                <Toggle
                    pageData={toogledata}
                    theme={'ubuntu'}
                    backText={'View more Forums'}
                    backLink={'/forum'}
                    title={forum.title}
                />
                {/* Header Section */}
                <div className="flex gap-4 mb-4">
                    {/* Left Side - Topic and Description */}
                    <div className="flex-1 w-[3/4] rounded-lg pt-6 pl-6">
                        <div className='bg-[#F3F3F3] rounded-lg shadow-md p-6 mb-4 relative'> {/* Added relative positioning for absolute child */}
                            <div className="absolute top-4 right-4"> {/* Positioned BARAZA button */}
                                <button className="bg-red-600 text-white px-4 py-2 rounded-full text-sm font-medium">
                                    <MapPin className="inline w-4 h-4 mr-1" />
                                    {forum.tags}
                                </button>
                            </div>
                            <div className="flex items-center space-x-4 mb-4">
                                <div className="w-16 h-16 rounded-full overflow-hidden"> {/* Changed to overflow-hidden for image cropping */}
                                    <img src={forum.avatar} alt={forum.title} className="w-full h-full object-cover" /> {/* Use object-cover for better image fitting */}
                                </div>
                                <div>
                                    <h1 className="text-xl font-bold text-gray-900">{forum.title}</h1> {/* Adjusted text size */}
                                    <p className="text-sm text-gray-600">Owner: {forum.owner}</p>
                                    <p className="text-sm text-gray-600">Created : {forum.createdAt}</p>
                                </div>
                            </div>

                            <p className="text-gray-700 mb-4 leading-relaxed text-sm">
                                {forum.description}
                            </p>
                            {forum.currentUserId && (
                                <div className="flex flex-wrap gap-2 absolute bottom-10 right-4 p-6">
                                    {forum.currentUserId === forum.ownerId ? (
                                    <button
                                        onClick={() => {
                                        if (
                                            confirm(
                                            `Please Confirm Deletion Of This Forum\n\n` +
                                            `Forum Name: ${forum.title}\n\n` +
                                            `Forum ID: ${forum.id}\n\n` +
                                            `This Forum Will Be Deleted Once You Click 'Ok' And Can't Be Undone.`
                                            )
                                        ) {
                                            router.delete(`/forums/delete/${forum.id}`);
                                        }
                                        }}
                                        className="bg-green-600 text-white px-3 py-1 rounded-lg text-xs font-medium"
                                    >
                                        Delete Forum
                                    </button>
                                    ) : forum.isMember ? (
                                    <button
                                        onClick={() => router.delete(`/forums/leave/${forum.id}`)}
                                        className="bg-green-600 text-white px-3 py-1 rounded-lg text-xs font-medium"
                                    >
                                        Leave Forum
                                    </button>
                                    ) : (
                                    <button
                                        onClick={handleJoinForum}
                                        className="bg-red-600 text-white px-3 py-1 rounded-lg text-xs font-medium"
                                    >
                                        Join Forum
                                    </button>
                                    )}
                                </div>
                            )}

                        </div>
                        <div className="bg-[#F3F3F3] rounded-lg shadow-md p-6 mb-4">
                            {forum.isMember || forum.ownerId === forum?.currentUserId ? (
                                <>
                                    {/* Navigation Tabs */}
                                    <div className="border-b border-gray-200">
                                        <div className="flex">
                                            {[
                                                { key: 'posts', label: 'Posts' },
                                                { key: 'discussions', label: 'Discussions' },
                                                { key: 'resources', label: 'Resources' },
                                                { key: 'collaborators', label: 'Collaborators' }
                                            ].map((tab) => (
                                                <button
                                                    key={tab.key}
                                                    onClick={() => setActiveTab(tab.key)}
                                                    className={`px-6 py-3 font-medium text-sm border-b-2 transition-colors ${activeTab === tab.key
                                                            ? 'border-red-600 text-red-600 bg-red-50'
                                                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                                                        }`}
                                                >
                                                    {tab.label}
                                                </button>
                                            ))}
                                        </div>
                                    </div>

                                {/* Content */}
                                <div className="p-6 min-h-96">
                                    {activeTab === 'posts' && (
                                    <Posts posts={posts} forumId={forum.id} userId={forum.currentUserId} handlePostClick={handlePostClick} />
                                    )}
                                    {activeTab === 'discussions' && (
                                    <Discussions
                                        selectedPost={selectedPost}
                                        newComment={newComment}
                                        setNewComment={setNewComment}
                                        handleAddComment={handleAddComment}
                                        currentUser={data.user}
                                    />
                                    )}
                                    {activeTab === 'resources' && <Resources />}
                                    {activeTab === 'collaborators' && (
                                    <Collaborators collaborators={forum.members} />
                                    )}
                                </div>
                                </>
                            ) :
                                (
                                    <div className="flex items-center justify-center ">
                                        <div className="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-6 rounded-md shadow-sm text-center w-full">
                                            <p className="text-lg font-medium mb-4">
                                                You need to join this forum to interact with its content.
                                            </p>
                                            <button
                                                onClick={handleJoinForum}
                                                className="bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-4 rounded-md transition duration-200"
                                            >
                                                Join Forum
                                            </button>
                                        </div>
                                    </div>

                                )}
                        </div>
                    </div>

                    {/* Right Side - Search and BARAZA */}
                    <div className="w-[1/4] p-6 space-y-4">
                        <div className="bg-[#F3F3F3] rounded-2xl border-4 border-red-700 p-2"> {/* Adjusted outer container styling */}
                            <div className="flex items-center rounded-full border border-gray-300 overflow-hidden"> {/* Combined input and select into a single flex container with rounded corners and border */}
                                <div className="relative flex items-center px-4 py-2 text-base font-semibold text-gray-800 cursor-pointer"> {/* Custom dropdown container */}
                                    <span>Baraza</span>
                                    <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </div>
                                <div className="h-8 w-px bg-gray-300 mx-1"></div> {/* Visual separator line */}
                                <input
                                    type="text"
                                    placeholder="Search..."
                                    className="flex-1 px-4 py-2 text-base outline-none" // Removed specific rounded classes as parent handles it, increased padding and text size
                                />
                                <button className="bg-amber-800 text-white px-8 py-2 text-base font-semibold"> {/* Adjusted padding and font size for button */}
                                    Search
                                </button>
                            </div>
                        </div>

                        {/* Other Forums Sidebar */}
                        <div className="bg-[#F3F3F3] rounded-lg border border-gray-200 p-4">
                            <h3 className="font-semibold text-red mb-4">Other Forums You Might Like</h3>
                            <div className="space-y-3">
                                {otherForums.map((forum, index) => (
                                    <div key={index} className="flex items-start space-x-3 p-2 hover:bg-gray-50 rounded">
                                        <div className="w-8 h-8 rounded-full overflow-hidden flex-shrink-0">
                                            <img src={ProfileImage} alt="Forum" className="w-full h-full object-cover" />
                                        </div>
                                        <div className="flex-1 min-w-0">
                                            <p className="font-medium text-sm text-gray-900 mb-1">{forum.name}</p>
                                            <p className="text-xs text-gray-500">{forum.members} • {forum.posts} posts</p>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                </div>


            </div>
        </div>
    );
};

ForumPage.layout = (page) => <Layout children={page} title="Projects' Page" userData={page.props.user} />

export default ForumPage;