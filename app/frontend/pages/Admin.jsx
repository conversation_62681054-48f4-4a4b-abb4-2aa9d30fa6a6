import { Inertia } from '@inertiajs/inertia'
import { useState, useEffect } from 'react'
import InviteUserModal from './InviteUser'

export default function Admin({ user, error: initialError }) {
    const [users, setUsers] = useState([])
    const [usersFetched, setUsersFetched] = useState(false)
    const [message, setMessage] = useState("")
    const [error, setError] = useState(initialError || "")

    async function loadUsers() {
        const res = await fetch('/users')
        const data = await res.json()
        setUsers(data.users)
        setUsersFetched(true)
    }

    // Clear error after 5 seconds
    useEffect(() => {
        if (error) {
            const timer = setTimeout(() => setError(""), 5000)
            return () => clearTimeout(timer)
        }
    }, [error])

    // Clear message after 5 seconds
    useEffect(() => {
        if (message) {
            const timer = setTimeout(() => setMessage(""), 5000)
            return () => clearTimeout(timer)
        }
    }, [message])

    const handleRemoveRole = async (id, role) => {
        if (!id || !role) return;
        setError("");
        setMessage("");

        try {
            const response = await fetch("/admin/remove_role", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "Accept": "application/json",
                    "X-CSRF-Token": document.querySelector('meta[name="csrf-token"]').content,
                },
                body: JSON.stringify({ id, role }),
            });

            if (!response.ok) {
                const errorData = await response.json();
                setError(errorData.error || "Something went wrong");
            } else {
                await loadUsers();
                setMessage(`${role} role removed successfully`);
            }
        } catch (error) {
            setError("Network error, please try again.");
            console.error("Fetch error:", error);
        }
    };

    const handleAddRole = async (id, role) => {
        if (!id || !role) return;
        setError("");
        setMessage("");

        try {
            const response = await fetch("/admin/add_role", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "Accept": "application/json",
                    "X-CSRF-Token": document.querySelector('meta[name="csrf-token"]').content,
                },
                body: JSON.stringify({ id, role }),
            });

            if (!response.ok) {
                const errorData = await response.json();
                setError(errorData.error || "Something went wrong");
            } else {
                await loadUsers();
                setMessage(`${role} role added successfully`);
            }
        } catch (error) {
            setError("Network error, please try again.");
            console.error("Fetch error:", error);
        }
    };

    return (
        <div className="min-h-screen bg-gray-100">
            {/* Header */}
            <header className="bg-white shadow-md px-6 py-4 flex items-center justify-between">
                <h3 className="text-lg text-gray-800 font-semibold">
                    Welcome, Admin {user.email}
                </h3>
                <button
                    onClick={() => Inertia.delete("/users/sign_out")}
                    className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition"
                >
                    Logout
                </button>
            </header>
            <div>
                {error && <div className="text-red-500 mt-4">{error}</div>}
                {message && <div className="text-green-500 mt-4">{message}</div>}
            </div>

            {/* Main Content */}
            <main className="p-8">
                <h1 className="text-2xl font-bold text-gray-800 mb-6">Admin Dashboard</h1>

                <button
                    onClick={loadUsers}
                    className="text-blue-600 hover:text-blue-800 font-medium cursor-pointer
"
                >
                    List all users with roles
                </button>

                <button type="button"
                    className="text-blue-600 hover:text-blue-800 font-medium m-4 cursor-pointer
"
                    onClick={(e)=>{
                        e.preventDefault()
                        Inertia.get("/users/invitation/new")
                    }}
                >
                    Invite Users
                </button>

                      <button type="button"
                    className="text-blue-600 hover:text-blue-800 font-medium m-4 cursor-pointer
"
                    onClick={(e)=>{
                        e.preventDefault()
                        Inertia.get("/pghero")
                    }}
                >
                    View Database Stats
                </button>

                {/* Display users table if available */}
                {users && users.length > 0 && (
                    <div className="mt-8 overflow-x-auto">
                        <table className="w-full bg-white rounded-lg shadow">
                            <thead className="bg-gray-100 text-left">
                                <tr>
                                    <th className="py-2 px-4">Id</th>
                                    <th className="py-2 px-4">Name</th>
                                    <th className="py-2 px-4">Email</th>
                                    <th className="py-2 px-4">Roles</th>
                                </tr>
                            </thead>
                            <tbody>
                                {users.map((u) => (
                                    <tr key={u.id} className="border-t">
                                        <td className="py-2 px-4">{u.id}</td>
                                        <td className="py-2 px-4">{u.first_name} {u.last_name}</td>
                                        <td className="py-2 px-4">{u.email}</td>
                                        <td className="py-2 px-4 space-y-2">
                                            <div className="flex flex-wrap gap-2">
                                                {u.roles.map((role, index) => (
                                                    <span
                                                        key={index}
                                                        className="flex items-center bg-gray-200 text-sm px-2 py-1 rounded-full"
                                                    >
                                                        {role}
                                                        <button
                                                            type='button'
                                                            className="ml-2 text-red-500 hover:text-red-700"
                                                            onClick={() => handleRemoveRole(u.id, role)}
                                                        >
                                                            ×
                                                        </button>
                                                    </span>
                                                ))}
                                            </div>

                                            <select
                                                className="mt-2 border rounded px-2 py-1 text-sm"
                                                onChange={(e) => {
                                                    handleAddRole(u.id, e.target.value);
                                                    e.target.value = "";
                                                }}
                                                defaultValue=""
                                            >
                                                <option value="" disabled>Add Role</option>
                                                <option value="admin">Admin</option>
                                                <option value="regular_user">Regular User</option>
                                            </select>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                )}

                {usersFetched && users && users.length === 0 && (
                    <p className="mt-6 text-gray-600">No users found.</p>
                )}
            </main>
        </div>
    )
}
