// app/frontend/pages/Places/Index.jsx
import Layout from '../../components/shared/Layout'
import Banner from '../../components/shared/Banner'
import placesImg1 from '/assets/places_carousel/places1.png';
import placesImg2 from '/assets/places_carousel/places2.png';
import placesImg3 from '/assets/places_carousel/places3.png';
import placesImg4 from '/assets/places_carousel/places4.png';
import placesFrame from '/assets/places_carousel/frame.png';
import PlacesCardsSection from '../../components/pageSpecific/places/PlacesCardsSection';
import Search from '../../components/shared/Search';

const placesImages = [placesImg1, placesImg2, placesImg3, placesImg4];

const Places = ({places}) => {
    return (
        <>
            <Banner
                bgColor="bg-primaryorange"
                title="Places"
                description="Change makers need safe spaces. Spaces to engage, co-create, invent and learn, These places have exactly what you need: facilities, tools and just the right environment to provide and receive support from like-minded souls…where will you go next?"
                images={placesImages}
                frame={placesFrame}
                fadeDuration={500}
                imageInterval={3000}
            />
            <Search
                color="orange"
                defaultPlaceholder="Search for places in design, visual art, music, craft, technology, and more..."
                mostSearchedItems={[
                    'UI/UX Design',
                    'Education',
                    'Visual Art',
                    'Performing Art',
                    'Video & Film',
                    'Music',
                    'Craft',
                    'Technology',
                    'Journalism',
                ]}
                filterOptions={{
                    'Category': [
                        'accelerator', 'co-creators', 'crafts workshop', 'event space', 'gallery',
                        'incubator', 'innovation hub', 'maker space', 'co-working space', 'music studio',
                        'training facility'
                    ],
                    'Service': [
                        '3D Printer', 'Catering', 'Exhibition space', 'instruments', 'lighting',
                        'office infrastructure', 'Pitching events', 'Pop up stores', 'Precision cutter', 'Recording',
                        'residence', 'Sewing machines', 'sound system', 'stage', 'tools',
                        'Trainings', 'Welding equipment'
                    ]
                }}
            />
            <PlacesCardsSection places={places} />
        </>
    )
}

Places.layout = (page) => <Layout children={page} title="Places' Page" userData={page.props.user} />

export default Places

