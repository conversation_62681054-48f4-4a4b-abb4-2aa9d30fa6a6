// People/index.jsx
import Layout from '../../components/shared/Layout'
import Banner from '../../components/shared/Banner'
import Search from '../../components/shared/Search'
import PeopleCardsSection from '../../components/pageSpecific/people/PeopleCardsSection';
import peopleImg1 from '/assets/people_carousel/people1.png';
import peopleImg2 from '/assets/people_carousel/people2.png';
import peopleImg3 from '/assets/people_carousel/people3.png';
import peopleImg4 from '/assets/people_carousel/people4.png';
import peopleFrame from '/assets/people_carousel/frame.png';

const peopleImages = [peopleImg1, peopleImg2, peopleImg3, peopleImg4];

const People = ({ profiles }) => {
    return (
        <>
            <Banner
                bgColor="bg-primaryyellow"
                title="People"
                description="A vibrant community of creatives is ready to connect, collaborate, and co-create. From artists, filmmakers, and photographers to graphic, product, web, and fashion designers — and even architects — BongoHub brings together talents from all walks of creative life."
                images={peopleImages}
                frame={peopleFrame}
                fadeDuration={500}
                imageInterval={3000}
            />
            <Search
                color="yellow"
                defaultPlaceholder="Search for people in designers, visual artists, music, craft, technology, and more..."
                mostSearchedItems={[
                    'UI/UX Design',
                    'Education',
                    'Visual Art',
                    'Performing Art',
                    'Video & Film',
                    'Music',
                    'Craft',
                    'Technology',
                    'Journalism',
                ]}
                filterOptions={{
                    'Skill Tags': [
                        '3D-animation', 'actor/actress', 'architecture', 'climate', 'dancer',
                        'education', 'edutainment', 'environment', 'facilitator', 'girls & women',
                        'graphic recording', 'guitar', 'percussion', 'interior design', 'moderator',
                        'photographer', 'recycling', 'sculpturing', 'singer', 'social entrepreneur',
                        'social justice', 'sound technician', 'sports', 'training', 'upcycling',
                        'urban planning'
                    ],
                    'Category': [
                        'activism', 'animation', 'architecture', 'art', 'crafts', 'design',
                        'edutainment', 'fashion', 'gaming', 'graphic', 'music', 'performance',
                        'photography', 'R&D', 'urban culture', 'UX design', 'video / film'
                    ],
                }}
            />
            <PeopleCardsSection profiles={profiles} />
        </>
    )
}

People.layout = (page) => <Layout children={page} title="Peoples' Page" userData={page.props.user} />

export default People

