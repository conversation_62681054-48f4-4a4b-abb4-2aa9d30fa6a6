// app/frontend/pages/Home.jsx
import { Inertia } from '@inertiajs/inertia'

export default function Home({ user }) {
  return (
    <div>
      {user ? (
        <>
          <h3>Welcome {user.first_name || "Stranger"}</h3>
          <button onClick={() => Inertia.delete("/users/sign_out")}>Logout</button>
        </>
      ) : (
        <>
          <h3>Hello Guest</h3>
          <a href="/login">Login</a>
          <a href="/signup">SignUp</a>
          <input 
          type="button" 
          value="facebook"
              onClick={() => { Inertia.get("/users/auth/facebook")}}
          />
        </>
      )}
    </div>
  )
}
