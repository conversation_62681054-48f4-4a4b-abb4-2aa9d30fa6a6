// app/frontend/pages/Profile/Index.jsx
import React from 'react';
import { usePage } from '@inertiajs/react';
import Layout from '../../components/shared/Layout';
import Background from '/assets/profilebackground.png';
import Toggle from '../../components/pageSpecific/profile/Toggle';
import ProfileSection from '../../components/pageSpecific/profile/ProfileSection';
import CommunityStanding from '../../components/pageSpecific/profile/CommunityStanding';
import Gallery from '../../components/pageSpecific/profile/Gallery';
import PortfolioSection from '../../components/pageSpecific/profile/PortfolioSection';

// Sample defaults for missing fields
const defaultData = {
  status: "Online",
  friendlinessLevel: "Very Friendly",
  associatedProjects: [],
  associatedPlaces: [],
  associatedForums: []
};

const EmptyState = ({ type }) => (
  <div className="flex flex-col items-center justify-center text-center p-6 bg-yellow-100 text-yellow-800 rounded-md shadow-md">
    <h3 className="text-xl font-semibold mb-2">Oops!</h3>
    <p className="text-base">Looks like we came up empty.</p>
  </div>
);

// Main Profile Page Component
const UserProfilePage = ({ userData, bookmarkedProfileIds }) => {
  // Combine userData data with defaults and handle empty states
  const combinedData = {
    ...defaultData,
    ...userData,
    bookmarkedProfileIds,
    // Transform projects data
    projects: userData.projects?.length > 0 ? userData.projects.map(project => ({
      id: project.id,
      name: project.name,
      image: project.profile_image || project.image
    })) : [],
    // Transform places data
    places: userData.places?.length > 0 ? userData.places.map(place => ({
      id: place.id,
      name: place.name,
      image: place.profile_image || place.image
    })) : [],
    // Transform forums data
    forums: userData.forums?.length > 0 ? userData.forums.map(forum => ({
      id: forum.id,
      name: forum.title,
      image: forum.image
    })) : [],
    // Use title array as professionalFocus if it exists
    title: userData.title || [],
    professionalFocus: userData.professionalFocus?.length > 0 ? userData.professionalFocus.map(proffesion => proffesion) : [],
    // Ensure skills is always an array
    skills: userData.skills || []
  };

  return (
    <div className="min-h-screen bg-white py-8 px-8 md:px-12 lg:px-20">
      <div
        className="min-h-screen mx-auto rounded-lg overflow-hidden"
        style={{ backgroundImage: `url(${Background})`, backgroundRepeat: 'repeat-y', backgroundSize: 'contain' }}
      >
        <Toggle user={combinedData} />
        <div className="max-w-9xl mx-auto px-4 py-6">
          <ProfileSection user={combinedData} />
          <CommunityStanding user={combinedData} />
          <Gallery items={combinedData.gallery_images} title="Gallery" EmptyState={EmptyState} />
          <PortfolioSection user={combinedData} EmptyState={EmptyState} />
        </div>
      </div>
    </div>
  );
}

UserProfilePage.layout = page => <Layout children={page} title="Profile Page" userData={page.props.user} />;

export default UserProfilePage;
