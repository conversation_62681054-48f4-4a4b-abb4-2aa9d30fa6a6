// app/frontend/pages/Signup.jsx
import { useState } from 'react'
import { Inertia } from '@inertiajs/inertia'

export default function SignUp({ errors: pageErrors = {} }) {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [first_name, setfirst_name] = useState("")
  const [last_name, setlast_name] = useState("")
  const [errors, setErrors] = useState(pageErrors)

  const handleSubmit = (e) => {
    e.preventDefault()

    if (password !== confirmPassword) {
      setErrors({ password_confirmation: "Passwords do not match" })
      return
    }

    Inertia.post('/users', {
      user: {
        first_name,
        last_name,
        email,
        password,
        password_confirmation: confirmPassword
      }
    }, {
      onError: (err) => setErrors(err),
    })
  }

  return (
    <div style={{ padding: '2rem', maxWidth: '400px', margin: 'auto' }}>
      <h2>Sign Up</h2>

      <form onSubmit={handleSubmit}>
        <input
          type="text"
          placeholder="First Name"
          value={first_name}
          onChange={(e) => setfirst_name(e.target.value)}
          style={{ display: 'block', width: '100%', marginBottom: '1rem' }}
        />
        {errors.first_name && <div style={{ color: 'red' }}>{errors.first_name}</div>}
        
        <input
          type="text"
          placeholder="Last Name"
          value={last_name}
          onChange={(e) => setlast_name(e.target.value)}
          style={{ display: 'block', width: '100%', marginBottom: '1rem' }}
        />
        {errors.last_name && <div style={{ color: 'red' }}>{errors.last_name}</div>}
        
        <input
          type="email"
          placeholder="Email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          style={{ display: 'block', width: '100%', marginBottom: '1rem' }}
        />
        {errors.email && <div style={{ color: 'red' }}>{errors.email}</div>}

        <input
          type="password"
          placeholder="Password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          style={{ display: 'block', width: '100%', marginBottom: '1rem' }}
        />
          {errors.password && <div style={{ color: 'red' }}>{errors.password}</div>}
        <input
          type="password"
          placeholder="Confirm Password"
          value={confirmPassword}
          onChange={(e) => setConfirmPassword(e.target.value)}
          style={{ display: 'block', width: '100%', marginBottom: '1rem' }}
        />
        {errors.password_confirmation && <div style={{ color: 'red' }}>{errors.password_confirmation}</div>}

        <button type="submit" style={{ width: '100%', cursor: 'pointer' }}>Register</button>
      </form>
      <a href="/users/auth/facebook">Sign in with Facebook</a>
      <a href="/users/auth/google_oauth2">Sign in with Google</a>
    </div>
  )
}
