import Layout from '../../components/shared/Layout'
import Banner from '../../components/shared/Banner'
import Search from '../../components/shared/Search'
import ProjectsCardsSection from '../../components/pageSpecific/projects/ProjectsCardsSection'

import projectsImg1 from '/assets/projects_carousel/project1.png'
import projectsImg2 from '/assets/projects_carousel/project2.png'
import projectsImg3 from '/assets/projects_carousel/project3.png'
import projectsImg4 from '/assets/projects_carousel/project4.png'
import projectsFrame from '/assets/projects_carousel/frame.png'

const projectsImages = [projectsImg1, projectsImg2, projectsImg3, projectsImg4];

const PROJECTS_DESCRIPTION = "In every community, creative change-makers are building African solutions \
for African challenges—because no one understands our needs better than we do. These projects reflect innovation, \
impact, and local empowerment."

const Projects = ({ projects }) => {
    return (
        <>
            <Banner
                bgColor="bg-primarygreen"
                title="Projects"
                description={PROJECTS_DESCRIPTION}
                images={projectsImages}
                frame={projectsFrame}
                fadeDuration={500}
                imageInterval={3000}
            />
            <Search
                color="green"
                defaultPlaceholder="Search for projects in design, visual art, music, craft, technology, and more..."
                mostSearchedItems={[
                    'UI/UX Design',
                    'Education',
                    'Visual Art',
                    'Performing Art',
                    'Video & Film',
                    'Music',
                    'Craft',
                    'Technology',
                    'Journalism',
                ]}
                filterOptions={{
                    'Category': [
                        'climate', 'community', 'culture', 'education', 'empowerment',
                        'environment', 'financing', 'food', 'gender', 'health',
                        'job creation', 'music', 'sports', 'urban beautification', 'youth'
                    ],
                    'Service': [
                        'awareness raising', 'campaigns', 'counselling', 'events', 'financial support',
                        'group activities', 'individual support', 'sponsoring', 'tools & facilities', 'training'
                    ]
                }}
            />
            <ProjectsCardsSection projects={projects} />
        </>
    )
}

Projects.layout = (page) => <Layout children={page} title="Projects' Page" userData={page.props.user} />

export default Projects

