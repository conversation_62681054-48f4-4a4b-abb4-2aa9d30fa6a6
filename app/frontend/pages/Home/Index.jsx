import React from 'react'
import Layout from '../../components/shared/Layout'
import PeoplePreview from './Components/People'
import PlacesPreview from './Components/Places'
import Footer from './Components/Footer'
import HeroSection from './Components/Hero'
import TribeSection from './Components/Tribe'

import ProjectsPreview from './Components/Projects'
import UbuntuPreview from './Components/Ubuntu'

function Index({ user, forums, bookmarkedForumIds }) {
  return (
    <div>
      <HeroSection />
      <TribeSection />
      <PeoplePreview />
      <ProjectsPreview />
      <PlacesPreview />
      <UbuntuPreview forums={forums} bookmarkedForumIds={bookmarkedForumIds}/>
    </div>
  )
}

Index.layout = (page) => <Layout children={page} title="Home" userData={page.props.user} />

export default Index