import React from 'react';
import SectionSeparator from './SectionSeparator';
import { router } from '@inertiajs/react';

import heroImage from '/assets/heroimage.png';
import profileImage1 from '/assets/profile1.jpg';
import profileImage2 from '/assets/profile2.jpg';
import profileImage3 from '/assets/profile3.jpg';
import profileImage4 from '/assets/profile4.jpg';

export default function HeroSection() {
    return (
        <>
            <section className="w-full px-6 2xl:px-20 py-8 md:py-16 pt-26 md:pt-30 2xl:pt-40">
                <div className="flex flex-col-reverse items-center md:flex-row md:items-center md:justify-between">
                    {/* Left Content - Text and Buttons */}
                    <div className="w-full md:w-1/2 mb-8 md:mb-0 text-center md:text-left">
                        <h1 className="text-4xl md:text-5xl lg:text-6xl 2xl:text-7xl font-bold text-amber-950 mb-6 leading-tight">
                            Connecting Creatives<br />
                            Across East Africa.<br />
                            <PERSON>oja <PERSON>.
                        </h1>

                        <p className="text-base md:text-lg text-gray-800 mb-8 max-w-lg mx-auto md:mx-0">
                            A community-driven network of creative people, projects and places across East Africa.
                        </p>

                        <div className="flex w-full flex-col lg:flex-row items-center lg:pr-15 md:items-start gap-5 mb-8">
                            <button
                                className="flex-1 min-w-[200px] px-10 py-3 bg-primarybrown text-white font-medium rounded-full hover:bg-black hover:text-white hover:cursor-pointer transition duration-300"
                                onClick={() => {
                                    router.visit('/people');
                                }}
                            >
                                Find a creative
                            </button>
                            <button
                                className="flex-1 min-w-[200px] px-10 py-3 bg-white text-black font-medium rounded-full border-2 border-black hover:bg-black hover:text-white hover:cursor-pointer transition duration-300"
                                onClick={() => {
                                    window.location.href = '/users/sign_up';
                                }}
                            >
                                Join as a creative
                            </button>
                        </div>

                        {/* Creative Count */}
                        <div className="flex items-center justify-center md:justify-start">
                            <div className="flex -space-x-5">
                                {/* profile images */}
                                <div className="w-20 h-20 rounded-full overflow-hidden border-2 border-white">
                                    <img src={profileImage1} alt="Creative profile" className="w-full h-full object-cover" />
                                </div>
                                <div className="w-20 h-20 rounded-full overflow-hidden border-2 border-white">
                                    <img src={profileImage2} alt="Creative profile" className="w-full h-full object-cover" />
                                </div>
                                <div className="w-20 h-20 rounded-full overflow-hidden border-2 border-white">
                                    <img src={profileImage3} alt="Creative profile" className="w-full h-full object-cover" />
                                </div>
                                <div className="w-20 h-20 rounded-full overflow-hidden border-2 border-white">
                                    <img src={profileImage4} alt="Creative profile" className="w-full h-full object-cover" />
                                </div>
                            </div>
                            <div className="ml-4">
                                <p className="font-bold text-xl">100+</p>
                                <p className="text-sm">Creatives</p>
                            </div>
                        </div>
                    </div>


                    {/* Right Content - Image Collage */}
                    <div className="w-full md:w-1/2 lg:pl-12">
                        <div className="relative aspect-square w-full lg:aspect-video">
                            <BackgroundSection />
                        </div>
                    </div>
                </div>
            </section>
            {/* Section Separator */}
            <SectionSeparator />
        </>
    );
}

const BackgroundSection = () => {
    // Remove useId and use a static ID since SVG IDs need to be unique but stable
    const maskId = 'hero-mask';

    return (
        // Change h-screen to appropriate aspect ratio
        <div className="relative aspect-square w-full overflow-hidden max-h-aspect md:max-h-[700px]">
            {/* Background Image */}
            <div
                className="absolute inset-0 bg-cover bg-center"
                style={{ backgroundImage: `url(${heroImage})` }}
            />

            {/* SVG Mask Definition - Now visible in DOM */}
            <svg aria-hidden="true" className="absolute inset-0 h-full w-full">
                <mask id={maskId}>
                    <rect width="110%" height="110%" fill="white" />
                    {/* First rectangle - starts from top, ends 20% before bottom */}
                    <rect
                        x="4%" // 4% to align with left edge
                        y="0%" // Start from top
                        width="20%" // 2 * rx (10% * 2)
                        height="80%" // 80% of container height (not reaching bottom)
                        rx="10%" // Fully rounded ends (half of width)
                        fill="black"
                    />
                    {/* Second rectangle - starts 20% below top, ends at bottom */}
                    <rect
                        x="28%" // 4%+20+4%% to align with left edge
                        y="20%" // Start 20% below top
                        width="20%" // 2 * rx (10% * 2)
                        height="80%" // To reach the bottom (100% - 20%)
                        rx="10%" // Fully rounded ends (half of width)
                        fill="black"
                    />
                    {/* Third rectangle - starts from top, ends 20% before bottom */}
                    <rect
                        x="52%" // 28%+4%+20% to align with left edge
                        y="0%" // Start from top
                        width="20%" // 2 * rx (10% * 2)
                        height="80%" // 80% of container height (not reaching bottom)
                        rx="10%" // Fully rounded ends (half of width)
                        fill="black"
                    />
                    {/* Fourth rectangle - starts 20% below top, ends at bottom */}
                    <rect
                        x="76%" // 52%+4%+20% to align with left edge
                        y="20%" // Start 20% below top
                        width="20%" // 2 * rx (10% * 2)
                        height="80%" // To reach the bottom (100% - 20%)
                        rx="10%" // Fully rounded ends (half of width)
                        fill="black"
                    />
                </mask>
            </svg>

            {/* Overlay with Mask */}
            <div
                className="absolute inset-0 bg-white"
                style={{
                    mask: `url(#${maskId})`,
                    WebkitMask: `url(#${maskId})`,
                }}
            />
        </div>
    );
}