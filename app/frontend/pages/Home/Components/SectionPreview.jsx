import { useState, useEffect } from 'react';
import { router } from '@inertiajs/react';

import SectionSeparator from './SectionSeparator';
import PrimaryActionBtn from './PrimaryActionButton';
import SecondaryActionBtn from './SecondaryActionButton';
import ImageCarousel from './ImageCarousel'

const FADE_DURATION_MS = 1000;
const IMAGE_INTERVAL_MS = 4000;

// Section Preview Component
const SectionPreview = ({
  bgColor,
  title,
  description,
  images,
  frame,
  primaryButtonText,
  secondaryButtonText,
  imageLeft = false,
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    if (!images || images.length === 0) {
      return;
    }
    const intervalId = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % images.length);
    }, IMAGE_INTERVAL_MS);

    // Clear the interval when the component unmounts or images prop changes
    return () => clearInterval(intervalId);
  }, [images]);

  // Function to handle button clicks and navigate with Inertia.js
  // Function to handle button clicks and navigate with Inertia.js
  const handleButtonClick = (buttonText) => {
    let path = '';
    switch (buttonText) {
      case "Become a BongoHubber":
        path = "/users/sign_up";
        break;
      case "Visit People's Page":
        path = "/people";
        break;
      case "View All Projects":
        path = "/projects";
        break;
      case "Register A Project":
        path = "/profile?section=projects&view=create-project";
        break;
      case "Continue To Places":
        path = "/places";
        break;
      case "Register A Place":
        path = "/profile?section=places&view=create-place";
        break;
      default:
        // Handle cases where no specific path is defined, perhaps a default or error
        console.warn(`No specific navigation path defined for: ${buttonText}`);
        return;
    }
    if (path) {
      window.location.href = path
    }
  };

  // Determine flex direction based on imageLeft prop
  const flexDirectionClass = imageLeft ? 'md:flex-row-reverse' : 'md:flex-row';

  return (
    <>
      {/* Background Section with Pattern using Tailwind Pseudo-elements */}
      <div
        className={`${bgColor} font-inter px-6 2xl:px-20 pt-8 pb-12 md:pt-8 md:pb-20 lg:pt-8 lg:pb-24 relative
                   before:content-[''] before:absolute before:inset-0
                   before:bg-[url('/assets/simple_pattern.png')] before:bg-repeat-x before:bg-bottom before:bg-[20vw_auto]
                   before:opacity-60 before:z-0`}
      >
        {/* Container for Flexbox Layout  */}
        <div className={`w-full flex flex-col ${flexDirectionClass} justify-between items-center md:items-center relative z-10`}>
          {/* Text Content Section */}
          <div className="w-full max-w-screen-md mr-auto md:w-1/2 text-black mb-8 md:mb-0">
            <h2 className="text-2xl md:text-4xl lg:text-6xl font-semibold md:font-bold mb-4 md:mb-6">
              {title}
            </h2>
            <p className="text-sm md:text-base lg:text-lg font-normal mb-6 md:mb-8 leading-relaxed">
              {description}
            </p>

            {/* Separator Line - Hidden on mobile */}
            <div className="w-full h-px bg-black my-6 md:my-8 hidden md:block"></div>

            {/* Buttons - Hidden on mobile */}
            <div className="hidden md:block">
              <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
                {primaryButtonText && (
                  <PrimaryActionBtn
                    text={primaryButtonText}
                    onClick={() => handleButtonClick(primaryButtonText)}
                  />
                )}
                {secondaryButtonText && (
                  <SecondaryActionBtn
                    text={secondaryButtonText}
                    onClick={() => handleButtonClick(secondaryButtonText)}
                  />
                )}
              </div>
            </div>
          </div>

          {/* Image Carousel Section */}
          {images && images.length > 0 && (
            <div className="w-full md:w-1/2 flex flex-col justify-center items-center mb-8 md:mb-0">
              <ImageCarousel images={images} frame={frame} fadeDuration={FADE_DURATION_MS} imageInterval={IMAGE_INTERVAL_MS} left={imageLeft} />
              {/* Separator Line - Visible on mobile */}
              <div className="w-full h-px bg-black my-3 md:hidden"></div>
            </div>
          )}

          {/* Buttons - centered on mobile only */}
          <div className="w-full flex justify-center md:hidden">
            <div className="flex flex-row w-full space-between space-x-4">
              {primaryButtonText && (
                <PrimaryActionBtn
                  text={primaryButtonText}
                  className="w-[48%]"
                />
              )}
              {secondaryButtonText && (
                <SecondaryActionBtn
                  text={secondaryButtonText}
                  className="w-[48%]"
                />
              )}
            </div>
          </div>

        </div>
      </div>
      <SectionSeparator />
    </>
  );
};

export default SectionPreview;