import React from 'react';
import { ThumbsUp, MessageSquare } from 'lucide-react';
import imageUrl from '/assets/ubuntu_assets/placeholder.png';

const UbuntuCard = ({ title, description, author, likes, comments, profileImage }) => {
  return (
    <div className="bg-blue-50 rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-transform duration-300 flex flex-col group">
      {/* Image */}
      <div className="relative h-60 md:h-80 bg-blue-100 overflow-hidden">
        <img
          src={imageUrl}
          alt={title}
          className="absolute inset-0 w-full h-full object-cover transition-opacity duration-300 group-hover:opacity-80"
        />
        {/* Overlay */}
        <div className="absolute inset-0 bg-black bg-opacity-50 text-white flex items-center justify-center transition-opacity duration-300 opacity-0 group-hover:opacity-75">
          <span className="font-bold text-2xl group-hover:underline">Read More</span>
        </div>
      </div>

      {/* Card Content */}
      <div className="p-4 flex-grow flex flex-col justify-between">
        <div className="transition-transform duration-200 group-hover:scale-[0.99]">
          <h3 className="text-base md:text-xl font-bold mb-2 group-hover:underline min-h-14">{title}</h3>
          <p className="text-sm md:text-base lg:text-lg text-gray-700 mb-10 truncate">{description}</p>
        </div>

        {/* Author and Stats */}
        <div className="flex items-center justify-between transition-transform duration-200 group-hover:scale-[0.99]">
          <div className="flex items-center group-hover:underline">
            {/* Profile Image */}
            <div className="w-8 h-8 rounded-full overflow-hidden mr-2">
              <img
                src={profileImage}
                alt={`${author}'s profile`}
                className="w-full h-full object-cover"
              />
            </div>
            <span className="font-semibold">{author}</span>
          </div>
          <div className="flex items-center space-x-4">
            <div className="flex items-center hover:underline">
              <ThumbsUp className="w-5 h-5 mr-1" fill="currentColor" />
              <span>{likes}</span>
            </div>
            <div className="flex items-center hover:underline">
              <MessageSquare className="w-5 h-5 mr-1" fill="currentColor" />
              <span>{comments}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UbuntuCard;