import { Instagram, Youtube, Facebook, ArrowUpRight } from 'lucide-react';
import BongoHubLogo from '/assets/logo.png';
import FooterPattern from '/assets/footer-pattern.png';
import { router } from '@inertiajs/react';

export default function Footer() {
    return (
        <footer className="w-full bg-[#3B1E00] text-white">
            <div className=" mx-auto px-6 2xl:pl-30 py-12 2xl:px-20">
                <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4 md:gap-1">
                    {/* Logo and Description Section */}
                    <div className="flex flex-col md:pr-10 items-center">
                        <div className="bg-white border-5 md:border-9 border-[#E0E0E0] rounded-full p-4 mb-6">
                            <img
                                src={BongoHubLogo}
                                onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
                                alt="Bongo Hub Logo"
                                className="h-20 w-20 md:h-30 md:w-30 object-contain"
                            />
                        </div>
                        <p className="text-sm max-w-xs text-center ">
                            This is a sample text description for the site. Lorem Ipsum is simply dummy text.
                        </p>
                    </div>

                    {/* People Section */}
                    <div className="flex flex-col items-center md:items-start">
                        <a onClick={() => router.visit('/people')}>
                            <h3 className="text-sm md:text-xl font-semibold mb-1 md:mb-4">People</h3>
                        </a>
                        <ul className="space-y-3 hidden md:grid" >
                            <li>
                                <a href="#" className=" transition-colors duration-200">Account</a>
                            </li>
                            <li>
                                <a href="#" className="flex items-center  transition-colors duration-200">
                                    My Projects <ArrowUpRight className="ml-1 h-4 w-4" />
                                </a>
                            </li>
                            <li>
                                <a href="#" className=" transition-colors duration-200">Connection</a>
                            </li>
                            <li>
                                <a href="#" className=" transition-colors duration-200">Favorites</a>
                            </li>
                            <li>
                                <a href="#" className=" transition-colors duration-200">Recommendations</a>
                            </li>
                        </ul>
                    </div>

                    {/* Project Section */}
                    <div className="flex flex-col items-center md:items-start">
                        <a
                            onClick={() => router.visit('/projects')}
                        >
                            <h3 className="text-sm md:text-xl font-semibold mb-4">Project</h3>
                        </a>
                        <ul className="space-y-3 hidden md:grid">
                            <li>
                                <a onClick={() => router.visit('/profile?section=projects&view=create-project')} href="#" className=" transition-colors duration-200">New project</a>
                            </li>
                            <li>
                                <a onClick={() => router.visit('/profile?section=projects')} href="#" className=" transition-colors duration-200">My projects</a>
                            </li>
                            <li>
                                <a href="#" className=" transition-colors duration-200">Popular</a>
                            </li>
                            <li>
                                <a href="#" className=" transition-colors duration-200">All projects</a>
                            </li>
                        </ul>
                    </div>

                    {/* Places and Follow Us Section */}
                    <div className="flex flex-col items-center md:items-start">
                        {/* Places Section */}
                        <a onClick={() => router.visit('/places')}>
                            <h3 className="text-sm md:text-xl font-semibold mb-4">Places</h3>
                        </a>
                        <ul className="space-y-3 hidden md:grid">
                            <li>
                                <a onClick={() => router.visit('/profile?section=places&view=create-place')} href="#" className=" transition-colors duration-200">New place</a>
                            </li>
                            <li>
                                <a onClick={() => router.visit('/profile?section=places')} href="#" className=" transition-colors duration-200">My places</a>
                            </li>
                            <li>
                                <a href="#" className=" transition-colors duration-200">Popular</a>
                            </li>
                            <li>
                                <a href="#" className=" transition-colors duration-200">Hubs</a>
                            </li>
                        </ul>
                    </div>
                    {/* Follow Us Section */}
                    <div className="flex flex-col items-center md:items-start">
                        <h3 className="text-sm md:text-xl font-semibold mb-4 text-center md:text-left">Follow Us</h3>
                        <div className="flex justify-center md:justify-start space-x-4 mb-6">
                            <a href="#" className="bg-white p-2 rounded-lg transition-colors duration-200">
                                <Instagram className="h-4 w-4 md:h-6 md:w-6 text-amber-950" />
                            </a>
                            <a href="#" className="bg-white p-2 rounded-lg transition-colors duration-200">
                                <Youtube className="h-4 w-4 md:h-6 md:w-6 text-amber-950" />
                            </a>
                            <a href="#" className="bg-white p-2 rounded-lg transition-colors duration-200">
                                <Facebook className="h-4 w-4 md:h-6 md:w-6 text-amber-950" />
                            </a>
                        </div>

                        {/* Address Information */}
                        <address className="not-italic text-sm text-center md:text-start">
                            <p className="mb-1">47 Ushindi Street, Mikocheni B,</p>
                            <p className="mb-1">Dar es Salaam, Tanzania</p>
                            <p className="mb-1">+255713xxx822</p>
                        </address>
                    </div>
                </div>
            </div>

            {/* Footer Pattern */}
            <div
                className="w-full h-[80px] md:h-[130px] lg:h-[190px] 2xl:h-[250px] bg-repeat-x bg-bottom object-bottom"
                style={{
                    backgroundImage: `url(${FooterPattern})`,
                    backgroundSize: '20vw auto' // 4 x 25vw = 100vw (full screen)
                }}
            ></div>

        </footer>
    );
}
