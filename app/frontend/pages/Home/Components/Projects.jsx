import React from 'react';
import SectionPreview from './SectionPreview';

import projectImg1 from '/assets/projects_carousel/project1.png';
import projectImg2 from '/assets/projects_carousel/project2.png';
import projectImg3 from '/assets/projects_carousel/project3.png';
import projectImg4 from '/assets/projects_carousel/project4.png';
import projectFrame from '/assets/projects_carousel/frame.png';

const projectImages = [projectImg1, projectImg2, projectImg3, projectImg4];

const ProjectsPreview = () => {
  return (
    <SectionPreview
      bgColor="bg-primarygreen"
      title="Projects"
      description="In every community, creative change-makers are building African solutions for African challenges—because no one understands our needs better than we do. These projects reflect innovation, impact, and local empowerment."
      images={projectImages}
      frame={projectFrame}
      primaryButtonText="View All Projects"
      secondaryButtonText="Register A Project"
      imageLeft={true}
    />
  );
};

export default ProjectsPreview;

