import { useState, useEffect, useRef, useCallback } from "react";
import { <PERSON><PERSON>ef<PERSON>, ArrowRight } from "lucide-react";
import UbuntuCard from "./UbuntuCard";
import UbuntuPageCard from "../../../components/pageSpecific/ubuntu/UbuntuPageCard";
import profileImage from "/assets/ubuntu_assets/profile.png";
import { Link, router } from "@inertiajs/react";

// UbuntuPreview component
function UbuntuPreview({ forums, bookmarkedForumIds }) {

    // const { bookmarkedForumIds } = usePage().props;

    const ubuntuData = forums.map(forum => ({
        id: forum.id,
        title: forum.title,
        description: forum.description,
        categories: ["Co-creator", "Environment", "Innovation Hub"],
        members: ["profile1.png", "profile2.png", "profile3.png"],
        initialBookmarked: bookmarkedForumIds.includes(forum.id),
        // profileImage: forum.profile_image
    }))

    const [currentSlide, setCurrentSlide] = useState(0);
    // Initialize with a default, will be updated by useEffect
    const [itemsPerPage, setItemsPerPage] = useState(1);
    const [displayData, setDisplayData] = useState([]);
    const [isTransitioning, setIsTransitioning] = useState(false);
    const sliderRef = useRef(null);
    const totalOriginalItems = forums.length;

    // Effect to update itemsPerPage based on screen size
    useEffect(() => {
        const checkScreenSize = () => {
            const width = window.innerWidth;
            let itemsPerPage = 1;
            // logic for sm (1), md (2), and lg+ (3) breakpoints
            if (width >= 1024) { // lg
                itemsPerPage = 4;
            } else if (width >= 768) { // md
                itemsPerPage = 3;
            } else { // sm
                itemsPerPage = 1;
            }

            setItemsPerPage(prevItemsPerPage => {
                if (prevItemsPerPage !== itemsPerPage || displayData.length === 0) {
                    const needsCloning = totalOriginalItems > itemsPerPage;
                    let extendedData = [...ubuntuData];
                    if (needsCloning) {
                        const itemsToPrepend = ubuntuData.slice(-itemsPerPage);
                        const itemsToAppend = ubuntuData.slice(0, itemsPerPage);
                        extendedData = [...itemsToPrepend, ...ubuntuData, ...itemsToAppend];
                    } else {
                        extendedData = [...ubuntuData];
                    }

                    setDisplayData(extendedData);

                    if (sliderRef.current) {
                        sliderRef.current.style.transition = 'none';
                    }
                    const initialSlideIndex = needsCloning ? itemsPerPage : 0;
                    setCurrentSlide(initialSlideIndex);

                    setTimeout(() => {
                        if (sliderRef.current) {
                            sliderRef.current.style.transition = 'transform 0.3s ease-in-out';
                        }
                    }, 50);
                }
                return itemsPerPage;
            });
        };

        window.addEventListener('resize', checkScreenSize);
        checkScreenSize();

        return () => window.removeEventListener('resize', checkScreenSize);
    }, [totalOriginalItems, displayData.length]);

    // Calculate the percentage width of a single item slot
    const itemWidthPercentage = 100 / itemsPerPage;
    // Calculate the total translation based on the current item index
    const translationPercentage = currentSlide * itemWidthPercentage;

    const handleNavigation = useCallback((direction) => {
        if (isTransitioning || totalOriginalItems <= itemsPerPage) return;

        setIsTransitioning(true);

        setCurrentSlide(prev => {
            const newSlide = direction === 'next' ? prev + 1 : prev - 1;
            return newSlide;
        });
    }, [isTransitioning, totalOriginalItems, itemsPerPage]);

    const nextSlide = () => handleNavigation('next');
    const prevSlide = () => handleNavigation('prev');

    const handleTransitionEnd = () => {
        let needsJump = false;
        let jumpToSlide = currentSlide;

        if (currentSlide <= itemsPerPage - 1 && itemsPerPage < totalOriginalItems) {
            needsJump = true;
            jumpToSlide = currentSlide + totalOriginalItems;
        } else if (currentSlide >= totalOriginalItems + itemsPerPage && itemsPerPage < totalOriginalItems) {
            needsJump = true;
            jumpToSlide = currentSlide - totalOriginalItems;
        }

        if (needsJump && sliderRef.current) {
            sliderRef.current.style.transition = 'none';
            setCurrentSlide(jumpToSlide);
            sliderRef.current.offsetHeight;
            sliderRef.current.style.transition = 'transform 0.3s ease-in-out';
        }

        setIsTransitioning(false);
    };

    const disableArrows = totalOriginalItems <= itemsPerPage;

    return (
        <div className="w-full bg-primarymaroon py-12 font-inter px-4 md:px-20">
            {/* Header Section */}
            <div className="text-center mb-8">
                <h1 className="text-2xl md:text-4xl lg:text-6xl text-white font-bold mb-2">Ubuntu</h1>
                <p className="text-base md:text-xl lg:text-2xl text-[#1B0E01] font-bold mb-4">"I am because you are!"</p>
                <p className="text-sm md:text-base lg:text-lg text-white font-normal max-w-4xl mx-auto">
                    Engage with our unconventional community of artists, co-creators and living labs, build networks, collaborate,
                    discuss, and share food for thought – join an existing team or start your own!
                </p>
            </div>

            {/* Carousel Section */}
            <div className="relative flex items-center">
                {/* Left Arrow */}
                <button
                    onClick={prevSlide}
                    disabled={disableArrows || isTransitioning}
                    className={`absolute left-0 top-1/2 -translate-y-1/2 -ml-1 md:-ml-6 bg-white rounded-full p-2 shadow-lg z-10 disabled:opacity-50 disabled:cursor-not-allowed`}
                >
                    <div className="text-primarymaroon">
                        <ArrowLeft size={24} />
                    </div>
                </button>

                {/* Cards Container */}
                <div className="overflow-hidden flex-grow mx-2 md:mx-4">
                    <div
                        ref={sliderRef}
                        className="flex"
                        style={{
                            transform: `translateX(-${translationPercentage}%)`,
                            transition: isTransitioning ? 'transform 0.3s ease-in-out' : 'none',
                        }}
                        onTransitionEnd={handleTransitionEnd}
                    >
                        {displayData.map((forum, index) => (
                            <div
                                key={`${forum.id}-${index}`}
                                className="flex-shrink-0 px-2"
                                // Use explicit width based on itemsPerPage
                                style={{ width: `${itemWidthPercentage}%` }}
                            >
                                <UbuntuPageCard forum={forum} />
                            </div>
                        ))}
                    </div>
                </div>

                {/* Right Arrow */}
                <button
                    onClick={nextSlide}
                    disabled={disableArrows || isTransitioning}
                    className={`absolute right-0 top-1/2 -translate-y-1/2 -mr-1 md:-mr-6 bg-white rounded-full p-2 shadow-lg z-10 disabled:opacity-50 disabled:cursor-not-allowed`}
                >
                    <div className="text-primarymaroon">
                        <ArrowRight size={24} />
                    </div>
                </button>
            </div>
            <div className="text-center my-8">
                <div
                    onClick={() => router.visit('/forums')}
                    className="bg-primarybrown text-white py-3 px-4 lg:px-6 rounded-full w-36 md:w-72 hover:bg-black hover:border-none cursor-pointer transition duration-300 ease-in-out text-xs md:text-sm lg:text-base font-medium whitespace-nowrap
               flex items-center justify-center space-x-2
               mx-auto"
                >
                    <span>View All</span>
                    <ArrowRight size={16} />
                </div>
            </div>

        </div>
    );
}

export default UbuntuPreview;
