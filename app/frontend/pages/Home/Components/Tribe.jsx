import React from 'react';
import mirrorimage from '/assets/mirrorimage.png';
import SectionSeparator from './SectionSeparator';

const TribeSection = () => {
    return (
        <>
            <div className="relative w-full h-[200px] md:h-[350px] flex items-center justify-center overflow-hidden">
                {/* Background with diagonal pattern - using a placeholder image that will be replaced */}
                <div
                    className="absolute inset-0 bg-cover bg-top z-0"
                    style={{
                        backgroundImage: `url(${mirrorimage})`,
                    }}
                />

                {/* Content overlay */}
                <div className="relative z-10 text-center px-4 max-w-4xl">
                    <h2 className="text-amber-800 sm:text-xl md:text-2xl lg:text-3xl font-medium mb-6">
                        Freelancers, changemakers, creatives ...
                    </h2>
                    <h1 className="text-gray-900 text-3xl md:text-4xl lg:text-6xl font-bold">
                        This is where you find your tribe.
                    </h1>
                </div>
            </div>
            <SectionSeparator />
        </>
    );
};

export default TribeSection;