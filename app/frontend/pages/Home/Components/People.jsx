import React from 'react';
import SectionPreview from './SectionPreview';

import peopleImg1 from '/assets/people_carousel/people1.png';
import peopleImg2 from '/assets/people_carousel/people2.png';
import peopleImg3 from '/assets/people_carousel/people3.png';
import peopleImg4 from '/assets/people_carousel/people4.png';
import peopleFrame from '/assets/people_carousel/frame.png';

const peopleImages = [peopleImg1, peopleImg2, peopleImg3, peopleImg4];

const PeoplePreview = () => {
  return (
    <SectionPreview
      bgColor="bg-primaryyellow"
      title="People"
      description="A vibrant community of creatives is ready to connect, collaborate, and co-create. From artists, filmmakers, and photographers to graphic, product, web, and fashion designers — and even architects — BongoHub brings together talents from all walks of creative life."
      images={peopleImages}
      frame={peopleFrame}
      primaryButtonText="Become a BongoHubber"
      secondaryButtonText="Visit People's Page"
      imageLeft={false}
    />
  );
};

export default PeoplePreview;