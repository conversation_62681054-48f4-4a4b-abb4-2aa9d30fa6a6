import React from 'react';
import SectionPreview from './SectionPreview';

import placesImg1 from '/assets/places_carousel/places1.png';
import placesImg2 from '/assets/places_carousel/places2.png';
import placesImg3 from '/assets/places_carousel/places3.png';
import placesImg4 from '/assets/places_carousel/places4.png';
import placesFrame from '/assets/places_carousel/frame.png';

const placesImages = [placesImg1, placesImg2, placesImg3, placesImg4];

const PlacesPreview = () => {
  return (
    <SectionPreview
      bgColor="bg-primaryorange"
      title="Places"
      description="Change makers need safe spaces. Spaces to engage, co-create, invent and learn, These places have exactly what you need: facilities, tools and just the right environment to provide and receive support from like-minded souls…where will you go next?"
      images={placesImages}
      frame={placesFrame}
      primaryButtonText="Continue To Places"
      secondaryButtonText="Register A Place"
      imageLeft={false}
    />
  );
};

export default PlacesPreview;

