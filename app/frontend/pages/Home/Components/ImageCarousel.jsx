import React, { useState, useEffect } from 'react';

// Image Carousel Component
const ImageCarousel = ({ images, frame, fadeDuration, imageInterval, left = false }) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    if (!images || images.length === 0) return;

    const intervalId = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % images.length);
    }, imageInterval);

    return () => clearInterval(intervalId);
  }, [images, imageInterval]);

  const alignmentClass = left ? 'mr-auto' : 'ml-auto';

  return (
    <div className={`relative w-full max-w-screen-sm aspect-square ${alignmentClass}`}>
      {images.map((imgSrc, index) => (
        <img
          key={index}
          src={imgSrc}
          alt={`Carousel Image ${index + 1}`}
          className={`absolute inset-0 w-full h-full object-cover scale-95 transition-opacity ease-in-out ${
            index === currentIndex ? 'opacity-100' : 'opacity-0'
          }`}
          style={{ transitionDuration: `${fadeDuration}ms` }}
        />
      ))}
      {frame && (
        <img
          src={frame}
          alt="Frame"
          className="absolute inset-0 h-full z-10 pointer-events-none scale-x-100"
        />
      )}
    </div>
  );
};

export default ImageCarousel;
