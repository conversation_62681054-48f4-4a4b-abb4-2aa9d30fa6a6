import { useState } from 'react';
import {
  Menu,
  X,
  Home,
  Users,
  Folder,
  MapPin,
  MessageCircleMore,
  Info,
  BookOpen,
  User,
  Plus,
  Bell,
  LogOut,
  LayoutDashboard,
  ChevronDown,
} from 'lucide-react';
import { router, usePage } from '@inertiajs/react';
import { Inertia } from '@inertiajs/inertia';

// Assuming the logo and pattern will be imported from your assets
import BongoHubLogo from '/assets/logo.png';
import PatternStrip from '/assets/headerstrip.png';

export default function Header({ userData }) {
  const [isOpen, setIsOpen] = useState(false);
  const [isProfileDropdownOpen, setIsProfileDropdownOpen] = useState(false);
  const { url } = usePage();

  let user = null;

  if (userData) {
    user = {
      name: userData.first_name || null,
      email: userData.email || null,
      profile_picture: userData.avatar_url || null,
    };
  }

  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };

  const toggleProfileDropdown = () => {
    setIsProfileDropdownOpen(!isProfileDropdownOpen);
  };

  // Function to determine if a link is active based on the current Inertia URL
  const isActiveLink = (pathname) => {
    const currentPathname = new URL(url, window.location.origin).pathname;
    return currentPathname === pathname;
  };

  return (
    <>
      <header className="fixed top-0 left-0 right-0 z-50 bg-white w-full">
        <div className=" px-6 2xl:px-20 flex justify-between items-center">
          {/* Logo */}
          <div className="flex-shrink-0 h-full">
            <a >
              <img
                src={BongoHubLogo}
                alt="Bongo Hub Logo"
                className="md:h-20 h-15 object-contain"
                onClick={() => router.visit('/')}
              />
            </a>
          </div>

          {/* Desktop Navigation */}
          <nav className="font-inter hidden md:flex items-center space-x-4">
            <a
              onClick={() => router.visit('/')}
              className={`text-black hidden lg:flex font-semibold text-sm 2xl:text-lg rounded-full transition-colors duration-200 hover:bg-primarybrown hover:text-white px-3 py-1 ${isActiveLink('/') ? 'bg-primarybrown text-white' : ''
                }`}
            >
              Home
            </a>
            <a
              onClick={() => router.visit('/people')}
              className={`text-black font-semibold text-sm 2xl:text-lg rounded-full transition-colors duration-200 hover:bg-primaryyellow hover:text-white px-3 py-1 ${isActiveLink('/people') ? 'bg-primaryyellow text-white' : ''
                }`}
            >
              People
            </a>
            <a
              onClick={() => router.visit('/projects')}
              className={`text-black font-semibold text-sm 2xl:text-lg rounded-full transition-colors duration-200 hover:bg-primarygreen hover:text-white px-3 py-1 ${isActiveLink('/projects') ? 'bg-primarygreen text-white' : ''
                }`}
            >
              Projects
            </a>
            <a
              onClick={() => router.visit('/places')}
              className={`text-black font-semibold text-sm 2xl:text-lg rounded-full transition-colors duration-200 hover:bg-primaryorange hover:text-white px-3 py-1 ${isActiveLink('/places') ? 'bg-primaryorange text-white' : ''
                }`}
            >
              Places
            </a>
            <a
              onClick={() => router.visit('/forums')}
              className={`text-black font-semibold text-sm 2xl:text-lg rounded-full transition-colors duration-200 hover:bg-primarymaroon hover:text-white px-3 py-1 ${isActiveLink('/forums') ? 'bg-primarymaroon text-white' : ''
                }`}
            >
              Ubuntu
            </a>
            <a
              href="#"
              className="text-black font-semibold text-sm 2xl:text-lg rounded-full transition-colors duration-200 hover:bg-primarybrown hover:text-white px-3 py-1"
            >
              About
            </a>
            <a
              href="#"
              className="text-black font-semibold text-sm 2xl:text-lg rounded-full transition-colors duration-200 hover:bg-primarybrown hover:text-white px-3 py-1"
            >
              Blog
            </a>
          </nav>

          {/* Conditional Rendering: User Profile or Login/Signup Buttons */}
          {user ? (
            /* Logged In User Section */
            <div className="hidden lg:flex items-center space-x-4">
              {/* Bell Icon for Notifications */}
              <button
                onClick={() => router.visit('/profile?section=newsfeed')}
                className="relative p-2 text-gray-600 hover:text-primarybrown transition-colors duration-200"
              >
                <Bell size={20} className="2xl:w-6 2xl:h-6" />
                {/* Optional: Notification badge */}
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
                  3
                </span>
              </button>

              {/* Profile Picture with Dropdown */}
              <div className="relative">
                <button
                  onClick={toggleProfileDropdown}
                  className="flex items-center space-x-2 focus:outline-none"
                >
                  <ChevronDown size={16} className="text-gray-600" />
                  <img
                    src={user.profile_picture || '/assets/default-avatar.png'}
                    alt={
                      user.name
                        ? user.name
                          .split(' ')
                          .map((word) => word[0])
                          .join('')
                          .toUpperCase()
                        : 'User'
                    }
                    className="w-8 h-8 2xl:w-10 2xl:h-10 rounded-full object-cover border-2 border-gray-200 hover:border-primarybrown transition-colors duration-200"
                  />
                </button>

                {/* Profile Dropdown */}
                {isProfileDropdownOpen && (
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border px-5 border-gray-100 py-2 z-50">
                    <a
                      onClick={() => {
                        router.visit('/profile')
                        toggleProfileDropdown();
                      }}
                      className="flex items-center px-4 py-2 text-sm text-white bg-primarybrown hover:text-white transition-colors duration-200 rounded-xl mb-2"
                    >
                      Dashboard
                    </a>
                    <button
                      onClick={() => {
                        Inertia.delete("/users/sign_out");
                      }}
                      className="flex items-center w-full px-4 py-2 text-sm text-white bg-primarybrown hover:text-red-600 transition-colors duration-200 rounded-xl"
                    >
                      Sign out
                    </button>
                  </div>
                )}
              </div>
            </div>
          ) : (
            /* Desktop Login/Signup Buttons for Non-logged Users */
            <div className="hidden lg:flex items-center space-x-4">
              <a
                href="/users/sign_in"
                className="px-6 py-2 font-semibold cursor-pointer text-sm 2xl:text-lg rounded-full border-2 border-bordergray text-black hover:bg-black hover:text-white transition duration-300"
              >
                Login
              </a>
              <a
                href="/users/sign_up"
                className="px-6 py-2 font-semibold text-sm 2xl:text-lg rounded-full bg-primarybrown text-white hover:bg-black hover:text-white transition duration-300"
              >
                Join Now
              </a>
            </div>
          )}

          {/* Mobile Menu Button */}
          <button className="lg:hidden text-gray-800" onClick={toggleMenu}>
            {isOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isOpen && (
          <div className="lg:hidden fixed right-0 top-15 md:top-20 w-64 bg-white py-4 px-4 shadow-xl rounded-b-lg border-l border-b border-gray-100">
            <nav className="md:hidden flex flex-col space-y-3">
              <a
                onClick={() => router.visit('/')}
                className={`flex items-center text-black font-semibold text-sm rounded-full transition-colors duration-200 hover:bg-primarybrown hover:text-white px-3 py-2 ${isActiveLink('/') ? 'bg-primarybrown text-white' : ''
                  }`}
              >
                <Home className="w-5 h-5 mr-2" />
                Home
              </a>
              <a
                onClick={() => router.visit('/people')}
                className={`flex items-center text-black font-semibold text-sm rounded-full transition-colors duration-200 hover:bg-primaryyellow hover:text-white px-3 py-2 ${isActiveLink('/people') ? 'bg-primaryyellow text-white' : ''
                  }`}
              >
                <Users className="w-5 h-5 mr-2" />
                People
              </a>
              <a
                onClick={() => router.visit('/projects')}
                className={`flex items-center text-black font-semibold text-sm rounded-full transition-colors duration-200 hover:bg-primarygreen hover:text-white px-3 py-2 ${isActiveLink('/projects') ? 'bg-primarygreen text-white' : ''
                  }`}
              >
                <Folder className="w-5 h-5 mr-2" />
                Projects
              </a>
              <a
                onClick={() => router.visit('/places')}
                className={`flex items-center text-black font-semibold text-sm rounded-full transition-colors duration-200 hover:bg-primaryorange hover:text-white px-3 py-2 ${isActiveLink('/places') ? 'bg-primaryorange text-white' : ''
                  }`}
              >
                <MapPin className="w-5 h-5 mr-2" />
                Places
              </a>
              <a
                onClick={() => router.visit('/forums')}
                className={`flex items-center text-black font-semibold text-sm rounded-full transition-colors duration-200 hover:bg-primarymaroon hover:text-white px-3 py-2 ${isActiveLink('/forums') ? 'bg-primarymaroon text-white' : ''
                  }`}
              >
                <MessageCircleMore className="w-5 h-5 mr-2" />
                Ubuntu
              </a>
              <a
                href="#"
                className="flex items-center text-black font-semibold text-sm rounded-full transition-colors duration-200 hover:bg-primarybrown hover:text-white px-3 py-2"
              >
                <Info className="w-5 h-5 mr-2" />
                About
              </a>
              <a
                href="#"
                className="flex items-center text-black font-semibold text-sm rounded-full transition-colors duration-200 hover:bg-primarybrown hover:text-white px-3 py-2"
              >
                <BookOpen className="w-5 h-5 mr-2" />
                Blog
              </a>
            </nav>

            {/* Mobile User Section or Login/Signup */}
            {user ? (
              <div className="flex flex-col space-y-3 mt-3 pt-3 border-t border-gray-200">
                <div className="flex items-center space-x-3 px-3 py-2">
                  <img
                    src={user.profile_picture || '/assets/default-avatar.png'}
                    alt={user.name || 'User'}
                    className="w-8 h-8 rounded-full object-cover border-2 border-gray-200"
                  />
                </div>
                <a
                  onClick={() => {
                    router.visit('/profile')
                    toggleProfileDropdown();
                  }}
                  className="flex items-center px-3 py-2 text-sm text-white bg-primarybrown hover:text-white rounded-full transition-colors duration-200"
                >
                  Dashboard
                </a>
                <button
                  onClick={() => {
                    Inertia.delete('/users/sign_out');
                  }}
                  className="flex items-center px-3 py-2 text-sm text-white bg-primarybrown hover:text-white rounded-full transition-colors duration-200"
                >
                  Sign out
                </button>
              </div>
            ) : (
              <div className="flex flex-col space-y-3 mt-3">
                <a
                  href="/users/sign_in"
                  className="flex items-center justify-center px-6 py-2 rounded-full cursor-pointer border-2 border-gray-300 text-black hover:bg-black hover:text-white transition duration-300"
                >
                  <User className="w-5 h-5 mr-2" />
                  Login
                </a>
                <a
                  href="/users/sign_up"
                  className="flex items-center justify-center px-6 py-2 rounded-full bg-primarybrown text-white hover:bg-black hover:text-white transition duration-300"
                >
                  <Plus className="w-5 h-5 mr-2" />
                  Join Now
                </a>
              </div>
            )}
          </div>
        )}

        {/* Pattern Strip */}
        <div className="w-full">
          <img
            src={PatternStrip}
            alt="Decorative pattern"
            className="w-full h-3 md:h-4"
          />
        </div>
      </header>
    </>
  );
}
