// Secondary Action Button Component
const SecondaryActionBtn = ({ text, onClick }) => (
  <button
    className="border border-black text-black py-3 px-4 lg:px-6 rounded-full w-full md:w-72 bg-transparent hover:bg-black hover:text-white hover:cursor-pointer transition duration-300 ease-in-out text-xs md:text-sm lg:text-base font-medium whitespace-nowrap"
    onClick={onClick}
  >
    {text}
  </button>
);

export default SecondaryActionBtn;