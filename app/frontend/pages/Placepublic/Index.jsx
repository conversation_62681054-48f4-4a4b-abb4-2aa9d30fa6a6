import React from 'react';
import Layout from '../../components/shared/Layout';
import ProfilePage from '../../components/pageSpecific/places&projects/Base';
// Main Profile Page Component
const PlacesPublicPage = ({ pageData, bookmarkedPlaceIds }) => {
  const Data = {
    theme: 'places',
    profile: {
      id: pageData.id,
      title: pageData.title,
      description: pageData.description,
      location: pageData.location,
      rating: 4.3,
      profileImage: pageData.profileImage,
      owner: pageData.owner.first_name,
      ownerId: pageData.ownerId,
      contributors: pageData.collaborators.map(collaborator => collaborator.image),
    },
    categories: pageData.tags.map(tag => ({
      name: tag,
      icon: '🏷️'
    })) || [],
    activities: pageData.what_we_do.map(tag => ({
      name: tag
    })) || [],
    gallery: pageData.images || [],
    bookmarkedProfileIds: bookmarkedPlaceIds,
  };

  return (
    <>
      <ProfilePage pageData={Data} />
    </>
  );
};

PlacesPublicPage.layout = (page) => <Layout children={page} title="Profile Page" userData={page.props.user} />;

export default PlacesPublicPage;