@import 'tailwindcss';

@plugin "@tailwindcss/typography";
@plugin "@tailwindcss/forms";
@source "../../../app/views/devise/**/*.html.erb";
@theme {
  --color-primaryyellow: #ebe354;
  --color-borderyellow: #fbf693;
  --color-yellowhover: #fbf693;
  --color-yellowclicked: #fffcbb;
  --color-primarygreen: #01bda7;
  --color-bordergreen: #03dec4;
  --color-greenhover: #00d8bf;
  --color-greenclicked: #03dec4;
  --color-messagegreen: #2dbe73;
  --color-settingsblue: #355c7d;
  --color-primarymaroon: #bc2932;
  --color-bordermaroon: #da1d29;
  --color-maroonhover: #da1d29;
  --color-maroonclicked: #da1d29;
  --color-primaryorange: #ffa210;
  --color-borderorange: #e29112;
  --color-orangehover: #e29112;
  --color-orangeclicked: #ca8211;
  --color-primarybrown: #633100;
  --color-primarybrown-transparent: rgba(99, 49, 0, 0.5);
  --color-brownhover: #804001;
  --color-brownclicked: #9d4f00;
  --color-bordergray: #4c4c4c9c;
}

@theme {
  --font-inter: 'Inter', sans-serif;
}

@tailwind utilities;

@layer components {
  .bg-brown {
    @apply bg-[#3D1F00];
  }
  .text-brown {
    @apply text-[#3D1F00];
  }
  .bg-brown-transparent {
    @apply bg-[rgba(61,31,0,0.5)];
  }
  .focus\:ring-brown:focus {
    @apply ring-[#3D1F00];
  }
  .focus\:border-brown:focus {
    @apply border-[#3D1F00];
  }
  .text-red {
    @apply text-[#BC2932];
  }
}

@layer base {
  button, a {
    @apply cursor-pointer;
  }
}
