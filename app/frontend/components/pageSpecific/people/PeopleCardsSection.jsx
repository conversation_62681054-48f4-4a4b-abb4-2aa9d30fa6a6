import React from 'react';
import { usePage } from '@inertiajs/react';
import PeopleCard from './PeopleCard';

export default function PeopleCardsSection() {
  const { profiles, bookmarkedProfileIds } = usePage().props;

  const transform = (profile) => ({
    userId:            profile.user_id,
    profileId:         profile.id,
    name:              profile.name,
    profession:        profile.profession,
    address:           profile.address,
    verified:          profile.verified,
    views:             profile.views,
    images:            [profile.avatar_url],
    rating:            profile.rating,
    location:          profile.location,
    initialBookmarked: bookmarkedProfileIds.includes(profile.id),
  });

  return (
    <div className="w-full px-2 2xl:px-24 py-6 sm:p-8 shadow-inner">
      {profiles.length > 0 ? (
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-2 md:gap-4">
          {profiles.map((profile) => (
            <PeopleCard key={profile.id} person={transform(profile)} />
          ))}
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center text-center p-6 bg-yellow-100 text-yellow-800 rounded-md shadow-md">
          <h3 className="text-xl font-semibold mb-2">Oops!</h3>
          <p className="text-base">
            Looks like we came up empty. Maybe give it another shot with different words?
          </p>
        </div>
      )}
    </div>
  );
}
