// app/frontend/components/pageSpecific/people/PeopleCard.jsx
import React, { useState, useEffect, useRef } from 'react';
import { Link } from '@inertiajs/react';
import { MapPin, Bookmark as BmIcon, BadgeCheck, Eye } from 'lucide-react';
import { useBookmark } from '../../../custom_hooks/bookmark';
import UnbookmarkModal from '../../shared/UnbookmarkModal';

export default function PeopleCard({ person }) {
 
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isHovering, setIsHovering] = useState(false);
  const [bookmarkHover, setBookmarkHover] = useState(false);

  const {
    bookmarked,
    toggleBookmark,
    showUnbookmarkModal,
    confirmUnbookmark,
    cancelUnbookmark,
  } = useBookmark({
    id: person.profileId,
    type: 'Profile',
    initialBookmarked: person.initialBookmarked,
  });

  const cardRef = useRef(null);

  useEffect(() => {
    let interval;
    if (isHovering && person.images?.length > 1) {
      interval = setInterval(() => {
        setCurrentImageIndex(i =>
          i === person.images.length - 1 ? 0 : i + 1
        );
      }, 2000);
    }
    return () => clearInterval(interval);
  }, [isHovering, person.images]);

  const renderStars = rating => {
    const stars = [];
    const fullStars = Math.round(rating);
    const hasHalf   = Math.round(rating * 2) / 2 > fullStars;
    for (let i = 0; i < 5; i++) {
      if (i < fullStars || (i === fullStars && hasHalf)) {
        stars.push(<span key={i} className="text-yellow-400">★</span>);
      } else {
        stars.push(<span key={i} className="text-gray-300">★</span>);
      }
    }
    return stars;
  };

  return (
    <>
      <Link href={`/profile/${person.userId}`} className="no-underline">
        <div
          className="relative rounded-lg overflow-hidden text-inter shadow-sm bg-white cursor-pointer"
          onMouseEnter={() => setIsHovering(true)}
          onMouseLeave={() => {
            setIsHovering(false);
            setCurrentImageIndex(0);
          }}
          ref={cardRef}
        >
          {/* Image slideshow */}
          <div className="relative h-36 md:h-60 w-full bg-gray-100 z-5">
            {person.images?.map((src, idx) => (
              <img
                key={idx}
                src={src}
                alt={`${person.name} sample ${idx + 1}`}
                className="absolute top-0 left-0 w-full h-full object-cover transition-opacity duration-1000 ease-in-out"
                style={{
                  opacity: currentImageIndex === idx ? 1 : 0,
                  zIndex:   currentImageIndex === idx ? 10 : 1,
                }}
              />
            ))}

            {/* Bookmark button */}
            <button
              type="button"
              onClick={toggleBookmark}
              onMouseEnter={() => setBookmarkHover(true)}
              onMouseLeave={() => setBookmarkHover(false)}
              className={`absolute top-1 md:top-2 right-1 md:right-2 p-0.5 md:p-1 rounded-md md:rounded-lg z-20 transition-all duration-200 transform hover:scale-110 cursor-pointer ${
                bookmarked
                  ? 'bg-primaryyellow text-black shadow-md'
                  : bookmarkHover
                  ? 'bg-primaryyellow text-black shadow-md'
                  : 'bg-white/80 text-gray-600 hover:bg-white'
              }`}
              aria-label={bookmarked ? 'Remove bookmark' : 'Add bookmark'}
            >
              <BmIcon size={24} className={bookmarked ? 'fill-current' : ''} />
            </button>

            {/* Indicator dots */}
            <div className="absolute bottom-2 left-0 right-0 flex justify-center space-x-1">
              {person.images?.map((_, idx) => (
                <div
                  key={idx}
                  className={`w-2 h-2 rounded-full transition-colors duration-300 ${
                    currentImageIndex === idx ? 'bg-primaryyellow' : 'bg-gray-300'
                  }`}
                />
              ))}
            </div>
          </div>

          {/* Card content */}
          <div className="p-2 sm:p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <h3 className="text-base md:text-lg font-semibold">{person.name}</h3>
                {person.verified && (
                  <BadgeCheck size={16} className="ml-1 text-green-500" />
                )}
              </div>
              <div className="flex items-center bg-primaryyellow text-black-600 px-1 sm:px-2 py-1 rounded-full">
                <Eye size={16} className="mr-1" />
                <span className="text-xs sm:text-sm">{person.views}</span>
              </div>
            </div>

            <p className="text-black-600 text-sm mt-1">{person.profession}</p>

            <div className="flex items-center mt-1 sm:mt-2 text-gray-600">
              <MapPin size={16} className="mr-1" />
              <span className="text-xs sm:text-sm">{person?.location?.address}</span>
            </div>

            <div className="flex items-center mt-1 sm:mt-2">
              <div className="flex">{renderStars(person.rating)}</div>
              <span className="ml-1 text-xs sm:text-sm text-gray-600">(0)</span>
            </div>
          </div>
        </div>
      </Link>

      {/* Unbookmark confirmation */}
      <UnbookmarkModal
        isOpen={showUnbookmarkModal}
        onClose={cancelUnbookmark}
        onConfirm={confirmUnbookmark}
        name={person.name}
      />
    </>
  );
}
