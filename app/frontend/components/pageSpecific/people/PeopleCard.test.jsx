import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, act } from '@testing-library/react';
import PeopleCard from './PeopleCard';

describe('PeopleCard Component', () => {
  const mockPerson = {
    name: '<PERSON>',
    domain: 'Portrait Photography',
    location: 'New York, USA',
    rating: 4.5,
    verified: true,
    views: 23,
    images: [
      '/sample-image-1.jpg',
      '/sample-image-2.jpg',
      '/sample-image-3.jpg',
    ],
  };

  // Mock the resize observer
  beforeEach(() => {
    vi.useFakeTimers();
    global.ResizeObserver = vi.fn().mockImplementation(() => ({
      observe: vi.fn(),
      unobserve: vi.fn(),
      disconnect: vi.fn(),
    }));
  });

  afterEach(() => {
    vi.restoreAllMocks();
    vi.useRealTimers();
  });

  it('renders the person information correctly', () => {
    render(<PeopleCard person={mockPerson} />);
    
    expect(screen.getByText('<PERSON>')).toBeInTheDocument();
    expect(screen.getByText('Portrait Photography')).toBeInTheDocument();
    expect(screen.getByText('New York, USA')).toBeInTheDocument();
    expect(screen.getByText('(4.5)')).toBeInTheDocument();
    expect(screen.getByText('23k')).toBeInTheDocument();
  });

  it('renders the correct number of images', () => {
    render(<PeopleCard person={mockPerson} />);
    
    const images = screen.getAllByAltText(/Jane Doe photography sample/);
    expect(images).toHaveLength(3);
  });

  it('shows the first image by default', () => {
    render(<PeopleCard person={mockPerson} />);
    
    const firstImage = screen.getByAltText('Jane Doe photography sample 1');
    expect(firstImage).toHaveStyle('opacity: 1'); // First image should be visible
    
    const otherImages = screen.getAllByAltText(/Jane Doe photography sample [23]/);
    for (const img of otherImages) {
      expect(img).toHaveStyle('opacity: 0'); // Other images should be hidden
    }
  });

  it('shows slideshow indicator dots equal to number of images', () => {
    render(<PeopleCard person={mockPerson} />);
    
    const dots = document.querySelectorAll('.w-2.h-2.rounded-full');
    expect(dots).toHaveLength(mockPerson.images.length);
  });

  it('first dot should be active by default', () => {
    render(<PeopleCard person={mockPerson} />);
    
    const dots = document.querySelectorAll('.w-2.h-2.rounded-full');
    expect(dots[0]).toHaveClass('bg-primaryyellow'); // First dot should be active
    expect(dots[1]).toHaveClass('bg-gray-300'); // Others should be inactive
    expect(dots[2]).toHaveClass('bg-gray-300');
  });

  it('changes images when hovering', async () => {
    render(<PeopleCard person={mockPerson} />);
    
    const card = screen.getByText('Jane Doe').closest('div.rounded-lg');
    
    // Trigger mouse enter
    fireEvent.mouseEnter(card);
    
    // Fast-forward time to trigger the first image change
    act(() => {
      vi.advanceTimersByTime(2000);
    });
    
    // After 2 seconds, the second image should be visible
    const images = screen.getAllByAltText(/Jane Doe photography sample/);
    expect(images[1]).toHaveStyle('opacity: 1');
    expect(images[0]).toHaveStyle('opacity: 0');
    expect(images[2]).toHaveStyle('opacity: 0');
    
    // And the second dot should be active
    const dots = document.querySelectorAll('.w-2.h-2.rounded-full');
    expect(dots[1]).toHaveClass('bg-primaryyellow');
    expect(dots[0]).toHaveClass('bg-gray-300');
    expect(dots[2]).toHaveClass('bg-gray-300');
  });

  it('stops and resets slideshow when mouse leaves', async () => {
    render(<PeopleCard person={mockPerson} />);
    
    const card = screen.getByText('Jane Doe').closest('div.rounded-lg');
    
    // Trigger mouse enter to start slideshow
    fireEvent.mouseEnter(card);
    
    // Advance time to show the second image
    act(() => {
      vi.advanceTimersByTime(2000);
    });
    
    // Then trigger mouse leave to stop slideshow
    fireEvent.mouseLeave(card);
    
    // The first image should be shown again when mouse leaves
    const images = screen.getAllByAltText(/Jane Doe photography sample/);
    expect(images[0]).toHaveStyle('opacity: 1');
    expect(images[1]).toHaveStyle('opacity: 0');
    expect(images[2]).toHaveStyle('opacity: 0');
  });

  it('renders stars correctly based on rating', () => {
    render(<PeopleCard person={mockPerson} />);
    
    // For a 4.5 rating, we should have 5 total stars (4.5 rounded to 5 full stars)
    const yellowStars = document.querySelectorAll('.text-yellow-400');
    const grayStars = document.querySelectorAll('.text-gray-300');
    
    expect(yellowStars).toHaveLength(5);
    expect(grayStars).toHaveLength(0);
    
    // Test with a different rating
    const lowerRatedPerson = { ...mockPerson, rating: 3.2 };
    render(<PeopleCard person={lowerRatedPerson} />);
    
    const updatedYellowStars = document.querySelectorAll('.text-yellow-400');
    const updatedGrayStars = document.querySelectorAll('.text-gray-300');
    
    expect(updatedYellowStars).toHaveLength(8);
    expect(updatedGrayStars).toHaveLength(2); 
  });
});