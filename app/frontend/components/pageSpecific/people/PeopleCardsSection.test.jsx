import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import PeopleCardsSection from './PeopleCardsSection';
import { Link } from '@inertiajs/react';

// Mock the PeopleCard component
vi.mock('./PeopleCard', () => ({
  default: vi.fn(() => <div data-testid="people-card">Mocked PeopleCard</div>)
}));

import PeopleCard from './PeopleCard';

describe('PeopleCardsSection Component', () => {
  // Sample test data that matches your new component structure
  const mockProfiles = [
    {
      id: "1",
      name: "<PERSON>",
      domain: "Photographer",
      location: "Nairobi, Kenya",
      rating: 4.8,
      views: 2.1,
      verified: true,
      avatar_url: "https://example.com/alice.jpg"
    },
    {
      id: "2",
      name: "<PERSON>",
      domain: "Designer",
      location: "Mombasa, Kenya",
      rating: 4.5,
      views: 1.5,
      verified: false,
      avatar_url: "https://example.com/bob.jpg"
    },
    {
      id: "3",
      name: "<PERSON>",
      domain: "Developer",
      location: "Kisumu, Kenya",
      rating: 4.9,
      views: 3.2,
      verified: true,
      avatar_url: "https://example.com/carol.jpg"
    }
  ];

  beforeEach(() => {
    // Clear mock call history before each test
    vi.clearAllMocks();
  });

  it('renders the correct number of PeopleCard components based on profiles prop', () => {
    render(<PeopleCardsSection profiles={mockProfiles} />);
    
    const cardElements = screen.getAllByTestId('people-card');
    expect(cardElements).toHaveLength(3);
  });

  it('passes the correct props to each PeopleCard', () => {
    render(<PeopleCardsSection profiles={mockProfiles} />);
    
    // Check that PeopleCard was called correct number of times
    expect(PeopleCard).toHaveBeenCalledTimes(3);
    
    // Verify the props passed to the first PeopleCard call
    const expectedFirstPersonProps = {
      person: {
        id: "1",
        name: "Alice Johnson",
        domain: "Photographer",
        location: "Nairobi, Kenya",
        rating: 4.8,
        views: 2.1,
        verified: true,
        images: ["https://example.com/alice.jpg"]
      }
    };
    
    expect(PeopleCard).toHaveBeenNthCalledWith(1, expect.objectContaining(expectedFirstPersonProps), expect.anything());
    
    // Verify the props passed to the second PeopleCard call
    const expectedSecondPersonProps = {
      person: {
        id: "2",
        name: "Bob Smith",
        domain: "Designer",
        location: "Mombasa, Kenya",
        rating: 4.5,
        views: 1.5,
        verified: false,
        images: ["https://example.com/bob.jpg"]
      }
    };
    
    expect(PeopleCard).toHaveBeenNthCalledWith(2, expect.objectContaining(expectedSecondPersonProps), expect.anything());
  });

  it('handles empty profiles array by showing "No Match Found" message', () => {
    render(<PeopleCardsSection profiles={[]} />);
    
    expect(screen.getByText('Looks like we came up empty. Maybe give it another shot with different words?')).toBeInTheDocument();
    expect(screen.queryByTestId('people-card')).not.toBeInTheDocument();
  });

  it('handles undefined profiles prop gracefully', () => {
    // This test checks what happens if profiles prop is not provided
    expect(() => render(<PeopleCardsSection />)).toThrow();
  });

  it('assigns unique keys to each PeopleCard component', () => {
    const { container } = render(<PeopleCardsSection profiles={mockProfiles} />);
    
    // Check that we have the correct number of direct child elements in the grid container
    const gridContainer = container.querySelector('.grid');
    expect(gridContainer.children).toHaveLength(3);
  });

  it('applies the correct grid classes for responsive layout', () => {
    const { container } = render(<PeopleCardsSection profiles={mockProfiles} />);
    
    const wrapper = container.firstChild;
    expect(wrapper).toHaveClass('w-full');
    expect(wrapper).toHaveClass('px-2');
    expect(wrapper).toHaveClass('2xl:px-24');
    
    const grid = wrapper.firstChild;
    expect(grid).toHaveClass('grid');
    expect(grid).toHaveClass('grid-cols-2');
    expect(grid).toHaveClass('sm:grid-cols-3');
    expect(grid).toHaveClass('lg:grid-cols-5');
    expect(grid).toHaveClass('gap-2');
    expect(grid).toHaveClass('md:gap-4');
  });

  it('renders section with shadow-inner class', () => {
    const { container } = render(<PeopleCardsSection profiles={mockProfiles} />);
    
    const wrapper = container.firstChild;
    expect(wrapper).toHaveClass('shadow-inner');
  });

  it('renders section with correct padding classes', () => {
    const { container } = render(<PeopleCardsSection profiles={mockProfiles} />);
    
    const wrapper = container.firstChild;
    expect(wrapper).toHaveClass('py-6');
    expect(wrapper).toHaveClass('sm:p-8');
  });

  it('renders Link components with correct href', () => {
    render(<PeopleCardsSection profiles={mockProfiles} />);
    
    const links = screen.getAllByRole('link');
    expect(links).toHaveLength(mockProfiles.length);
    expect(links[0]).toHaveAttribute('href', '/profile/1');
  });

  it('correctly transforms profile data to person data format', () => {
    render(<PeopleCardsSection profiles={mockProfiles} />);
    
    // Check that the avatar_url is correctly transformed to images array
    const firstCallProps = PeopleCard.mock.calls[0][0];
    expect(firstCallProps.person.images).toEqual(["https://example.com/alice.jpg"]);
    
    // Verify all expected properties are mapped correctly
    expect(firstCallProps.person).toHaveProperty('id');
    expect(firstCallProps.person.id).toBe("1");
    expect(firstCallProps.person).toHaveProperty('name');
    expect(firstCallProps.person).toHaveProperty('domain');
    expect(firstCallProps.person).toHaveProperty('location');
    expect(firstCallProps.person).toHaveProperty('rating');
    expect(firstCallProps.person).toHaveProperty('views');
    expect(firstCallProps.person).toHaveProperty('verified');
    expect(firstCallProps.person).toHaveProperty('images');
  });

  it('renders different data for each profile', () => {
    render(<PeopleCardsSection profiles={mockProfiles} />);
    
    // Unlike the old test, each call should now have different data
    const firstCallProps = PeopleCard.mock.calls[0][0];
    const secondCallProps = PeopleCard.mock.calls[1][0];
    const thirdCallProps = PeopleCard.mock.calls[2][0];
    
    expect(firstCallProps.person.name).toBe("Alice Johnson");
    expect(secondCallProps.person.name).toBe("Bob Smith");
    expect(thirdCallProps.person.name).toBe("Carol Williams");
    
    // Ensure they're actually different objects
    expect(firstCallProps).not.toEqual(secondCallProps);
    expect(secondCallProps).not.toEqual(thirdCallProps);
  });
});