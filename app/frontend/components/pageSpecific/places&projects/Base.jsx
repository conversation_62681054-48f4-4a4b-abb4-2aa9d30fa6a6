import React, { useState } from "react";
import {
  ArrowRight,
  Bookmark,
  MapPin,
  Users,
  Calendar,
  Star,
} from "lucide-react";
import Background from "/assets/profilebackground.png";
import { Link, router } from "@inertiajs/react";
import { useBookmark } from "../../../custom_hooks/bookmark";
import UnbookmarkModal from "../../shared/UnbookmarkModal";
import Toggle from "../../shared/Toggle";
import { Inertia } from '@inertiajs/inertia'

//Empty state component
const EmptyState = ({ type }) => (
  <div class="flex flex-col items-center justify-center text-center p-6 bg-yellow-100 text-yellow-800 rounded-md shadow-md">
    <h3 class="text-xl font-semibold mb-2">Oops!</h3>
    <p class="text-base">Looks like we came up empty.</p>
  </div>
);



// Profile Header Component
const ProfileHeader = ({
  title,
  description,
  location,
  rating,
  theme,
  owner,
  ownerId,
  profileImage,
  contributors,
  id,
  ismember

}) => (


  <div className=" mb-6">
    <div className="flex flex-col md:flex-row gap-6">
      {/* Left Side - Profile Info */}
      <div
        className={`md:w-1/4 ${theme === "places" ? "bg-[#ffa310b4]" : "bg-[#01BDA7D9]"
          } rounded-xl py-6 flex flex-col items-center justify-center text-center`}
      >
        <div className="flex flex-col items-center">
          <img
            src={profileImage || "/api/placeholder/200/150"}
            alt={title}
            className="w-36 h-36 object-cover rounded-full"
          />
          <h2 className="text-xl font-bold text-black mt-4 mb-2">{title}</h2>
          <div className="flex items-center justify-center text-black text-sm mb-3">
            <div className="flex">
              {[...Array(5)].map((_, i) => (
                <Star
                  key={i}
                  className={`w-4 h-4 ${i < Math.floor(rating)
                      ? "text-black fill-current"
                      : "text-white"
                    }`}
                />
              ))}
            </div>
            <span className="ml-2">{rating}</span>
          </div>

          {ismember ?  (
            <p className="text-gray-500 italic">Already a Member</p>
          )  :

          (
                      <button

          onClick={() => {
  const url = theme === 'projects' ? '/join_project' : '/join_place'
  const data = theme === 'projects' ? { project_id: id } : {}
  Inertia.post(url, data)
}}


          className={`bg-white text-black px-4 py-2 rounded-full text-sm font-medium hover:opacity-90 transition-opacity`}>
            Become A Contributor ↗
          </button>
          )

          }

        </div>
      </div>
      <div
        className={`md:w-3/4 ${theme === "places" ? "bg-[#ffa310b4]" : "bg-[#01BDA7D9]"
          } rounded-xl p-6`}
      >
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-black">
            {theme === "places" ? "Place Description" : "Project Description"}
          </h3>
          <div className="flex items-center gap-4">
            <span className="text-base font-semibold text-white">
              {theme === "places" ? "Place Owner" : "Project Owner"}
            </span>
            <span
              className="bg-white text-black px-6 py-2 hover:cursor-pointer rounded-full text-sm font-semibold"
              onClick={() => {
                const path = `/profile/${ownerId}`
                console.log(path)
                router.visit(path)
              }}
            >
              {owner} ↗
            </span>
          </div>
        </div>

        <p
          className={`text-black text-base ${theme === "places" ? "bg-orange-200" : "bg-teal-200"
            } leading-relaxed p-5 rounded-lg mb-6`}
        >
          {description}
        </p>

        <div className="flex items-center gap-8 text-sm text-black">
          <div className="flex items-center gap-2">
            <MapPin className="w-4 h-4" />
            <span>{location}</span>
          </div>
          <div className="flex items-center gap-2">
            <Calendar className="w-4 h-4" />
            <span>Event Series</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-black">Members:</span>
            {/* Member avatars */}
            <div className="flex -space-x-5">
              {contributors.map((member, index) => (
                <div
                  key={index}
                  className="w-10 h-10 rounded-full overflow-hidden border-2 border-white bg-gray-200"
                >
                  <img
                    src={member}
                    alt={`Member ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                </div>
              ))}
              {/* Member count circle */}
              <div className="w-10 h-10 rounded-full bg-gray-200 border-2 border-white flex items-center justify-center">
                <span className="text-xs font-bold text-black">{contributors.length}+</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
);

// Categories Component
const Categories = ({ categories = [], theme }) => (
  <div
    className={`${theme === "places" ? "bg-[#ffa310b4]" : "bg-[#01BDA7D9]"
      } rounded-lg mb-6 p-6`}
  >
    <h3 className="text-lg font-semibold text-black mb-4">Categories</h3>
    {categories.length > 0 ? (
      <div className="flex flex-wrap gap-3">
        {categories.map((category, index) => (
          <span
            key={index}
            className="bg-white text-black px-4 py-2 rounded-full text-sm font-medium flex items-center gap-2"
          >
            {category.icon && <span>{category.icon}</span>}
            {category.name}
          </span>
        ))}
      </div>
    ) : (
      <EmptyState type="categories" />
    )}
  </div>
);

// What We Do Component
const WhatWeDo = ({ activities = [], theme }) => (
  <div
    className={`${theme === "places" ? "bg-[#ffa310b4]" : "bg-[#01BDA7D9]"
      } rounded-lg mb-6 p-6`}
  >
    <h3 className="text-lg font-semibold text-black mb-4">
      {theme === "places" ? "What we offer" : "What we do"}
    </h3>
    {activities.length > 0 ? (
      <div className="flex flex-wrap gap-3">
        {activities.map((activity, index) => (
          <span
            key={index}
            className="bg-white text-black px-4 py-2 rounded-full text-sm font-medium flex items-center gap-2"
          >
            {activity.icon && <span>{activity.icon}</span>}
            {activity.name}
          </span>
        ))}
      </div>
    ) : (
      <EmptyState type="activities" />
    )}
  </div>
);

// Gallery Component
const Gallery = ({ images = [], theme, title }) => {
  const [showAll, setShowAll] = useState(false);

  const visibleImages = showAll ? images : images.slice(0, 8);
  const hasMore = images.length > 8 && !showAll;

  return (
    <div
      className={`${theme === "places" ? "bg-[#ffa310b4]" : "bg-[#01BDA7D9]"
        } rounded-lg mb-6 p-6`}
    >
      <h3 className="text-lg font-semibold text-black mb-4">{title}</h3>
      {images.length > 0 ? (
        <>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
            {visibleImages.map((image, index) => (
              <img
                key={index}
                src={image || "/api/placeholder/150/120"}
                alt={`Gallery ${index + 1}`}
                className="w-full h-30 md:h-40 lg:h-60 object-cover rounded-lg shadow-md group-hover:shadow-lg transition-shadow"
              />
            ))}
          </div>
          {hasMore && (
            <div className="text-center">
              <button
                className="bg-white text-black px-6 py-2 rounded-md text-sm font-medium hover:bg-gray-50 transition-colors"
                onClick={() => setShowAll(true)}
              >
                Load More
              </button>
            </div>
          )}
        </>
      ) : (
        <EmptyState type="gallery" />
      )}
    </div>
  );
};

// Main Profile Page Component
const ProfilePage = ({ pageData }) => {
  const { theme, profile, categories, activities, gallery } = pageData;

  return (
    <div className="min-h-screen bg-white py-8 px-8 md:px-12 lg:px-20 pt-26 md:pt-30">
      <div
        className="min-h-screen mx-auto rounded-lg overflow-hidden"
        style={{
          backgroundImage: `url(${Background})`,
          backgroundRepeat: "repeat-y",
          backgroundSize: "cover",
        }}
      >
        <Toggle
          pageData={pageData}
          theme={theme}
          backText={
            theme === "places" ? "View More Places" : "View More Projects"
          }
          backLink={theme === "places" ? "/places" : "/projects"}
          title={theme === "places" ? profile.title : profile.title}
        />

        <div className="max-w-9xl mx-auto px-4 py-6">
          <ProfileHeader
            title={profile.title}
            description={profile.description}
            location={profile.location}
            rating={profile.rating}
            theme={theme}
            owner={profile.owner}
            ownerId={profile.ownerId}
            profileImage={profile.profileImage}
            contributors={profile.contributors}
            id={profile.id}
            ismember={profile.ismember}

          />

          <Categories categories={categories} theme={theme} />

          <WhatWeDo activities={activities} theme={theme} />

          <Gallery
            images={gallery}
            theme={theme}
            title={theme === "places" ? "Place Gallery" : "Project Gallery"}
          />
        </div>
      </div>
    </div>
  );
};

export default ProfilePage;
