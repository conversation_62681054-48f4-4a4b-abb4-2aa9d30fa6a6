import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import ProjectsCard from './ProjectsCard';

// Mock the lucide-react icons if they cause issues in testing environment
vi.mock('lucide-react', () => ({
  MapPin: () => <svg data-testid="map-pin-icon" />,
  Bookmark: () => <svg data-testid="bookmark-icon" />,
  Eye: () => <svg data-testid="eye-icon" />,
}));

const mockProject = {
  name: 'Innovative Eco-House',
  location: 'Nairobi, Kenya',
  description: 'A sustainable housing project focusing on renewable energy and eco-friendly materials.',
  images: [
    'https://via.placeholder.com/400x300/FF5733/FFFFFF?text=Image+1',
    'https://via.placeholder.com/400x300/33FF57/FFFFFF?text=Image+2',
    'https://via.placeholder.com/400x300/3357FF/FFFFFF?text=Image+3',
  ],
  views: 1234,
  tags: ['Architecture', 'Sustainability', 'GreenTech'],
  rating: 4.5,
  ratingCount: 8,
  collaborators: [
    { id: '1', name: '<PERSON>', image: 'https://via.placeholder.com/32/FF0000/FFFFFF?text=A' },
    { id: '2', name: 'Bob Johnson', image: 'https://via.placeholder.com/32/00FF00/FFFFFF?text=B' },
    { id: '3', name: '<PERSON> Brown', image: 'https://via.placeholder.com/32/0000FF/FFFFFF?text=C' },
    { id: '4', name: 'Diana Prince', image: 'https://via.placeholder.com/32/FFFF00/000000?text=D' },
    { id: '5', name: 'Eve Adams', image: 'https://via.placeholder.com/32/00FFFF/000000?text=E' },
  ],
};

describe('ProjectsCard', () => {
  beforeEach(() => {
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.runOnlyPendingTimers();
    vi.useRealTimers();
  });

  it('renders project details correctly', () => {
    render(<ProjectsCard project={mockProject} />);

    // Assert project name
    expect(screen.getByRole('heading', { name: /innovative eco-house/i })).toBeInTheDocument();

    // Assert location
    expect(screen.getByText(/nairobi, kenya/i)).toBeInTheDocument();
    expect(screen.getByTestId('map-pin-icon')).toBeInTheDocument();

    // Assert description
    expect(screen.getByText(/a sustainable housing project focusing on renewable energy and eco-friendly materials./i)).toBeInTheDocument();

    // Assert views
    expect(screen.getByText('1234')).toBeInTheDocument();
    expect(screen.getByTestId('eye-icon')).toBeInTheDocument();

    // Assert rating
    expect(screen.getByText('4.5 (8)')).toBeInTheDocument();
    // Use getAllByText for stars, as there are multiple
    expect(screen.getAllByText('★').length).toBeGreaterThanOrEqual(Math.floor(mockProject.rating));
  });

  it('renders correct number of collaborators and "remaining" count', () => {
    render(<ProjectsCard project={mockProject} />);

    // Query for collaborator images specifically within the collaborator section
    const collaboratorImages = screen.getAllByRole('img', { name: /Alice Smith|Bob Johnson|Charlie Brown/ });
    expect(collaboratorImages).toHaveLength(3);
    expect(screen.getByAltText('Alice Smith')).toBeInTheDocument();
    expect(screen.getByAltText('Bob Johnson')).toBeInTheDocument();
    expect(screen.getByAltText('Charlie Brown')).toBeInTheDocument();
    expect(screen.queryByAltText('Diana Prince')).not.toBeInTheDocument();

    // Check the remaining collaborators count
    expect(screen.getByText(/^\+2$/)).toBeInTheDocument(); // Use regex for exact match
  });

  it('does not show remaining collaborators if there are 3 or less', () => {
    const projectWithFewCollaborators = {
      ...mockProject,
      collaborators: mockProject.collaborators.slice(0, 2), // Only 2 collaborators
    };
    render(<ProjectsCard project={projectWithFewCollaborators} />);

    // Query for collaborator images specifically
    const collaboratorImages = screen.getAllByRole('img', { name: /Alice Smith|Bob Johnson/ });
    expect(collaboratorImages).toHaveLength(2);
    expect(screen.queryByText(/^\+\d+$/)).not.toBeInTheDocument(); // No remaining count
  });

  it('bookmark icon is present', () => {
    render(<ProjectsCard project={mockProject} />);
    expect(screen.getByTestId('bookmark-icon')).toBeInTheDocument();
  });

  it('renders correct number of slideshow indicator dots', () => {
    render(<ProjectsCard project={mockProject} />);
    // Select the parent div of the dots, then query within it
    const dotsContainer = screen.getByTestId('slideshow-dots'); // Add data-testid to the dots container
    const dots = dotsContainer.querySelectorAll('div');
    expect(dots).toHaveLength(mockProject.images.length);

    // Additionally, check that the first dot is active initially
    expect(dots[0]).toHaveClass('bg-primarygreen');
    expect(dots[1]).toHaveClass('bg-gray-300');
  });
});
