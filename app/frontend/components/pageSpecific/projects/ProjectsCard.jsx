import { useState, useEffect, useRef } from 'react';
import { MapPin, Bookmark, Eye } from 'lucide-react';
import { useBookmark } from '../../../custom_hooks/bookmark';
import UnbookmarkModal from '../../shared/UnbookmarkModal';

export default function ProjectsCard({ project }) {
    const [currentImageIndex, setCurrentImageIndex] = useState(0);
    const [isHovering, setIsHovering] = useState(false);
    const { 
        bookmarked, 
        toggleBookmark, 
        showUnbookmarkModal, 
        confirmUnbookmark, 
        cancelUnbookmark 
    } = useBookmark({
         id: project.id, 
         type: "Project",
         initialBookmarked: project.initialBookmarked, 
        });
    const [bookmarkHover, setBookmarkHover] = useState(false);
    const cardRef = useRef(null);

    useEffect(() => {
        let interval;

        if (isHovering) {
            interval = setInterval(() => {
                setCurrentImageIndex((prevIndex) =>
                    prevIndex === project.images.length - 1 ? 0 : prevIndex + 1
                );
            }, 1000);
        }

        return () => clearInterval(interval);
    }, [isHovering, project.images.length]);

    const renderStars = (rating) => {
        const stars = [];
        const fullStars = Math.floor(rating);
        const halfStar = rating % 1 >= 0.5;

        for (let i = 0; i < 5; i++) {
            if (i < fullStars) {
                stars.push(
                    <span key={i} className="text-black-400">★</span>
                );
            } else if (i === fullStars && halfStar) {
                stars.push(
                    <span key={i} className="text-black-400">★</span>
                );
            } else {
                stars.push(
                    <span key={i} className="text-gray-300">★</span>
                );
            }
        }
        return stars;
    };

    const remainingCollaborators = project.collaborators.length > 3 ? project.collaborators.length - 3 : 0;

    return (
        <div
            className="rounded-lg md:rounded-xl overflow-hidden text-inter shadow-md bg-white border border-gray-200"
            onMouseEnter={() => setIsHovering(true)}
            onMouseLeave={() => {
                setIsHovering(false);
                setCurrentImageIndex(0);
            }}
            ref={cardRef}
            data-testid="projects-card"
        >
            <div className="relative h-36 md:h-48 w-full bg-gray-100 z-5">
                {/* Tag on the top left corner */}
                <div
                    className="text-black bg-primarygreen hover:bg-primarygreen absolute top-1 md:top-2 left-1 md:left-2 px-2 py-0.5 md:py-1 rounded-full z-20"
                >
                    {}
                </div>

                {/* Bookmark Icon */}
                <button
                    type='button'
                    onClick={toggleBookmark}
                    onMouseEnter={() => setBookmarkHover(true)}
                    onMouseLeave={() => setBookmarkHover(false)}
                    className={`absolute top-1 md:top-2 right-1 md:right-2 p-0.5 md:p-1 rounded-md md:rounded-lg z-20 transition-all duration-200 transform hover:scale-110 cursor-pointer ${
                        bookmarked 
                            ? 'bg-primarygreen text-black shadow-md' 
                            : bookmarkHover 
                            ? 'bg-primarygreen text-black shadow-md' 
                            : 'bg-white/80 text-gray-600 hover:bg-white'
                    }`}
                    aria-label={bookmarked ? "Remove bookmark" : "Add bookmark"}
                >
                    <Bookmark 
                        size={24} 
                        className={`transition-all duration-200 ${
                            bookmarked ? 'fill-current' : ''
                        }`}
                    />
                </button>
                {/* Project Images */}
                {project.images.map((imageSrc, index) => (
                    <img
                        key={index}
                        src={imageSrc}
                        alt={`${project.name} images ${index + 1}`}
                        className="absolute top-0 left-0 w-full h-full object-cover transition-opacity duration-1000 ease-in-out"
                        style={{
                            opacity: currentImageIndex === index ? 1 : 0,
                            zIndex: currentImageIndex === index ? 10 : 1,
                        }}
                    />
                ))}

                {/* Slideshow indicator dots */}
                <div className="absolute bottom-2 left-0 right-0 flex justify-center space-x-1 z-10" data-testid="slideshow-dots">
                    {project.images.map((_, index) => (
                        <div
                            key={index}
                            className={`w-2 h-2 rounded-full transition-colors duration-300 ${currentImageIndex === index ? 'bg-primarygreen' : 'bg-gray-300'
                                }`}
                        />
                    ))}
                </div>
            </div>

            <div className="p-2 sm:p-4">
                {/* Project Name and Views */}
                <div className="flex items-center justify-between mb-1">
                    <div className="flex items-center">
                        <h3 className="text-base md:text-lg font-semibold">{project.name}</h3>
                    </div>
                    <div className="flex items-center bg-primarygreen text-black-600 px-1 sm:px-2 py-1 rounded-full">
                        <Eye size={16} className="mr-1" />
                        <span className="text-xs sm:text-sm">{project.views}</span>
                    </div>
                </div>

                {/* Location */}
                <div className="flex items-center text-gray-600 mb-2">
                    <MapPin size={16} className="mr-1" />
                    <span className="text-xs sm:text-sm">{project?.location?.address}</span>
                </div>

                {/* Description */}
                <p className="text-sm text-gray-700 mb-4 sm:mb-6 line-clamp-2">
                    {project.description}
                </p>

                {/* Tags */}
                <div className="flex flex-wrap mb-4 sm:mb-6 gap-2">
                    {project.tags?.map((tag, index) => (
                        <span
                            key={index}
                            className="bg-gray-200 text-gray-700 text-xs sm:text-sm px-2 py-0.5 md:py-1.5 rounded-full flex items-center"
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1 rotate-45" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"></path><path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"></path></svg>
                            {tag}
                        </span>
                    ))}
                </div>

                {/* Status and Rating - combined in one row */}
                {/* on mobile flex direction column */}
                <div className="flex items-start md:items-center justify-between mb-4 sm:mb-6 flex-col md:flex-row">
                    {/* Status */}
                    <span className="px-4 py-1.5 rounded-lg text-sm font-semibold mb-2 md:mb-0 bg-green-100">
                        Seeking Collaborators
                    </span>

                    {/* Rating */}
                    <div className="flex items-center">
                        <div className="flex">
                            {renderStars(project.rating)}
                        </div>
                        <span className="ml-1 text-xs sm:text-sm text-gray-600">
                             ({project.rating || 0})
                        </span>
                    </div>
                </div>

                {/* Collaborators Section */}
                <div className="flex items-center justify-between mb-2 sm:mb-4">
                    <span className="text-sm text-gray-700 font-medium">Collaborators:</span>
                    <div className="flex items-center -space-x-2 overflow-hidden">
                        {project.collaborators.slice(0, 3).map((collaborator) => (
                            <img
                                key={collaborator.id}
                                className="inline-block h-8 w-8 rounded-full ring-2 ring-white"
                                src={collaborator.image}
                                alt={collaborator.name}
                            />
                        ))}
                        {remainingCollaborators > 0 && (
                            <span className="flex items-center justify-center h-8 w-8 rounded-full bg-gray-200 text-gray-600 text-xs ring-2 ring-white">
                                +{remainingCollaborators}
                            </span>
                        )}
                    </div>
                </div>
            </div>
            {/* Unbookmark Confirmation Modal */}
            <UnbookmarkModal
                isOpen={showUnbookmarkModal}
                onClose={cancelUnbookmark}
                onConfirm={confirmUnbookmark}
                name={project.name}
            />
        </div>
    );
}
