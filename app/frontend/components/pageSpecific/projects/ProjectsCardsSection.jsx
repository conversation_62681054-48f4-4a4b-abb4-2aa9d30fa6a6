import { Link } from '@inertiajs/react';
import ProjectsCard from './ProjectsCard';
import { usePage } from '@inertiajs/react';

function ProjectsCardsSection({ projects }) {
  const { bookmarkedProjectIds } = usePage().props;

  const projectsData = projects.map(project => ({
    id: project.id, // Add id to the transformation
    name: project.name,
    views: project.views,
    location: project?.location,
    description: project.description,
    rating: project.rating,
    ratingCount: project.rating_count,
    images: project.images,
    collaborators: project.collaborators,
    tags: project.tags,
    initialBookmarked: bookmarkedProjectIds.includes(project.id),
  }))

  return (
    <div className="w-full font-inter px-2 2xl:px-24 py-6 sm:p-8 shadow-inner">
      {
        projectsData.length != 0 ?
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-2 md:gap-4">
            {projectsData.map((project, index) => (
              // Pass the project data to the ProjectsCard component via the 'project' prop
              <Link
                key={project.id}
                href={`/project/${project.id}`}
                className="no-underline"
              >
                {/* Render the ProjectsCard component for each project */}
                <ProjectsCard key={index} project={project} />
              </Link>
            ))}
          </div>
          :
          <div class="flex flex-col items-center justify-center text-center p-6 bg-yellow-100 text-yellow-800 rounded-md shadow-md">
            <h3 class="text-xl font-semibold mb-2">Oops!</h3>
            <p class="text-base">
              Looks like we came up empty. Maybe give it another shot with different words?
            </p>
          </div>

      }
    </div>
  );
}

export default ProjectsCardsSection;
