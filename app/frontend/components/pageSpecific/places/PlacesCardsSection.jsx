//app/frontend/components/pageSpecific/places/PlacesCardsSection.jsx
import { Link } from '@inertiajs/react';
import { usePage } from '@inertiajs/react';
import PlacesCard from './PlacesCard';
import profile1 from '/assets/profile1.jpg';
import profile2 from '/assets/profile2.jpg';
import profile3 from '/assets/profile3.jpg';
import profile4 from '/assets/profile4.jpg';


function PlacesCardsSection() {
  const { places, bookmarkedPlaceIds } = usePage().props;
  
  const placesData = places.map(place => ({
    id: place.id,
    name: place.name,
    tags: place.tags,
    description: place.description,
    avatar_url: place.avatar_url,
    images: place.images,
    rating: 4.5,
    views: place.views,
    location: place.location,
    initialBookmarked: bookmarkedPlaceIds.includes(place.id),
    associates: [
        {
          id: 1,
          name: "Associate 1",
          image: profile1
        },
        {
          id: 2,
          name: "Associate 2",
          image: profile2
        },
        {
          id: 3,
          name: "Associate 3",
          image: profile3
        },
        {
          id: 4,
          name: "Associate 4",
          image: profile4
        }
      ]
  }))
  

  // Create an array of 10 copies of the placeholder data

  return (
    <div className="w-full font-inter px-2 2xl:px-24 py-6 sm:p-8 shadow-inner">

      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        {placesData.map((place, index) => (
          // Pass the place data to the PlacesCard component via the 'place' prop
          <Link
            key={index}
            href={`/place/${place.id}`}
            className="no-underline"
          >
            {/* Render the PlacesCard component for each place */}
          <PlacesCard key={index} place={place} />
          </Link>
        ))}
      </div>
    </div>
  );
}

export default PlacesCardsSection;


