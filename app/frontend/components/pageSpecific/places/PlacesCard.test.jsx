import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, act } from '@testing-library/react';
import PlacesCard from './PlacesCard';

describe('PlacesCard Component', () => {
  const mockPlace = {
    name: 'Mountain View Hub',
    location: 'New York, USA',
    description: 'A vibrant innovation hub for tech enthusiasts and entrepreneurs.',
    tags: ['Incubator', 'Co-Creator', 'Innovation Hub', 'Maker Space', 'Co-working Space'],
    rating: 4.5,
    views: 23,
    associates: [
      { id: '1', name: '<PERSON>', image: 'https://via.placeholder.com/32/FF0000/FFFFFF?text=A' },
      { id: '2', name: '<PERSON>', image: 'https://via.placeholder.com/32/00FF00/FFFFFF?text=B' },
      { id: '3', name: '<PERSON>', image: 'https://via.placeholder.com/32/0000FF/FFFFFF?text=C' },
      { id: '4', name: '<PERSON>', image: 'https://via.placeholder.com/32/FFFF00/000000?text=D' },
      { id: '5', name: '<PERSON>', image: 'https://via.placeholder.com/32/00FFFF/000000?text=E' },
    ],
    images: [
      '/sample-image-1.jpg',
      '/sample-image-2.jpg',
      '/sample-image-3.jpg',
    ],
  };

  // Mock the resize observer
  beforeEach(() => {
    vi.useFakeTimers();
    global.ResizeObserver = vi.fn().mockImplementation(() => ({
      observe: vi.fn(),
      unobserve: vi.fn(),
      disconnect: vi.fn(),
    }));
  });

  afterEach(() => {
    vi.restoreAllMocks();
    vi.useRealTimers();
  });

  it('renders the place information correctly', () => {
    render(<PlacesCard place={mockPlace} />);

    expect(screen.getByText('Mountain View Hub')).toBeInTheDocument();
    expect(screen.getByText('Innovation Hub')).toBeInTheDocument();
    expect(screen.getByText('New York, USA')).toBeInTheDocument();
    expect(screen.getByText('(4.5)')).toBeInTheDocument();
    expect(screen.getByText('23k')).toBeInTheDocument();
  });

  it('renders the correct number of images', () => {
    render(<PlacesCard place={mockPlace} />);

    const images = screen.getAllByAltText(/Mountain View Hub images/);
    expect(images).toHaveLength(3);
  });

  it('shows the first image by default', () => {
    render(<PlacesCard place={mockPlace} />);

    const firstImage = screen.getByAltText('Mountain View Hub images 1');
    expect(firstImage).toHaveStyle('opacity: 1'); // First image should be visible

    const otherImages = screen.getAllByAltText(/Mountain View Hub images [23]/);
    for (const img of otherImages) {
      expect(img).toHaveStyle('opacity: 0'); // Other images should be hidden
    }
  });

  it('shows slideshow indicator dots equal to number of images', () => {
    render(<PlacesCard place={mockPlace} />);

    const dots = document.querySelectorAll('.w-2.h-2.rounded-full');
    expect(dots).toHaveLength(mockPlace.images.length);
  });

  it('first dot should be active by default', () => {
    render(<PlacesCard place={mockPlace} />);

    const dots = document.querySelectorAll('.w-2.h-2.rounded-full');
    expect(dots[0]).toHaveClass('bg-primaryorange'); // First dot should be active
    expect(dots[1]).toHaveClass('bg-gray-300'); // Others should be inactive
    expect(dots[2]).toHaveClass('bg-gray-300');
  });

  it('changes images when hovering', async () => {
    render(<PlacesCard place={mockPlace} />);

    const card = screen.getByText('Mountain View Hub').closest('div.rounded-lg');

    // Trigger mouse enter
    fireEvent.mouseEnter(card);

    // Fast-forward time to trigger the first image change
    act(() => {
      vi.advanceTimersByTime(1000);
    });

    // After 2 seconds, the second image should be visible
    const images = screen.getAllByAltText(/Mountain View Hub images/);
    expect(images[1]).toHaveStyle('opacity: 1');
    expect(images[0]).toHaveStyle('opacity: 0');
    expect(images[2]).toHaveStyle('opacity: 0');

    // And the second dot should be active
    const dots = document.querySelectorAll('.w-2.h-2.rounded-full');
    expect(dots[1]).toHaveClass('bg-primaryorange');
    expect(dots[0]).toHaveClass('bg-gray-300');
    expect(dots[2]).toHaveClass('bg-gray-300');
  });

  it('stops and resets slideshow when mouse leaves', async () => {
    render(<PlacesCard place={mockPlace} />);

    const card = screen.getByText('Mountain View Hub').closest('div.rounded-lg');

    // Trigger mouse enter to start slideshow
    fireEvent.mouseEnter(card);

    // Advance time to show the second image
    act(() => {
      vi.advanceTimersByTime(1000);
    });

    // Then trigger mouse leave to stop slideshow
    fireEvent.mouseLeave(card);

    // The first image should be shown again when mouse leaves
    const images = screen.getAllByAltText(/Mountain View Hub images/);
    expect(images[0]).toHaveStyle('opacity: 1');
    expect(images[1]).toHaveStyle('opacity: 0');
    expect(images[2]).toHaveStyle('opacity: 0');
  });

  it('renders stars correctly based on rating', () => {
    render(<PlacesCard place={mockPlace} />);

    // For a 4.5 rating, we should have 5 total stars (4.5 rounded to 5 full stars)
    const blackStars = document.querySelectorAll('.text-black-400');
    const grayStars = document.querySelectorAll('.text-gray-300');

    expect(blackStars).toHaveLength(5);
    expect(grayStars).toHaveLength(0);

    // Test with a different rating
    const lowerRatedPerson = { ...mockPlace, rating: 3.2 };
    render(<PlacesCard place={lowerRatedPerson} />);

    const updatedBlackStars = document.querySelectorAll('.text-black-400');
    const updatedGrayStars = document.querySelectorAll('.text-gray-300');

    expect(updatedBlackStars).toHaveLength(8);
    expect(updatedGrayStars).toHaveLength(2);
  });
  it('renders the place description (even if truncated)', () => {
    render(<PlacesCard place={mockPlace} />);

    const description = screen.getByText(/A vibrant innovation hub/i);
    expect(description).toBeInTheDocument();
  });

  it('renders first 3 associate avatars and shows +remaining', () => {
    render(<PlacesCard place={mockPlace} />);

    mockPlace.associates.slice(0,3).forEach((associate) => {
      const avatar = screen.getByAltText(associate.name);
      expect(avatar).toBeInTheDocument();
      expect(avatar).toHaveAttribute('src', associate.image);
    });
    // Ensure the remaining associates are not directly visible
    const hiddenAssociates = mockPlace.associates.slice(3);
    hiddenAssociates.forEach((associate) => {
      const avatar = screen.queryByAltText(associate.name);
      expect(avatar).not.toBeInTheDocument();
    });

    // Check for the "+2" indicator
    const remainingCount = mockPlace.associates.length - 3;
    const remainingIndicator = screen.getByText(`+${remainingCount}`);
    expect(remainingIndicator).toBeInTheDocument();
  });
});