//app/frontend/components/pageSpecific/places/PlacesCard.jsx
import { useState, useEffect, useRef } from 'react';
import { MapPin, Bookmark, Eye } from 'lucide-react';
import { useBookmark } from '../../../custom_hooks/bookmark';
import UnbookmarkModal from '../../shared/UnbookmarkModal';

// PlacesCard component that takes a 'place' prop
export default function PlacesCard({ place }) {
    const [currentImageIndex, setCurrentImageIndex] = useState(0);
    const [isHovering, setIsHovering] = useState(false);
    const { 
        bookmarked, 
        toggleBookmark, 
        showUnbookmarkModal, 
        confirmUnbookmark, 
        cancelUnbookmark 
      } = useBookmark({
        id: place.id, 
        type: "Place",
        initialBookmarked: place.initialBookmarked,
        });
    const [bookmarkHover, setBookmarkHover] = useState(false);
    const cardRef = useRef(null);

    useEffect(() => {
        let interval;

        if (isHovering) {
            // Start slideshow: cycle through images every 1 second
            interval = setInterval(() => {
                setCurrentImageIndex((prevIndex) =>
                    prevIndex === place.images.length - 1 ? 0 : prevIndex + 1
                );
            }, 1000);
        }

        // Cleanup interval on unmount or when hover ends
        return () => clearInterval(interval);
    }, [isHovering, place.images.length]);

    // Function to render star icons based on rating
    const renderStars = (rating) => {
        const stars = [];
        const fullStars = Math.round(rating);
        const hasHalfStar = Math.round(rating * 2) / 2 > fullStars;

        for (let i = 0; i < 5; i++) {
            if (i < fullStars) {
                stars.push(
                    <span key={i} className="text-black-400">★</span>
                );
            } else if (i === fullStars && hasHalfStar) {
                stars.push(
                    <span key={i} className="text-black-400">★</span> // (Optional) Half star display can be customized
                );
            } else {
                stars.push(
                    <span key={i} className="text-gray-300">★</span>
                );
            }
        }
        return stars;
    };

    const remainingAssociates = place.associates.length > 3 ? place.associates.length - 3 : 0;

    return (
        <>
        <div
            className="rounded-lg md:rounded-2xl overflow-hidden text-inter shadow-sm bg-white transform transition-all duration-300 hover:shadow-md hover:-translate-y-0.5 active:scale-98"
            onMouseEnter={() => setIsHovering(true)} // Start slideshow on hover
            onMouseLeave={() => {
                setIsHovering(false); // Stop slideshow on leave
                setCurrentImageIndex(0); // Reset to first image
            }}
            ref={cardRef}
        >
            {/* Image Slideshow Container */}
            <div className="relative h-48 lg:h-60 w-full bg-gray-100 z-5">
                {/* Bookmark icon at the top-right */}
                <button
                    type='button'
                    onClick={toggleBookmark}
                    onMouseEnter={() => setBookmarkHover(true)}
                    onMouseLeave={() => setBookmarkHover(false)}
                    className={`absolute top-1 md:top-2 right-1 md:right-2 p-0.5 md:p-1 rounded-md md:rounded-lg z-20 transition-all duration-200 transform hover:scale-110 cursor-pointer ${
                        bookmarked 
                            ? 'bg-primaryorange text-black shadow-md' 
                            : bookmarkHover 
                            ? 'bg-primaryorange text-black shadow-md' 
                            : 'bg-white/80 text-gray-600 hover:bg-white'
                    }`}
                    aria-label={bookmarked ? "Remove bookmark" : "Add bookmark"}
                >
                    <Bookmark 
                        size={24} 
                        className={`transition-all duration-200 ${
                            bookmarked ? 'fill-current' : ''
                        }`}
                    />
                </button>

                {/* Slideshow images */}
                {place.images.length != 0 ? 
                    place.images.map((imageSrc, index) => (
                    <img
                        key={index}
                        src={imageSrc}
                        alt={`${place.name} images ${index + 1}`}
                        className="absolute top-0 left-0 w-full h-full object-cover transition-opacity duration-1000 ease-in-out"
                        style={{
                            opacity: currentImageIndex === index ? 1 : 0,
                            zIndex: currentImageIndex === index ? 10 : 1,
                        }}
                    />
                ))
                :
                    <img
                        src={place.avatar_url}
                        alt={`${place.name}`}
                        className="absolute top-0 left-0 w-full h-full object-cover transition-opacity duration-1000 ease-in-out"
                    />
                }

                {/* Image indicator dots */}
                <div className="absolute bottom-2 left-0 right-0 flex justify-center space-x-1 z-10">
                    {place.images.map((_, index) => (
                        <div
                            key={index}
                            className={`w-2 h-2 rounded-full transition-colors duration-300 ${currentImageIndex === index ? 'bg-primaryorange' : 'bg-gray-300'
                                }`}
                        />
                    ))}
                </div>
            </div>

            {/* Textual Content Area */}
            <div className="p-4">
                {/* Title and views */}
                <div className="flex items-center justify-between mb-1">
                    <div className="flex items-center">
                        <h3 className="text-base md:text-lg font-semibold">{place.name}</h3>
                    </div>
                    {/* Views with eye icon */}
                    <div className="flex items-center bg-primaryorange text-black-600 px-1 sm:px-2 py-1 rounded-full">
                        <Eye size={16} className="mr-1" />
                        <span className="text-xs sm:text-sm">{place.views}</span>
                    </div>
                </div>

                {/* Location info with pin icon */}
                <div className="flex items-center mb-2 text-gray-600">
                    <MapPin size={16} className="mr-1" />
                    <span className="text-xs sm:text-sm">{place.location?.address}</span>
                </div>

                {/* Place description */}
                <p className="text-sm text-gray-700 mb-4 line-clamp-2">
                    {place.description}
                </p>

                {/* Tags/labels */}
                <div className="flex flex-wrap mb-4 gap-2">
                    {place.tags && place.tags.map((tag, index) => (
                        <span
                            key={index}
                            className="bg-gray-200 text-gray-700 text-xs sm:text-sm px-2 py-0.5 md:py-1.5 rounded-full flex items-center"
                        >
                            {tag}
                        </span>
                    ))}
                </div>

                {/* Rating stars */}
                <div className="flex items-center mb-4">
                    <div className="flex">
                        {renderStars(place.rating)}
                    </div>
                    <span className="ml-1 text-xs sm:text-sm text-gray-600">({place.rating|| 0})</span>
                </div>
                {/* Associates Section */}
                <div className="flex items-center justify-between mb-2 sm:mb-4">
                    <span className="text-sm text-gray-700 font-medium">Associates:</span>
                    <div className="flex items-center -space-x-2 overflow-hidden">
                        {place.associates.slice(0, 3).map((associate) => (
                            <img
                                key={associate.id}
                                className="inline-block h-8 w-8 rounded-full ring-2 ring-white"
                                src={associate.image}
                                alt={associate.name}
                            />
                        ))}
                        {remainingAssociates > 0 && (
                            <span className="flex items-center justify-center h-8 w-8 rounded-full bg-gray-200 text-gray-600 text-xs ring-2 ring-white">
                                +{remainingAssociates}
                            </span>
                        )}
                    </div>
                </div>
            </div>
        </div>
        {/* Unbookmark Confirmation Modal */}
        <UnbookmarkModal
                isOpen={showUnbookmarkModal}
                onClose={cancelUnbookmark}
                onConfirm={confirmUnbookmark}
                name={place.name}
        />
        </>
    );
}
