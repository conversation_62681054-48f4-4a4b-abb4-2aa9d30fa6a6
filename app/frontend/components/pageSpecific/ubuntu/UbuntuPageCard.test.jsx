import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import UbuntuPageCard from './UbuntuPageCard';

// Mock the image import
vi.mock('../../../assets/ubuntu_assets/placeholder.png', () => ({
  default: 'mocked-placeholder-image.png'
}));

// Mock lucide-react icons
vi.mock('lucide-react', () => ({
  ThumbsUp: () => <svg data-testid="thumbs-up-icon" />,
  MessageSquare: () => <svg data-testid="message-square-icon" />
}));

describe('UbuntuPageCard', () => {
  const mockForum = {
    title: 'Test Forum Title',
    description: 'Test forum description content',
    author: '<PERSON>',
    profileImage: 'https://example.com/profile.jpg',
    likes: 42,
    comments: 15
  };

  it('renders forum data correctly', () => {
    render(<UbuntuPageCard forum={mockForum} />);
    
    expect(screen.getByText('Test Forum Title')).toBeInTheDocument();
    expect(screen.getByText('Test forum description content')).toBeInTheDocument();
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('42')).toBeInTheDocument();
    expect(screen.getByText('15')).toBeInTheDocument();
  });

  it('renders images with correct attributes', () => {
    render(<UbuntuPageCard forum={mockForum} />);
    
    const mainImage = screen.getByAltText('Test Forum Title');
    const profileImage = screen.getByAltText("John Doe's profile");
    
    expect(mainImage).toHaveAttribute('src', 'mocked-placeholder-image.png');
    expect(profileImage).toHaveAttribute('src', 'https://example.com/profile.jpg');
  });

  it('renders icons for likes and comments', () => {
    render(<UbuntuPageCard forum={mockForum} />);
    
    expect(screen.getByTestId('thumbs-up-icon')).toBeInTheDocument();
    expect(screen.getByTestId('message-square-icon')).toBeInTheDocument();
  });

  it('handles missing or undefined forum data gracefully', () => {
    const incompleteForum = {
      title: 'Test Title',
      description: 'Test Description',
      author: 'Test Author'
      // Missing likes, comments, profileImage
    };
    
    expect(() => render(<UbuntuPageCard forum={incompleteForum} />)).not.toThrow();
  });
});