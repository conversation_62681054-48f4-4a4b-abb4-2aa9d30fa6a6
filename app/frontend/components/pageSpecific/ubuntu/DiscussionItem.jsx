import {ThumbsUp, MessageSquare } from 'lucide-react';

const DiscussionItem = ({ 
  title, 
  description, 
  author, 
  timePosted, 
  likes = 183, 
  comments = 0,
  avatarSrc 
}) => {
  return (
    <div className="w-full max-w-7xl flex items-center gap-3 md:gap-4 py-4 md:py-6 border-b-2 border-gray-500">
      {/* Avatar */}
      <div className="flex-shrink-0 self-center">
        <img
          src={avatarSrc || "/api/placeholder/60/60"}
          alt={`${author} avatar`}
          className="w-14 h-14 md:w-16 md:h-16 rounded-full object-cover"
        />
      </div>

      {/* Content and Stats Container */}
      <div className="flex flex-col justify-center flex-1 min-w-0">
        {/* Title and Description */}
        <h3 className="text-sm md:text-base font-medium text-gray-900 mb-1 leading-tight pr-2">
          {title}
        </h3>
        <p className="text-xs md:text-sm text-gray-600 mb-2 leading-relaxed">
          {description}
        </p>
        <div className="text-xs md:text-sm text-gray-500">
          By <span className="font-medium">{author}</span> · Posted {timePosted}
        </div>

        {/* Stats - Mobile only */}
        <div className="flex items-center gap-4 mt-3 md:hidden">
          <div className="flex items-center gap-1 text-gray-600">
            <ThumbsUp size={14} />
            <span className="text-xs font-medium">{likes}</span>
          </div>
          <div className="flex items-center gap-1 text-gray-600">
            <MessageSquare size={14} />
            <span className="text-xs font-medium">{comments}</span>
          </div>
        </div>
      </div>

      {/* Stats - Desktop only */}
      <div className="hidden md:flex items-center gap-4 ml-4">
        <div className="flex items-center gap-1 text-gray-800">
          <ThumbsUp size={16} fill="currentColor" />
          <span className="text-sm font-medium">{likes}</span>
        </div>
        <div className="flex items-center gap-1 text-gray-800">
          <MessageSquare size={16} fill="currentColor" />
          <span className="text-sm font-medium">{comments}</span>
        </div>
      </div>
    </div>
  );
};


export default DiscussionItem;
