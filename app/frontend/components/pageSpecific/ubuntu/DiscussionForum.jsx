import  DiscussionHeader from './DiscussionHeader';
import  DiscussionItem  from './DiscussionItem';
import LoadMoreButton from './LoadMoreButton';

import avatar from '../../../assets/ubuntu_assets/profile.png';

const DiscussionForum = () => {
  // Sample data
  const discussions = [
    {
      id: 1,
      title: "Where UI/UX designers reimagine digital spaces through empathy, inclusion, and impact.",
      description: "Explore how interfaces shape our world from public service platforms to artistic portfolios.",
      author: "Design Justice Lab",
      timePosted: "17m ago",
      likes: 183,
      comments: 0,
      avatarSrc: avatar
    },
    {
      id: 2,
      title: "Where UI/UX designers reimagine digital spaces through empathy, inclusion, and impact.",
      description: "Explore how interfaces shape our world from public service platforms to artistic portfolios.",
      author: "Design Justice Lab",
      timePosted: "17m ago",
      likes: 183,
      comments: 0,
      avatarSrc: avatar
    },
    {
      id: 3,
      title: "Where UI/UX designers reimagine digital spaces through empathy, inclusion, and impact.",
      description: "Explore how interfaces shape our world from public service platforms to artistic portfolios.",
      author: "Design Justice Lab",
      timePosted: "17m ago",
      likes: 183,
      comments: 0,
      avatarSrc: avatar
    },
    {
      id: 4,
      title: "Where UI/UX designers reimagine digital spaces through empathy, inclusion, and impact.",
      description: "Explore how interfaces shape our world from public service platforms to artistic portfolios.",
      author: "Design Justice Lab",
      timePosted: "17m ago",
      likes: 183,
      comments: 0,
      avatarSrc: avatar
    }
  ];

  const handleSortChange = () => {
    // Implement sort logic
  };

  const handleStartDiscussion = () => {
    // Implement navigation or modal opening
  };

  const handleLoadMore = () => {
    // Implement load more logic
  };

  return (
    <div className="w-full mx-auto px-6 2xl:px-20 py-4 md:py-6 bg-white">
      <DiscussionHeader 
        onSortChange={handleSortChange}
        onStartDiscussion={handleStartDiscussion}
      />

      <div className="flex flex-col items-center bg-white">
        {discussions.map((discussion) => (
          <DiscussionItem
            key={discussion.id}
            title={discussion.title}
            description={discussion.description}
            author={discussion.author}
            timePosted={discussion.timePosted}
            likes={discussion.likes}
            comments={discussion.comments}
            avatarSrc={discussion.avatarSrc}
          />
        ))}
      </div>
      
      <LoadMoreButton onClick={handleLoadMore} />
    </div>
  );
};

export default DiscussionForum;
