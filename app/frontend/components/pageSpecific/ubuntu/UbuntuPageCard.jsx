// app/frontend/components/pageSpecific/ubuntu/UbuntuPageCard.jsx
import { Star, Bookmark, Eye } from 'lucide-react';
import imageUrl from '../../../assets/ubuntu_assets/placeholder.png';
import { useBookmark } from '../../../custom_hooks/bookmark';
import React, {useState} from 'react';
import UnbookmarkModal from '../../shared/UnbookmarkModal';

const UbuntuPageCard = ({ forum }) => {
    const { 
        bookmarked, 
        toggleBookmark, 
        showUnbookmarkModal, 
        confirmUnbookmark, 
        cancelUnbookmark 
      } = useBookmark({
        id: forum.id, 
        type: "Forum",
        initialBookmarked: forum.initialBookmarked, 
    });
    const [bookmarkHover, setBookmarkHover] = useState(false);
    // Generate star rating display
    const renderStars = (rating) => {
        const stars = [];
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 !== 0;
        
        for (let i = 0; i < fullStars; i++) {
            stars.push(
                <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
            );
        }
        
        if (hasHalfStar) {
            stars.push(
                <Star key="half" className="w-4 h-4 fill-yellow-400 text-yellow-400 opacity-50" />
            );
        }
        
        const emptyStars = 5 - Math.ceil(rating);
        for (let i = 0; i < emptyStars; i++) {
            stars.push(
                <Star key={`empty-${i}`} className="w-4 h-4 text-gray-300" />
            );
        }
        
        return stars;
    };

    return (
        <div className="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 flex flex-col group max-w-sm">
            {/* Image with bookmark */}
            <div className="relative h-48 bg-gray-100 overflow-hidden">
                <img
                    src={forum.profileImage}
                    alt={forum.title}
                    className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                />
                {/* Bookmark icon */}
                    <button
                        type='button'
                        onClick={toggleBookmark}
                        onMouseEnter={() => setBookmarkHover(true)}
                        onMouseLeave={() => setBookmarkHover(false)}
                        className={`absolute top-1 md:top-2 right-1 md:right-2 p-0.5 md:p-1 rounded-md md:rounded-lg transition-all duration-200 transform hover:scale-110 cursor-pointer ${
                            bookmarked 
                                ? 'bg-white text-red-500 shadow-md' 
                                : bookmarkHover 
                                ? 'bg-white text-red-500 shadow-md' 
                                : 'bg-white/80 text-gray-600 hover:bg-white'
                        }`}
                        aria-label={bookmarked ? "Remove bookmark" : "Add bookmark"}
                    >
                        <Bookmark 
                            size={24} 
                            className={`transition-all duration-200 ${
                                bookmarked ? 'fill-current' : ''
                            }`}
                        />
                    </button>
                <div className="relative h-48 lg:h-60 w-full bg-gray-100 z-5">
                    {/* Bookmark icon at the top-right */} 
                
                </div>
            </div>

            {/* Card Content */}
            <div className="p-4 flex-grow flex flex-col">
                {/* Title and Views */}
                <div className="flex items-start justify-between mb-2">
                    <h3 className="text-lg font-bold text-gray-900 line-clamp-2 group-hover:text-primarymaroon transition-colors duration-200 flex-1 mr-2">
                        {forum.title}
                    </h3>
                    <div className="flex items-center bg-primarymaroon text-white px-2 py-1 gap-2 rounded-full text-sm font-medium whitespace-nowrap">
                        <Eye className="text-white w-4 h-4"/>
                        <span>{forum.views}</span>
                    </div>
                </div>
                
                {/* Description */}
                <p className="text-sm text-gray-600 mb-4 line-clamp-2 flex-grow">
                    {forum.description}
                </p>

                {/* Categories */}
                <div className="flex flex-wrap gap-2 mb-4">
                    {forum.categories?.map((category, index) => (
                        <span
                            key={index}
                            className="px-3 py-1 bg-gray-100 text-gray-700 text-xs rounded-full hover:bg-gray-200 transition-colors duration-200"
                        >
                            {category.name}
                        </span>
                    ))}
                </div>

                {/* Rating */}
                <div className="flex items-center mb-4">
                    <div className="flex items-center mr-2">
                        {renderStars(forum.rating)}
                    </div>
                    <span className="text-sm text-gray-600">({forum.rating})</span>
                </div>

                {/* Members section */}
                <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Members:</span>
                    {/* Member avatars */}
                    <div className="flex -space-x-5">
                        {forum?.members.slice(0, 3).map((member, index) => (
                            <div
                                key={index}
                                className="w-10 h-10 rounded-full overflow-hidden border-2 border-white bg-gray-200"
                            >
                                <img
                                    src={member.avatar}
                                    alt={member.name}
                                    className="w-full h-full object-cover"
                                />
                            </div>
                        ))}
                        {/* Member count circle */}
                        <div className="w-10 h-10 rounded-full bg-gray-200 border-2 border-white flex items-center justify-center">
                            <span className="text-xs font-bold text-black">
                                {forum.numberOfUsers}+
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            {/* Unbookmark Confirmation Modal */}
            <UnbookmarkModal
                isOpen={showUnbookmarkModal}
                onClose={cancelUnbookmark}
                onConfirm={confirmUnbookmark}
                name={forum.title}
            />
        </div>
    );
};

export default UbuntuPageCard;