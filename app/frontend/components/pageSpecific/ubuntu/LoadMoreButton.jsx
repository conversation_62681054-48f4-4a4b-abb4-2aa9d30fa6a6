import { ChevronDown } from 'lucide-react';

const LoadMoreButton = ({ onClick, hasMore = true }) => {
  if (!hasMore) return null;
  
  return (
    <div className="flex justify-center pt-4 md:pt-6 px-4">
      <button 
        onClick={onClick}
        className="flex items-center gap-2 px-6 md:px-8 py-2.5 md:py-3 bg-primarybrown text-white rounded-full text-sm font-medium hover:bg-black transition-colors w-full sm:w-auto justify-center"
      >
        Load more
        <ChevronDown size={16} />
      </button>
    </div>
  );
};

export default LoadMoreButton;
