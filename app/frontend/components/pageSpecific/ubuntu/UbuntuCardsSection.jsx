// app/frontend/components/pageSpecific/ubuntu/UbuntuCardsSection.jsx
import { Link } from '@inertiajs/react';
import { usePage } from '@inertiajs/react';
import UbuntuPageCard from './UbuntuPageCard';
// import profileImage from "../../../assets/ubuntu_assets/profile.png";

function UbuntuCardsSection({ forums }) {
    const { bookmarkedForumIds } = usePage().props;
    const ubuntuData = forums.map(forum => ({
        id: forum.id,
        title: forum.title,
        description: forum.description,
        categories: forum?.categories || [],
        members: forum.forum_members,
        initialBookmarked: bookmarkedForumIds.includes(forum.id),
        views: forum.views,
        profileImage: forum.avatar_url
    }))


    return (
        <div className="w-full font-inter px-2 2xl:px-24 py-6 sm:p-8 shadow-inner">
            {
                ubuntuData.length != 0 ?
                    <div>
                        <div className="flex items-center justify-between mb-4">
                            <h2 className="text-xl font-semibold mb-4">Featured</h2>
                        </div>
                        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 2xl:grid-cols-5 gap-4 md:gap-6">
                            {ubuntuData.map((forum, index) => (
                                // Pass the forum data to the UbuntuPageCard component via the 'forum' prop
                                <Link
                                    href={`/forums/${forum.id}`}
                                    key={forum.id}
                                    className="no-underline"
                                >
                                    <UbuntuPageCard key={index} forum={forum} />
                                </Link>
                            ))}
                        </div>
                    </div>
                    :
                    <div class="flex flex-col items-center justify-center text-center p-6 bg-yellow-100 text-yellow-800 rounded-md shadow-md">
                        <h3 class="text-xl font-semibold mb-2">Oops!</h3>
                        <p class="text-base">
                            Looks like we came up empty.
                        </p>
                    </div>

            }
        </div>
    );
}

export default UbuntuCardsSection;
