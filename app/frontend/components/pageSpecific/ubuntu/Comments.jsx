import React, { useEffect, useState } from 'react';
import { ThumbsUp } from 'lucide-react';
import Replies from './Replies';
import ProfileImage from '/assets/profile3.jpg';
import axios from 'axios'

const Comment = ({ comment, depth = 0, onAddReply, currentUser }) => {
  const [showReplyInput, setShowReplyInput] = useState(false);
  const paddingLeft = depth * 32;

  const [commentLikes, setCommentLikes] = useState(comment.likes);
  const [likes, setLikes] = useState(comment.likes); // or just use commentLikes directly

  const handleLikeClick = (e) => {
    e.stopPropagation();
    axios.post(`/likes?comment_id=${comment.id}`)
      .then((res) => {
        const newLikesCount = res.data.total_likes;
        setCommentLikes(newLikesCount);
      })
      .catch((error) => console.log(error));
  };

  useEffect(() => {
    setLikes(commentLikes);
  }, [commentLikes]);

  return (
    <div className="relative">
      <div className={`flex items-start space-x-3 ${depth > 0 ? 'mt-4' : ''}`} style={{ paddingLeft }}>
        {depth > 0 && (
          <>
            <div className="absolute top-0 bottom-0 w-px bg-gray-300" style={{ left: `${paddingLeft - 16}px` }}></div>
            <div className="absolute top-4 w-2 h-2 bg-gray-300 rounded-full" style={{ left: `${paddingLeft - 19}px` }}></div>
          </>
        )}
        <div className="w-8 h-8 rounded-full overflow-hidden flex-shrink-0">
          <img src={comment.avatar || ProfileImage} alt="Profile" className="w-full h-full object-cover" />
        </div>
        <div className="flex-1">
          <div className="flex items-center space-x-2 mb-1">
            <span className="font-medium text-gray-900 text-sm">{comment.author}</span>
            {comment.replyToAuthor && (
              <span className="text-gray-500 text-xs">replying to <span className="font-medium">{comment.replyToAuthor}</span></span>
            )}
            <span className="text-gray-500 text-xs ml-auto">{comment.timeAgo}</span>
          </div>
          <p className="text-gray-700 text-sm mb-3 leading-relaxed">{comment.content}</p>
          
          {/* Like & Reply Buttons */}
          <div className="flex items-center space-x-4 text-xs">
            <button className="flex items-center space-x-1 text-gray-500 hover:text-red-600"
            onClick={(e) => handleLikeClick(e)}
            >
              <ThumbsUp className="w-3 h-3 fill-gray-500" />
              <span>{likes}</span>
              <span>Likes</span>
            </button>
            <button
              onClick={() => setShowReplyInput(!showReplyInput)}
              className="flex items-center space-x-1 text-gray-500 hover:text-red-600"
            >
              <span>Reply</span>
            </button>
          </div>

          {/* Replies Component */}
          <Replies 
            comment={comment} 
            onAddReply={onAddReply} 
            currentUser={currentUser}
            showInput={showReplyInput}
            onCancelReply={() => setShowReplyInput(false)}
            depth={depth + 1} 
          />
        </div>
      </div>
      {/* Recursive nested replies */}
      {/* {comment.replies && comment.replies.length > 0 && (
        <div className="ml-8">
          {comment.replies.map(reply => (
            <Comment 
              key={reply.id} 
              comment={reply} 
              depth={depth + 1} 
              onAddReply={onAddReply}
              currentUser={currentUser}
              showInput={showReplyInput}
            />
          ))}
        </div>
      )} */}
    </div>
  );
};

export default Comment;
