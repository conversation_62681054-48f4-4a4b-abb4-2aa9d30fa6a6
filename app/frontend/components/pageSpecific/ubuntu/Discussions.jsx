import React, { useState } from 'react';
import { MessageCircle, ThumbsUp, Plus, ArrowUpRight } from 'lucide-react';
import ProfileImage from '/assets/profile3.jpg';
import Comment from './Comments';
import axios from 'axios'
import { useEffect } from 'react';

const Discussions = ({
  selectedPost,
  newComment,
  setNewComment,
  handleAddComment,
  currentUser
}) => {

const [likes, setLikes] = useState(selectedPost?.likes || 0);
useEffect(() => {
  if (selectedPost) {
    axios.get(`/likes?post_id=${selectedPost.id}`)
      .then(res => {
        setLikes(res.data.total_likes);
      })
      .catch(error => console.error(error));
  }
}, [selectedPost]);


  const handleLikeClick = (e, postId) => {
    e.stopPropagation();
    axios.post("/likes", { post_id: selectedPost.id })
      .then((res) => {
        const newLikesCount = res.data.total_likes;
        setLikes(newLikesCount);
      })
      .catch((error) => console.log(error));
  };

  if (!selectedPost) {
    return (
      <div className="text-center py-12">
        <MessageCircle className="w-16 h-16 text-gray-300 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-500 mb-2">No discussion selected</h3>
        <p className="text-gray-400">Click on a post to view the discussion</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Main Post */}
      <div className="bg-gray-50 rounded-lg border border-gray-200 p-6">
        <div className="flex items-center space-x-3 mb-4">
          <div className="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
            <img src={selectedPost.avatar} alt="Profile" className="w-10 h-10 rounded-full" />
          </div>
          <div>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600">Posted By:</span>
              <div className="flex rounded-full px-2 bg-white">
                <span className="text-sm rounded-full bg-white font-medium text-red-600">{selectedPost.author}</span>
                <ArrowUpRight className="h-4 w-4" />
              </div>
            </div>
            <span className="text-sm text-gray-500">{selectedPost.timeAgo}</span>
          </div>
        </div>
        <h2 className="text-xl font-bold text-gray-900 mb-4">{selectedPost.title}</h2>
        <div className="text-gray-700 mb-6 leading-relaxed whitespace-pre-line">
          {selectedPost.content}
        </div>

        {/* Reactions */}
        <div className="flex items-center space-x-6 text-sm text-gray-500 border-t pt-4">
          <div className="flex items-center space-x-1">
            <MessageCircle className="w-4 h-4 fill-black" />
            <span>{selectedPost.comments}</span>
            <span>Comments</span>
          </div>
          <button
            onClick={handleLikeClick}
            className="flex items-center space-x-1 hover:text-red-600"
          >
            <ThumbsUp className="w-4 h-4 fill-black" />
            <span>{likes}</span>
            <span>Likes</span>
          </button>
        </div>
      </div>

      {/* Add Comment */}
      <div className="bg-gray-50 rounded-lg border border-gray-200 p-4">
        <textarea
          value={newComment}
          onChange={(e) => setNewComment(e.target.value)}
          placeholder="Add your comment..."
          className="w-full p-3 border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-red-500 text-sm"
          rows="3"
        />
        <div className="flex justify-end mt-3">
          <button
            onClick={handleAddComment}
            className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors text-sm"
          >
            Post Comment
          </button>
        </div>
      </div>

      {/* Comments Section */}
      <div className="space-y-4">
        {selectedPost.comments_list.map((comment) => (
          <div key={comment.id} className="bg-white rounded-lg shadow-sm p-4 relative">
            <Comment comment={comment} depth={0} currentUser={currentUser}/>
          </div>
        ))}
      </div>

      {/* Action Buttons */}
      <div className="flex justify-between">
        <button className="bg-red-600 text-white px-4 py-2 rounded-full hover:bg-red-700 transition-colors text-sm flex items-center space-x-2">
          <Plus className="w-4 h-4" />
          <span>Start A New Discussion</span>
        </button>
        <button className="bg-red-600 text-white px-4 py-2 rounded-full hover:bg-red-700 transition-colors text-sm">
          Load More Replies
        </button>
      </div>
    </div>
  );
};

export default Discussions;
