import React from 'react';
import { ChevronDown } from 'lucide-react';

const Resources = () => (
  <div className="space-y-6">
    <div className="bg-pink-50 rounded-lg border border-pink-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Community Language Guide</h3>
        <ChevronDown className="w-5 h-5 text-gray-500" />
      </div>
      <p className="text-gray-700 leading-relaxed text-sm">
        Welcome to our community! We value respectful, inclusive, and thoughtful dialogue. Use language that uplifts,
        avoids assumptions, and respects all identities. Speak with others, not for them. Avoid harmful stereotypes,
        slurs, or dismissive comments. Use person-first and gender-inclusive language (e.g., "people with disabilities,"
        rather than "disabled people"). Remember that behind every username is a real person whose experience matters,
        is listening, learning, and growing together. When in doubt, choose kindness and curiosity. This is a space for
        collective learning and advocacy, where everyone should feel safe to share, reflect, and engage. Let's build a
        better community through our words.
      </p>
    </div>
    <div className="bg-pink-50 rounded-lg border border-pink-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Frequently Asked Questions (FAQs)</h3>
        <ChevronDown className="w-5 h-5 text-gray-500" />
      </div>
      <div className="space-y-4">
        {[
          {
            q: "1. What is the purpose of this community?",
            a: "This is a space to exchange ideas, engage in meaningful discussions, and advocate for causes that matter. Our goal is to foster respectful dialogue and facilitate collective growth."
          },
          {
            q: "2. Who can join the community?",
            a: "Everyone is welcome—activists, learners, organizers, or anyone interested in social issues and positive change. All backgrounds and perspectives are welcome."
          },
          {
            q: "3. How do I start or join a discussion?",
            a: "To start a new post in the relevant section, be sure to reply thoughtfully to existing discussions. Be respectful and follow our community guidelines."
          },
          {
            q: "4. Are there any rules for posting?",
            a: "Yes. Be inclusive, avoid hate speech or misinformation, respect different perspectives, and stay on topic. Check our Community Language Guide for more tips."
          },
          {
            q: "5. Can I share external resources or events?",
            a: "Absolutely! If they align with our mission and values, we welcome relevant links, toolkits, or event announcements in the Resources or Events section."
          }
        ].map((faq, index) => (
          <div key={index}>
            <h4 className="font-medium text-gray-900 mb-2 text-sm">{faq.q}</h4>
            <p className="text-gray-700 text-sm leading-relaxed">{faq.a}</p>
          </div>
        ))}
      </div>
    </div>
  </div>
);

export default Resources;