import { ChevronDown } from 'lucide-react';

// Header Component
const DiscussionHeader = ({ onSortChange, onStartDiscussion }) => {
  return (
    <div className="mb-6 md:mb-8">
      {/* Title and Controls Container */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        {/* Title */}
        <h1 className="text-xl md:text-2xl font-bold text-gray-900">
          Popular Discussions
        </h1>

        {/* Controls */}
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3">
          {/* Sort Dropdown */}
          <button 
            onClick={onSortChange}
            className="flex items-center justify-center gap-2 px-4 py-2.5 border-2 border-bordergray rounded-full text-sm font-medium text-gray-700 bg-white hover:bg-black hover:text-white transition-colors w-full sm:w-[160px]"
          >
            Latest activity
            <ChevronDown size={16} />
          </button>
          
          {/* Start Discussion Button */}
          <button 
            onClick={onStartDiscussion}
            className="px-6 py-2.5 bg-primarybrown text-white rounded-full text-sm font-medium hover:bg-black transition-colors w-full sm:w-[160px]"
          >
            Start a Discussion
          </button>
        </div>
      </div>
    </div>
  );
};

export default DiscussionHeader;
