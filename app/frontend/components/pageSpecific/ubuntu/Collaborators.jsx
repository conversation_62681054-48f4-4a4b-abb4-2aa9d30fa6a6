import React from 'react';
import { MapPin } from 'lucide-react';
import { Link } from '@inertiajs/react';

const Collaborators = ({ collaborators }) => (
  <div className="space-y-4">
    {collaborators.map((collaborator, index) => (
      <div key={index} className="bg-gray-50 rounded-lg border border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center">
              <img src={collaborator.avatar} alt="Profile" className="w-12 h-12 rounded-full" />
            </div>
            <div>
              <h4 className="font-medium text-gray-900">{collaborator.name}</h4>
              <p className="text-sm text-gray-600">{collaborator.role}</p>
              <div className="flex items-center text-sm text-gray-500">
                <MapPin className="w-3 h-3 mr-1" />
                <span>{collaborator.location.address}</span>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <button 
            className="text-red-600 hover:text-red-700 text-sm font-medium"
            >
              <Link href={`/profile/${collaborator.id}`} className="no-underline">
              View Profile ↗
              </Link>
            </button>
            <button className="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700 transition-colors">
              + Connect
            </button>
          </div>
        </div>
      </div>
    ))}
    <div className="flex justify-center mt-6">
      <button className="bg-red-600 text-white px-6 py-2 rounded-full hover:bg-red-700 transition-colors text-sm">
        Load More Collaborators
      </button>
    </div>
  </div>
);

export default Collaborators;