import React, { useState, useEffect } from 'react';
import { MessageCircle, ThumbsUp, Plus, ArrowUpRight } from 'lucide-react';
import ProfileImage from '/assets/profile3.jpg';
import StartDiscussionModal from './StartDiscussionModal';
import axios from 'axios';

const Posts = ({ posts, forumId, userId, handlePostClick }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [postLikes, setPostLikes] = useState(
    posts.reduce((acc, post) => {
      acc[post.id] = post.likes || 0;
      return acc;
    }, {})
  );

  // Fetch likes for all posts on mount
  useEffect(() => {
    const fetchLikes = async () => {
      const likesMap = {};
      await Promise.all(posts.map(async (post) => {
        try {
          const res = await axios.get(`/likes?post_id=${post.id}`);
          likesMap[post.id] = res.data.total_likes;
        } catch (error) {
          console.error(`Failed to fetch likes for post ${post.id}`, error);
          likesMap[post.id] = 0; // fallback
        }
      }));
      setPostLikes((prev) => ({ ...prev, ...likesMap }));
    };

    fetchLikes();
  }, [posts]);

  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);

  const handleLikeClick = (e, postId) => {
    e.stopPropagation();
    axios.post("/likes", { post_id: postId })
      .then((res) => {
        const newLikesCount = res.data.total_likes;
        setPostLikes(prevLikes => ({
          ...prevLikes,
          [postId]: newLikesCount
        }));
      })
      .catch((error) => console.log(error));
  };

  return (
    <div className="space-y-6">
      {posts.map((post) => (
        <div
          key={post.id}
          className="rounded-lg border hover:shadow-md border-gray-200"
          onClick={() => handlePostClick(post)}
        >
          <div className="p-4">
            <div className="flex items-center space-x-3 mb-3">
              <div className="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
                <img src={post.avatar || ProfileImage} alt="Profile" className="w-10 h-10 rounded-full" />
              </div>
              <div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-600">Posted By:</span>
                  <div className="flex rounded-full px-2 bg-white">
                    <span className="text-sm rounded-full bg-white font-medium text-red-600">{post.author}</span>
                    <ArrowUpRight className="h-4 w-4" />
                  </div>
                </div>
                <span className="text-sm text-gray-500">{post.timeAgo}</span>
              </div>
            </div>

            <h3
              className="text-lg font-semibold text-gray-900 mb-3 cursor-pointer hover:text-red-600"
            >
              {post.title}
            </h3>

            <p className="text-gray-700 text-sm mb-4 leading-relaxed">
              {post.content}
            </p>

            <div className="flex items-center space-x-6 text-sm text-gray-500 pt-3">
              <button className="flex items-center space-x-1">
                <MessageCircle className="w-4 h-4 fill-black" />
                <span>{post.comments}</span>
                <span>Comments</span>
              </button>

              {/* Like button only triggers like */}
              <button
                onClick={(e) => handleLikeClick(e, post.id)}
                className="flex items-center space-x-1"
              >
                <ThumbsUp className="w-4 h-4 fill-black" />
                <span>{postLikes[post.id]}</span>
                <span>Likes</span>
              </button>
            </div>
          </div>
        </div>
      ))}

      {/* Buttons */}
      <div className="mt-6 flex justify-between">
        <button
          className="bg-red-600 text-white px-4 py-2 rounded-full hover:bg-red-700 transition-colors text-sm flex items-center space-x-2"
          onClick={openModal}
        >
          <Plus className="w-4 h-4" />
          <span>Start A New Post</span>
        </button>
        <button className="bg-red-600 text-white px-4 py-2 rounded-full hover:bg-red-700 transition-colors text-sm">
          Load More Posts
        </button>
      </div>

      <StartDiscussionModal
        isOpen={isModalOpen}
        onClose={closeModal}
        forumId={forumId}
        userId={userId}
      />
    </div>
  );
};

export default Posts;