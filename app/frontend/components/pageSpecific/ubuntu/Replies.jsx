import React, { useState } from 'react';
import { ThumbsUp } from 'lucide-react';
import axios from 'axios';

const Replies = ({ comment, depth, currentUser, showInput, onCancelReply }) => {
  const paddingLeft = depth * 32;
  const [replyText, setReplyText] = useState('');
  const [likes, setLikes] = useState(0)

  const handleReplySubmit = () => {
    if (replyText.trim()) {
      const newReply = {
        content: replyText,
        comment_id: comment.id,
      };

      axios.post("/replies", newReply)

    }
  };

  const handleReplyLikeClick = (e, id) => {
    e.stopPropagation();
    axios.post(`/likes?reply_id=${id}`)
    .then((res) => {
        const newLikesCount = res.data.total_likes;
        setLikes(newLikesCount);
      })
    .catch((error) => console.log(error));
      setReplyText('');
      onCancelReply();
  };

  const sortedReplies = comment.replies
    ? [...comment.replies].sort(
        (a, b) => new Date(b.createdAt || 0) - new Date(a.createdAt || 0)
      )
    : [];

  return (
    <div className="mt-3">
      {/* Reply Input */}
      {showInput && (
        <div className="mb-4 p-3 bg-gray-50 rounded-lg border border-gray-200">
          <div className="flex items-start space-x-2 mb-2">
            <div className="w-6 h-6 rounded-full overflow-hidden flex-shrink-0">
              <img
                src={currentUser?.avatar_url || '/assets/profile3.jpg'}
                alt="Your profile"
                className="w-full h-full object-cover"
              />
            </div>
            <span className="text-xs text-gray-600">
              Replying to <span className="font-medium">{comment.author}</span>
            </span>
          </div>
          <textarea
            value={replyText}
            onChange={(e) => setReplyText(e.target.value)}
            placeholder="Write your reply..."
            className="w-full p-2 border border-gray-300 rounded-md resize-none focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent text-sm"
            rows="3"
            autoFocus
          />
          <div className="flex justify-end space-x-2 mt-2">
            <button
              onClick={onCancelReply}
              className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-md hover:bg-gray-100 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleReplySubmit}
              className="px-3 py-1 text-sm bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={!replyText.trim()}
            >
              Reply
            </button>
          </div>
        </div>
      )}

      {/* Replies */}
      {sortedReplies.length > 0 && (
        <div className="space-y-3 mt-3 border-l-2 border-gray-200 pl-4">
          {sortedReplies.map((reply) => (
            <div key={reply.id} className="flex items-start space-x-3">
              <div className="w-6 h-6 rounded-full overflow-hidden flex-shrink-0">
                <img
                  src={reply.avatar_url}
                  alt="Profile"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-1">
                  <span className="font-medium text-gray-900 text-xs">{reply.author}</span>
                  {reply.replyToAuthor && (
                    <span className="text-gray-500 text-xs">
                      replying to <span className="font-medium">{reply.replyToAuthor}</span>
                    </span>
                  )}
                  <span className="text-gray-500 text-xs ml-auto">{reply.timeAgo}</span>
                </div>
                <p className="text-gray-700 text-xs mb-2 leading-relaxed">{reply.content}</p>
                <div className="flex items-center space-x-3 text-xs">
                  <button
                    onClick={(e) => handleReplyLikeClick(e, reply.id)}
                    className="flex items-center space-x-1 text-gray-500 hover:text-red-600 transition-colors"
                  >
                    <ThumbsUp className="w-3 h-3" />
                    <span>{reply.likes || 0}</span>
                    <span>Likes</span>
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default Replies;
