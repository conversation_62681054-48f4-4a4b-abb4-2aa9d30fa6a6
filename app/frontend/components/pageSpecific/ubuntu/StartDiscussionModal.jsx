import axios from 'axios';
import { useState, useRef, useEffect } from 'react';

// Get CSRF token from meta tags
const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

if (csrfToken) {
  axios.defaults.headers.common['X-CSRF-Token'] = csrfToken;
}
export default function StartDiscussionModal({ isOpen, onClose, forumId, userId }) {
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const modalRef = useRef(null);

  const closePopup = () => {
    setTitle('');
    setContent('');
    onClose && onClose();
  };

  const handleStartDiscussion = () => {
    if (title.trim() && content.trim()) {   
      axios.post('/posts', {
        forumId: forumId,
        title: title,
        content: content,
        userId: userId,
      })
      .then((response) => {
        window.location.reload();
      })
      .catch((error) => {
        if (error.response) {
          console.error('Error response:', error.response.data);
        } else {
          console.error('Error:', error.message);
        }
      });
      closePopup();
    }
  };

  // Effect to add and remove the event listener for clicks outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (isOpen && modalRef.current && !modalRef.current.contains(event.target)) {
        closePopup();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, closePopup]);

  return (
    <>
      {isOpen && (
        <div className="fixed inset-0 bg-white/30 backdrop-blur-md flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto" ref={modalRef}> {}
            <div className="p-8">
              <h2 className="text-xl font-semibold text-gray-800 mb-6">
                Start A New Forum Discussion
              </h2>
              <div className="space-y-4">
                <div>
                  <input
                    type="text"
                    placeholder="Enter Discussion Title"
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                    className="w-full px-4 py-3 bg-red-50 border border-red-100 rounded-lg placeholder-gray-500 text-gray-700 focus:outline-none focus:ring-2 focus:ring-red-200 focus:border-red-300 transition-all duration-200"
                  />
                </div>
                <div>
                  <textarea
                    placeholder="Share your thoughts..."
                    value={content}
                    onChange={(e) => setContent(e.target.value)}
                    rows={8}
                    className="w-full px-4 py-3 bg-red-50 border border-red-100 rounded-lg placeholder-gray-500 text-gray-700 focus:outline-none focus:ring-2 focus:ring-red-200 focus:border-red-300 transition-all duration-200 resize-none"
                  />
                </div>
              </div>
              <div className="flex justify-between mt-6">
                <button
                  onClick={closePopup}
                  className="px-6 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors duration-200"
                >
                  Cancel
                </button>
                <button
                  onClick={handleStartDiscussion}
                  className="px-6 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  disabled={!title.trim() || !content.trim()}
                >
                  Post
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}