// app/frontend/components/pageSpecific/dashboard/PeopleSection.jsx
import { useEffect, useState } from 'react';
import PeopleCard from '../people/PeopleCard';
import DashPeopleCard from './DashPeopleCard';
import { CirclePlus } from 'lucide-react';
import EmptyState from './EmptyState';

// Fetch recommendations
const getPeopleRecomendations = async () => {
  const res = await fetch("/people_recomendataion?verified=true")
  const resJson = await res.json();
  return resJson.profiles || [];
};

const getPeopleMatches = async () => {
  const res = await fetch("/people/matches")
  const resJson = await res.json();
  return resJson.profiles || [];
}

const getBookmarkedPeople = async () => {
  const res = await fetch("/people/bookmarked")
  const resJson = await res.json();
  return resJson.profiles || [];
};


const handleMatch = () => {
  alert("Match logic needs to be implemented here.");
}

const redirectPeople = () => {
  window.location.href = '/people';
}

const PeopleSection = () => {

  const [matches, setMatches] = useState([])
  const [recommendedPeople, setRecommendedPeople] = useState([]);
  const [bookmarkedPeople, setBookmarkedPeople] = useState([]);

  const person1 = {
    name: "Alice Johnson",
    profession: "Professional Photographer",
    address: "New York, USA",
    views: 15.2,
    verified: true,
    rating: 4.8,
    images: [
      "https://images.unsplash.com/photo-1534528741775-53994a69daeb?q=80&w=300&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
      "https://images.unsplash.com/photo-1534528741775-53994a69daeb?q=80&w=300&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
      "https://images.unsplash.com/photo-1534528741775-53994a69daeb?q=80&w=300&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
      "https://images.unsplash.com/photo-1534528741775-53994a69daeb?q=80&w=300&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",

    ],
  };

  useEffect(() => {
    getPeopleMatches().then(setMatches)
    getBookmarkedPeople().then(setBookmarkedPeople)
    getPeopleRecomendations().then(setRecommendedPeople);
  }, [])

  return (
    <div className="flex flex-col space-y-4 items-center w-full">
      {matches.length === 0 ? (
        <EmptyState
          title="My Matches"
          description="Looks like you haven't matched with anyone yet. Start by adding some people to your matches."
          actionText="Discover People"
          action={() => redirectPeople()}
          section={"people"}
        />
      ) : (
        <div className="w-full">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-2xl border-b-4 border-primarygreen font-semibold text-black mb-4">My Projects</h2>
            <button onClick={() => setShowRegistrationForm(true)} className="bg-primarybrown text-white px-4 py-2 rounded-full inline-flex items-center gap-2 hover:bg-black"><CirclePlus />Add New Project</button>
          </div>
          <div className="bg-gray-50 p-4 rounded-xl shadow-lg border-2 border-gray-200 overflow-x-auto whitespace-nowrap hide-scrollbar">
            <div className="inline-flex space-x-4 w-full">
              {matches.map((person) => (
                <div key={person.id} className="flex-none min-w-80 max-w-96">
                  <PeopleCard person={person} />
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {bookmarkedPeople.length === 0 ? (
        <EmptyState
          title="Bookmarked People"
          description="Looks like you haven't bookmarked any people yet. Start by discovering and bookmarking people."
          actionText="Discover People"
          action={() => redirectPeople()}
          section={"people"}
        />
      ) : (
        <div className="w-full max-w-6xl">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-2xl border-b-4 border-primaryyellow font-semibold text-black mb-4">Bookmarked People</h2>
          </div>
          <div className="space-y-3 bg-gray-50 p-4 rounded-xl shadow-lg border-2 border-gray-200 p-4 overflow-y-auto max-h-144">
            {bookmarkedPeople.map((profile) => (
              <div key={profile.id} className="w-full">
                <DashPeopleCard person={profile} isBookmarked={true} />
              </div>
            ))}
          </div>
        </div>
      )}
      {recommendedPeople.length === 0 ? (
        <EmptyState
          title="Suggested Matches"
          description="Looks like you haven't matched with anyone yet. Start by discovering and bookmarking people."
          actionText="Discover People"
          action={() => redirectPeople()}
          section={"people"}
        />
      ) : (
        <div className="w-full max-w-6xl">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-2xl border-b-4 border-primaryyellow font-semibold text-black mb-4">Suggested Matches</h2>
          </div>
          <div className="space-y-3 bg-gray-50 p-4 rounded-xl shadow-lg border-2 border-gray-200 p-4 overflow-y-auto max-h-144">
            {matches.map((profile) => (
              <div key={profile.id} className="w-full">
                <DashPeopleCard person={profile} />
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

export default PeopleSection;
