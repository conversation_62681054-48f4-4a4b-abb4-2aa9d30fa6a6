import people_oops from '../../../assets/people_oops.png';
import projects_oops from '../../../assets/projects_oops.png';
import places_oops from '../../../assets/places_oops.png';
import ubuntu_oops from '../../../assets/ubuntu_oops.png';

const EmptyState = ({title, description, actionText, action, section}) => {
    let image, borderColor;
    switch (section) {
        case 'people':
            image = people_oops;
            borderColor = 'primaryyellow';
            break;
        case 'projects':
            image = projects_oops;
            borderColor = 'primarygreen';
            break;
        case 'places':
            image = places_oops;
            borderColor = 'primaryorange';
            break;
        case 'ubuntu':
            image = ubuntu_oops;
            borderColor = 'primarymaroon';
            break;
        default:
            image = people_oops;
            borderColor = 'primaryyellow';
    }

    return (
        <div className="flex flex-col items-center w-full">
            <div className="w-full">
                <div className="flex justify-between items-center mb-2">
                    <h2 className={`text-2xl border-b-4 border-${borderColor} font-semibold text-black mb-4`}>{title}</h2>
                </div>
                <div className=" flex flex-col space-y-4 items-center bg-gray-50 p-4 rounded-xl shadow-lg border-2 border-gray-200">
                    <div className="flex flex-col items-center justify-center">
                        <img src={image} alt="No People Found" className="w-48 h-48 object-cover" />
                        <div className="text-gray-900 text-xl max-w-2/3 text-center font-medium">{description}
                        </div>
                    </div>
                    <div className="mb-4">
                        <button
                            onClick={() => action()}
                            className="bg-primarybrown text-white px-4 py-2 rounded-full inline-flex items-center gap-2 hover:bg-black"
                        >
                            {actionText}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
}

export default EmptyState;
