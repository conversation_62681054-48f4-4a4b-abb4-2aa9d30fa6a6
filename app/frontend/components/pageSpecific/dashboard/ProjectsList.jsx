import DashProjectsCard from './DashProjectsCard';
import EmptyState from './EmptyState';

const ProjectListSection = ({ title, projects, emptyStateProps, renderCardProps }) => {
  if (projects.length === 0) {
    return <EmptyState {...emptyStateProps} />;
  }

  return (
    <div className="w-full bg-white rounded-xl shadow-lg">
      <div className="p-4 flex justify-between items-center mb-2">
        <h2 className="text-2xl font-semibold text-black mb-4">{title}</h2>
      </div>
      <div className="space-y-3 p-4 overflow-y-auto max-h-144">
        {projects.map((project) => (
          <div key={project.id}>
            <DashProjectsCard project={project} {...renderCardProps(project)} />
          </div>
        ))}
      </div>
    </div>
  );
};

export default ProjectListSection;
