/**
 * PortfolioProject Component
 * Individual portfolio project display with optional edit/delete functionality
 * @param {string} props.image - Image URL
 * @param {string} props.title - Project title
 * @param {string} props.description - Project description (optional)
 */
const PortfolioProject = ({ 
  image, 
  title, 
  description,  
}) => {
  return (
    <div className="space-y-4 relative group">
      <div className="aspect-video bg-gray-100 rounded-lg overflow-hidden">
        <img
          src={image || "/api/placeholder/400/225"}
          alt={title || "Portfolio project"}
          className="w-full h-full object-cover"
        />
      </div>
      
      <div>
        <h3 
          className="text-lg font-medium text-gray-800 cursor-pointer hover:text-blue-600"
          onClick={() => {
            // Handle click on title if needed
          }}
        >
          {title || "Project Title goes here"}
        </h3>
        {description && (
          <p className="text-sm text-gray-600 mt-1">{description}</p>
        )}
      </div>
    </div>
  );
};

export default PortfolioProject;