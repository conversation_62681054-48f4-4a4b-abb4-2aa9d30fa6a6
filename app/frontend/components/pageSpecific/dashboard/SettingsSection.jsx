import { useState } from 'react';
import { User, Monitor, Bell, ShieldUser } from 'lucide-react';
import SettingsSubsection from './SettingsSubsection';

const SettingsSection = () => {
    const [accountSettings, setAccountSettings] = useState({
        showProfilePublicly: false,
        enableAccountRecovery: true,
        enableSignInAlerts: false
    });

    const [displaySettings, setDisplaySettings] = useState({
        enableDarkMode: false,
        useSystemTheme: false,
        compactLayout: false
    });

    const [notificationSettings, setNotificationSettings] = useState({
        emailNotifications: true,
        pushNotifications: true,
        activityReminders: true
    });

    const [privacySettings, setPrivacySettings] = useState({
        showOnlineStatus: 'public',
        allowSearchEmail: false,
        showActivity: true
    });

    const handleAccountChange = (key, value) => {
        setAccountSettings(prev => ({ ...prev, [key]: value }));
    };

    const handleDisplayChange = (key, value) => {
        setDisplaySettings(prev => ({ ...prev, [key]: value }));
    };

    const handleNotificationChange = (key, value) => {
        setNotificationSettings(prev => ({ ...prev, [key]: value }));
    };

    const handlePrivacyChange = (key, value) => {
        setPrivacySettings(prev => ({ ...prev, [key]: value }));
    };

    const accountSettingsConfig = [
        {
            key: 'showProfilePublicly',
            label: 'Show Profile Publicly',
            description: 'Enable this to let other users view your profile. Turn it off to stay private and hidden from search results.',
            color: 'blue'
        },
        {
            key: 'enableAccountRecovery',
            label: 'Enable Account Recovery/File Publicly',
            description: 'Allows you to recover your account using email or phone verification in case you forget your credentials.',
            color: 'blue'
        },
        {
            key: 'enableSignInAlerts',
            label: 'Enable sign-in alerts',
            description: 'Get notified whenever someone signs in to your account from a new device or location.',
            color: 'blue'
        }
    ];

    const displaySettingsConfig = [
        {
            key: 'enableDarkMode',
            label: 'Enable Dark Mode',
            description: 'Switch to a darker interface that\'s easier on the eyes, especially in low-light environments.',
            color: 'blue'
        },
        {
            key: 'useSystemTheme',
            label: 'Use System Theme',
            description: 'Automatically syncs the app\'s appearance with your device\'s system-wide theme settings (light or dark).',
            color: 'blue'
        },
        {
            key: 'compactLayout',
            label: 'Compact Layout',
            description: 'Tightens spacing and UI elements for a more condensed, efficient view — great for small screens or power users.',
            color: 'blue'
        }
    ];

    const notificationSettingsConfig = [
        {
            key: 'emailNotifications',
            label: 'Email Alerts',
            description: 'Receive important updates and alerts via email.',
            color: 'blue'
        },
        {
            key: 'pushNotifications',
            label: 'Push Notifications',
            description: 'Get instant notifications on your device.',
            color: 'blue'
        },
        {
            key: 'activityReminders',
            label: 'Activity Reminders',
            description: 'Receive critical alerts via text message.',
            color: 'blue'
        }
    ];

    const privacySettingsConfig = [
        {
            key: 'showOnlineStatus',
            label: 'Show Online Status',
            description: 'Allow friends or team members to see your recent activity (like posts, edits, or logins).',
            color: 'blue'
        },
        {
            key: 'allowSearchEmail',
            label: 'Allow Search by Email',
            description: 'Let others find your profile by entering your email. Disable to keep your email private.',
            color: 'blue'
        },
        {
            key: 'showActivity',
            label: 'Share Activity Status',
            description: 'Let others know when you\'re active or recently online. Disable to appear invisible.',
            color: 'blue'
        }
    ];

    return (
        <div className="mx-auto text-inter p-6 min-h-screen">
            <SettingsSubsection
                title="Account"
                icon={<User />}
                settings={accountSettingsConfig}
                values={accountSettings}
                onChange={handleAccountChange}
            />

            <SettingsSubsection
                title="Display"
                icon={<Monitor />}
                settings={displaySettingsConfig}
                values={displaySettings}
                onChange={handleDisplayChange}
            />

            <SettingsSubsection
                title="Notifications"
                icon={<Bell />}
                settings={notificationSettingsConfig}
                values={notificationSettings}
                onChange={handleNotificationChange}
            />

            <SettingsSubsection
                title="Privacy"
                icon={<ShieldUser />}
                settings={privacySettingsConfig}
                values={privacySettings}
                onChange={handlePrivacyChange}
            />
        </div>
    );
};

export default SettingsSection;