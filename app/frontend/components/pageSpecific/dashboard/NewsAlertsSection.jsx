// app/frontend/components/pageSpecific/dashboard/NewsAlertsSection.jsx
import { useState, useEffect } from 'react';
import axios from 'axios';
import { formatDistanceToNow } from 'date-fns';
import NotificationItem from './NotificationItem';
import Pagination from './Pagination';
import EmptyState from './EmptyState';

const NewsAlertsSection = ({ notifications = [] }) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [allNotifications, setAllNotifications] = useState([]);
  const notificationsPerPage = 5;

  useEffect(() => {
    setAllNotifications(notifications);
  }, [notifications]);

  const totalPages = Math.ceil(allNotifications.length / notificationsPerPage);
  const indexOfLastNotification = currentPage * notificationsPerPage;
  const indexOfFirstNotification = indexOfLastNotification - notificationsPerPage;
  const currentNotifications = allNotifications.slice(
    indexOfFirstNotification,
    indexOfLastNotification
  );

  const handlePageChange = (pageNumber) => {
    if (pageNumber < 1 || pageNumber > totalPages) return;
    setCurrentPage(pageNumber);
  };
  const csrfToken = document.querySelector('meta[name="csrf-token"]')?.content;

const handleNotificationClick = async (notification) => {
  if (!notification.read) {
    try {
      const response = await axios.patch(
        `/notifications/${notification.id}/mark_as_read`,
        {}, 
        {
          headers: {
            'X-CSRF-Token': csrfToken,
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        }
      );

      setAllNotifications(prev =>
        prev.map(n =>
          n.id === notification.id ? { ...n, read: true } : n
        )
      );

      // if (onNotificationRead && response.data.unread_count !== undefined) {
      //   onNotificationRead(response.data.unread_count);
      // }
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
      console.error('Server response:', error?.response?.data);
    }
  }

    // Handle navigation
    if (notification.notifiable_type === 'Place') {
      window.location.href = `/place/${notification.notifiable_id}`;
    } else if (notification.notifiable_type === 'Project') {
      window.location.href = `/project/${notification.notifiable_id}`;
    } else if (notification.notifiable_type === 'Forum') {
      window.location.href = `/forums/${notification.notifiable_id}`;
    }
    if (notification.url) {
      window.location.href = notification.url;
    }
  };

  const formattedNotifications = currentNotifications.map(n => ({
    id: n.id,
    type: "bookmark",
    title: n.message,
    description: "",
    actionText: "View",
    timeAgo: formatDistanceToNow(new Date(n.created_at), { addSuffix: true }),
    isNew: !n.read,
    read: n.read,
    notifiable_id: n.notifiable_id,
    notifiable_type: n.notifiable_type,
    url: n.url
  }));

  if (allNotifications.length === 0) {
    return (
      <EmptyState
        title="No Notifications Yet"
        description="It looks like you don't have any notifications right now. Check back later for updates!"
        actionText="Go to Dashboard"
        action={() => window.location.href = "/profile"}
      />
    );
  }

  return (
    <div className="w-full flex flex-col space-y-10 p-4">
      <div className="w-full bg-white shadow-md rounded-xl p-4">
        {/* ... existing header code ... */}
        
        <div className="space-y-4">
          {formattedNotifications.map((notification) => (
            <NotificationItem 
              key={notification.id} 
              {...notification} 
              onClick={() => handleNotificationClick(notification)}
            />
          ))}
        </div>
      </div>
      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={handlePageChange}
      />
    </div>
  );
};

export default NewsAlertsSection;