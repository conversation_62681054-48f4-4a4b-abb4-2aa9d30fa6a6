import { useState, useRef, useEffect } from 'react';
import { MoreVertical, Send, Paperclip, Smile, X } from 'lucide-react';
import Picker from '@emoji-mart/react';
import data from '@emoji-mart/data';

const ChatView = ({ currentChatId, user }) => {
  const [message, setMessage] = useState('');
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [selectedFile, setSelectedFile] = useState(null);
  const [imagePreviewUrl, setImagePreviewUrl] = useState(null);
  const messagesContainerRef = useRef(null);
  const emojiButtonRef = useRef(null);
  const fileInputRef = useRef(null);

  // Dummy chat data - replace with real data later
  const chatData = {
    1: {
      name: "Rukia",
      avatar: "https://randomuser.me/api/portraits/women/44.jpg",
      isOnline: true,
      messages: [
        {
          id: 1,
          sender: "other",
          content: "Hey <PERSON>, I came across your portfolio and just had to say your work is incredible.",
          timestamp: "2025-06-20T14:53:00Z",
          avatar: "https://randomuser.me/api/portraits/women/44.jpg"
        },
        {
          id: 2,
          sender: "me",
          content: "Rukia Hey team! We're exploring potential meetup spots or coworking",
          timestamp: "2025-06-20T14:53:00Z",
          isDelivered: true
        },
        {
          id: 3,
          sender: "other",
          content: "Rukia Hi Dancers we're thinking of linking up with other creative groups like Kuona Creatives...",
          timestamp: "2025-06-20T14:53:00Z",
          avatar: "https://randomuser.me/api/portraits/women/44.jpg"
        },
        {
          id: 4,
          sender: "me",
          content: "Rukia Hey team! We're exploring potential meetup spots or coworking hubs",
          timestamp: "2025-06-20T14:53:00Z",
          isDelivered: true
        },
        {
          id: 5,
          sender: "other",
          content: "Rukia Hey team! We're exploring potential meetup spots or coworking hubs where we can host in-person writing sessions.",
          timestamp: "2025-06-20T14:53:00Z",
          avatar: "https://randomuser.me/api/portraits/women/44.jpg"
        },
        {
          id: 6,
          sender: "other",
          content: "Rukia Hi Dancers we're thinking of linking up with other creative groups like Kuona Creatives...",
          timestamp: "2025-06-20T14:53:00Z",
          avatar: "https://randomuser.me/api/portraits/women/44.jpg"
        },
        {
          id: 7,
          sender: "me",
          content: "Rukia Hey team! We're exploring potential meetup spots or coworking hubs",
          timestamp: "2025-06-20T14:53:00Z",
          isDelivered: true
        },
        {
          id: 8,
          sender: "other",
          content: "Rukia Hey team! We're exploring potential meetup spots or coworking hubs where we can host in-person writing sessions.",
          timestamp: "2025-06-20T14:53:00Z",
          avatar: "https://randomuser.me/api/portraits/women/44.jpg"
        }
      ]
    },
    2: {
      id: 2,
      name: "Technical Writers",
      avatar: "https://randomuser.me/api/portraits/men/32.jpg",
      isOnline: true,
      messages: []
    },
    3: {
      id: 3,
      name: "Nafasi",
      avatar: "https://randomuser.me/api/portraits/men/15.jpg",
      isOnline: false,
      messages: []
    },
    4: {
      id: 4,
      name: "Kuona Artist Creatives",
      avatar: "https://randomuser.me/api/portraits/women/20.jpg",
      isOnline: false,
      messages: [],
    }
  };

  const currentChat = chatData[currentChatId];

  useEffect(() => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
    }
  }, [currentChatId, currentChat?.messages])

  useEffect(() => {
    setShowEmojiPicker(false);
  }, [currentChatId]);

  const handleClickOutside = (event) => {
    if (
      showEmojiPicker &&
      emojiButtonRef.current &&
      !emojiButtonRef.current.contains(event.target) &&
      !event.target.closest('.EmojiPickerReact')
    ) {
      setShowEmojiPicker(false);
    }
  };


  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
      if (allowedTypes.includes(file.type)) {
        setSelectedFile(file);
        // Create a URL for the image preview
        const reader = new FileReader();
        reader.onloadend = () => {
          setImagePreviewUrl(reader.result);
        };
        reader.readAsDataURL(file);
      } else {
        alert('Only images (JPG, PNG, GIF) are allowed.');
        setSelectedFile(null);
        setImagePreviewUrl(null);
      }
    }
    e.target.value = '';
  };

  const clearImagePreview = () => {
    setSelectedFile(null);
    setImagePreviewUrl(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = ''; // Important: Clear the file input value
    }
  };

  // Function to trigger file input click
  const triggerFileInput = () => {
    fileInputRef.current.click();
  };

  // Function to handle emoji selection (to be passed to emoji picker component)
  const handleEmojiSelect = (emoji) => {
    setMessage(prevMessage => prevMessage + emoji.native);
  };

  const handleSendMessage = (e) => {
    e.preventDefault();
    if (message.trim() || selectedFile) {
      if (selectedFile) {
        // Here you would implement the logic to upload the file
        // Example: Call an API to upload the file
        // uploadFile(selectedFile).then(response => {
        //   // Handle successful upload and send message with file URL
        //   // Then send a message object that includes the file URL
        // }).catch(error => {
        //   console.error('File upload failed:', error);
        // });
        setSelectedFile(null);
      }

      setMessage('');
    }
  };

  const formatTime = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
  };

  if (!currentChat) {
    return (
      <div className="flex items-center justify-center h-full bg-white">
        <p className="text-gray-500">Select a chat to start messaging</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-fit max-w-6xl mx-auto">
      {/* Chat Header */}
      <div className="p-4 border-b border-gray-200 bg-white rounded-t-xl">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <img
              src={currentChat.avatar}
              alt={currentChat.name}
              className="w-10 h-10 rounded-full object-cover"
            />
            <div>
              <h2 className="font-semibold text-gray-900">{currentChat.name}</h2>
              <p className="text-sm text-primarybrown">
                {currentChat.isOnline ? 'Active Now' : 'Last seen recently'}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <button className="p-2 text-gray-600 hover:bg-gray-100 rounded-full">
              <MoreVertical className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>

      {/* Messages */}
      <div ref={messagesContainerRef} className="h-[60vh] overflow-y-auto p-6 space-y-4 bg-white">
        {currentChat.messages.map((msg) => (
          <div
            key={msg.id}
            className={`flex ${msg.sender === 'me' ? 'justify-end' : 'justify-start'}`}
          >
            <div className={`flex flex-col ${msg.sender === 'me' ? 'items-end' : 'items-start'} space-y-2 max-w-xs lg:max-w-md`}>
              <div
                className={`px-4 py-2 rounded-2xl shadow-sm ${msg.sender === 'me'
                  ? 'bg-primarybrown text-white rounded-tr-md'
                  : 'bg-gray-100 text-gray-800 rounded-tl-md'
                  }`}
              >
                <p className="text-sm">{msg.content}</p>
              </div>
              <div className={`text-xs text-gray-500 ${msg.sender === 'me' ? 'mr-1' : 'ml-1'} flex items-center space-x-1`}>
                <span>{formatTime(msg.timestamp)}</span>
                {msg.sender === 'me' && msg.isDelivered && (
                  <span className="text-primarybrown">✓✓</span>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
      {/* Message Input */}
      <div className="p-4 border-t border-gray-200 bg-white rounded-b-xl">
        {imagePreviewUrl && ( // Conditionally render image preview
          <div className="relative mb-2 p-2 border border-gray-200 rounded-lg bg-gray-50 max-w-[200px]">
            <img src={imagePreviewUrl} alt="Preview" className="max-w-full h-auto rounded-md" />
            <button
              onClick={clearImagePreview}
              className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 text-xs hover:bg-red-600"
              aria-label="Remove image"
            >
              <X className="w-3 h-3" />
            </button>
          </div>
        )}
        <form onSubmit={handleSendMessage} className="flex items-end space-x-2">
          <div className="flex-1 relative">
            <input
              type="text"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Type a message..."
              className="w-full px-4 py-3 border border-gray-300 rounded-full focus:ring-2 focus:ring-primarybrown focus:border-primarybrown resize-none"
            />
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center space-x-1">
              <input
                type="file"
                ref={fileInputRef}
                className="hidden"
                onChange={handleFileChange}
                accept="image/jpeg,image/png,image/gif,.git"
              />
              <button
                type="button"
                onClick={triggerFileInput}
                className="p-1 text-gray-400 hover:text-gray-600"
              >
                <Paperclip className="w-4 h-4" />
              </button>
              <button
                type="button"
                onClick={() => setShowEmojiPicker(prev => !prev)}
                ref={emojiButtonRef}
                className="p-1 text-gray-400 hover:text-gray-600"
              >
                <Smile className="w-4 h-4" />
              </button>
              {/* Emoji Picker */}
              {showEmojiPicker && (
                <div className="absolute bottom-full right-0 mb-2 z-10">
                  <Picker data={data} onEmojiSelect={handleEmojiSelect} onClickOutside={handleClickOutside} />
                </div>
              )}
            </div>
          </div>
          <button
            type="submit"
            disabled={!message.trim()}
            className="p-3 bg-primarybrown text-white rounded-full hover:bg-primarybrown disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Send className="w-5 h-5" />
          </button>
        </form>
      </div>
    </div>
  );
};

export default ChatView;
