/**
 * StatusField Component
 * Reusable seeking status dropdown field
 * @param {string} props.value - Current value
 * @param {Function} props.onChange - Change handler function
 * @param {string} props.name - Name attribute for the select
 * @param {string} props.label - Label text for the field
 * @param {Array} props.options - Array of options [{value: string, label: string}]
 * @param {string} props.helperText - Helper text displayed below the select field
 */
const StatusField = ({ 
  value, 
  onChange, 
  name = 'seeking',
  label = 'What are you looking for?',
  options = [
    { value: '', label: 'Not seeking contributors at this time' },
    { value: 'seeking_contributors', label: 'Seeking Contributors' }
  ],
  helperText = 'This will be displayed as a badge on your projects/places card. You can change this anytime.'
}) => {
  return (
    <div>
      <label className="block font-medium text-gray-700 mb-1">
        {label}
      </label>
      <select
        name={name}
        value={value}
        onChange={onChange}
        className="w-full border rounded-2xl p-3"
      >
        {options.map(option => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
      <p className="text-sm text-gray-500 mt-1">
        {helperText}
      </p>
    </div>
  );
};

export default StatusField;
