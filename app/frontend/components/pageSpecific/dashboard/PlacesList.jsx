import DashPlacesCard from './DashPlaceCard';
import EmptyState from './EmptyState';

const PlacesList = ({ title, places = [], emptyStateProps, renderCardProps }) => {
  if (places.length === 0) {
    return <EmptyState {...emptyStateProps} />;
  }

  return (
    <div className="w-full bg-white rounded-xl shadow-lg">
      <div className="p-4 flex justify-between items-center mb-4">
        <h2 className="text-2xl font-semibold text-black mb-2">{title}</h2>
      </div>
      <div className="space-y-3 p-4 overflow-y-auto max-h-144">
        {places.map((place) => (
          <div key={place.id}>
            <DashPlacesCard place={place} {...renderCardProps(place)} />
          </div>
        ))}
      </div>
    </div>
  );
};

export default PlacesList;
