import { useState, useEffect } from 'react';
import UbuntuRegistrationForm from './UbuntuRegistrationForm';
import UbuntuPageCard from '../ubuntu/UbuntuPageCard';
import { CirclePlus } from 'lucide-react';
import EmptyState from './EmptyState';
import UbuntuList from './UbuntuList';

// Ubuntu Section Component
const UbuntuSection = ({ user }) => {
    const [recommendedUbuntu, setRecommendedUbuntu] = useState([]);
    const [associatedUbuntu, setAssociatedUbuntu] = useState([]);
    const [bookmarkedUbuntu, setBookmarkedUbuntu] = useState([]);
    const [showRegistrationForm, setShowRegistrationForm] = useState(false);
    const [userUbuntu, setUserUbuntu] = useState([]);
    const [error, setError] = useState(null);


    // Check URL parameters for sub-section navigation
    useEffect(() => {
        const urlParams = new URLSearchParams(window.location.search);
        const view = urlParams.get('view');

            // If view=create-place, show the registration form
        if (view === 'create-forum') {
            setShowRegistrationForm(true);
        }
    }, []);

    const getRecommendedUbuntu = async () => {
        try {
            const res = await fetch("/ubuntu/recommendations?verified=true");
            const resJson = await res.json();
            setRecommendedUbuntu(resJson.ubuntu || []);
        } catch (err) {
            setError("Sorry, we couldn't load ubuntu at the moment.");
        }
    };

    const getUserUbuntu = async () => {
        try {
            const res = await fetch("/user_ubuntu_channels");
            const resJson = await res.json();
            setUserUbuntu(resJson.forums);
        } catch (err) {
            setError("Error fetching user ubuntu:", err);
        }
    }

    const getBookmarkedUbuntu = async () => {
        try {
            const res = await fetch("/forums/bookmarked");
            const resJson = await res.json();
            setBookmarkedUbuntu(resJson.ubuntu || []);
        } catch (err) {
            setError("Error fetching bookmarked ubuntu:", err);
        }
    };

    const getAssociatedUbuntu = async () => {
        try {
            const res = await fetch("/associated_ubuntu");
            const resJson = await res.json();
            setAssociatedUbuntu(resJson.ubuntu || []);
        } catch (err) {
            setError("Error fetching associated ubuntu:", err);
        }
    };

    const handleJoin = () => {
        alert("Join ubuntu functionality should be implemented here.");
    }

    useEffect(() => {
        getRecommendedUbuntu();
        getUserUbuntu();
        getBookmarkedUbuntu();
        getAssociatedUbuntu();
    }, []);

    const handleGoToUbuntu = () => {
        window.location.href = '/ubuntu';
    }

    const handleShowForm = () => {
        setShowRegistrationForm(true);
        const params = new URLSearchParams(window.location.search);
        params.set('view', 'create-forum');
        const newUrl = `${window.location.pathname}?${params.toString()}`;
        window.history.replaceState({}, '', newUrl);
    };

    const handleBackFromForm = () => {
        setShowRegistrationForm(false);
        const params = new URLSearchParams(window.location.search);
        params.delete('view');
        const newUrl = `${window.location.pathname}?${params.toString()}`;
        window.history.replaceState({}, '', newUrl);
    };

    return (
        <div className="flex flex-col space-y-4 items-center w-full">
            {showRegistrationForm ? (
                <div className="p-6 rounded-xl w-full flex justify-center items-center">
                    <UbuntuRegistrationForm onBack={handleBackFromForm} />
                </div>
            ) : (
                <>
                    {userUbuntu.length === 0 ? (
                        <EmptyState
                            title="My Forums"
                            description="Looks like you haven't created any forums yet. Start by creating a new forum."
                            actionText="Create New Forum"
                            action={handleShowForm}
                            section={"ubuntu"}
                        />
                    ) : (
                        <div className="w-full">
                            <div className="flex justify-between items-center mb-4">
                                <h2 className="text-2xl border-b-4 border-primarymaroon font-semibold text-black mb-4">My Forums</h2>
                                <button onClick={handleShowForm} className="bg-primarybrown text-white px-4 py-2 rounded-full inline-flex items-center gap-2 hover:bg-black"><CirclePlus />Add New Forum</button>
                            </div>
                            <div className="bg-gray-50 p-4 rounded-xl shadow-lg border-2 border-gray-200 overflow-x-auto whitespace-nowrap hide-scrollbar">
                                <div className="inline-flex space-x-4 w-full">
                                    {userUbuntu?.map((forum) => (
                                        <div key={forum.id} className="flex-none max-w-80">
                                            <UbuntuPageCard forum={forum} />
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                    )}

                    <UbuntuList
                        title="Associated Ubuntu"
                        forums={associatedUbuntu}
                        emptyStateProps={{
                            title: "No Associated Ubuntu",
                            description: "You will see ubuntu you interact with here. Join or create a ubuntu to get started!",
                            actionText: "Go to Ubuntu",
                            action: handleGoToUbuntu,
                            section: "ubuntu",
                        }}
                        renderCardProps={() => ({})}
                    />

                    <UbuntuList
                        title="Bookmarked Ubuntu"
                        forums={bookmarkedUbuntu}
                        emptyStateProps={{
                            title: "No Bookmarked Ubuntu",
                            description: "You will see ubuntu you bookmark here. Bookmark a ubuntu to get started!",
                            actionText: "Go to Ubuntu",
                            action: handleGoToUbuntu,
                            section: "ubuntu",
                        }}
                        renderCardProps={() => ({ isBookmarked: true })}
                    />

                    <UbuntuList
                        title="Suggested for You"
                        forums={recommendedUbuntu}
                        emptyStateProps={{
                            title: "No Suggested Ubuntu",
                            description: "Explore and join new ubuntu!",
                            actionText: "Go to Ubuntu",
                            action: handleGoToUbuntu,
                            section: "ubuntu",
                        }}
                        renderCardProps={() => ({ showJoin: true, handleJoin: handleJoin })}
                    />
                </>
            )}
        </div>
    );
};

export default UbuntuSection;
