import { Link } from '@inertiajs/react';
import { Users } from 'lucide-react';

const ChatItem = ({ chat, onOpenChat }) => {
    const formatTime = (timestamp) => {
        if (!timestamp) return '';
        const date = new Date(timestamp);
        const now = new Date();
        const diffInHours = (now - date) / (1000 * 60 * 60);

        if (diffInHours < 24) {
            return date.toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: false
            });
        } else {
            return date.toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric'
            });
        }
    };

    const truncateMessage = (message, maxLength = 80) => {
        if (!message) return '';
        return message.length > maxLength
            ? message.substring(0, maxLength) + '......'
            : message;
    };

    return (
        <Link
            key={chat.id}
            href={`/chats/${chat.id}`}
            onClick={(e) => {
                e.preventDefault();
                onOpenChat(chat.id);
            }}
            className="flex items-center px-6 py-4 hover:bg-gray-50 transition-colors"
        >
            {/* Avatar */}
            <div className="flex-shrink-0 mr-4">
                {chat.avatar ? (
                    <img
                        src={chat.avatar}
                        alt={chat.name}
                        className="w-12 h-12 rounded-full object-cover"
                    />
                ) : (
                    <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center">
                        <span className="text-white font-semibold text-lg">
                            {chat.name?.charAt(0)?.toUpperCase() || '?'}
                        </span>
                    </div>
                )}
            </div>

            {/* Chat Info */}
            <div className="flex-1 min-w-0 mr-4">
                <div className="flex items-center mb-1">
                    <h3 className="text-lg font-semibold text-gray-900 truncate">
                        {chat.name}
                    </h3>
                    {chat.isGroup && (
                        <Users className="w-4 h-4 text-gray-400 ml-2 flex-shrink-0" />
                    )}
                </div>
                <p className="text-gray-600 text-sm leading-relaxed">
                    {truncateMessage(chat.lastMessage)}
                </p>
            </div>

            {/* Time and Badge */}
            <div className="flex flex-col items-end">
                <span className="text-sm text-gray-500 mb-2">
                    {formatTime(chat.lastMessageTime)}
                </span>
                {chat.unreadCount > 0 && (
                    <div className="bg-primarybrown text-white text-xs font-bold rounded-full min-w-[24px] h-6 flex items-center justify-center px-2">
                        {chat.unreadCount > 99 ? '99+' : chat.unreadCount}
                    </div>
                )}
            </div>
        </Link>
    );
}

export default ChatItem;
