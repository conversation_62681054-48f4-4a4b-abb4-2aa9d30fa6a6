import { router } from '@inertiajs/react';
import { useState } from 'react';
import FormHeader from './FormHeader';
import ImageUpload from './ImageUpload';
import FormField from './FormField';
import DropdownSelection from './DropdownSelection';
import ActionButtons from './ActionButtons';
import GalleryUpload from './GalleryUpload';
import StatusField from './StatusField';

const tagsOptions = [
  'Activism', 'Architecture', 'Climate Action',
  'Community Building', 'Content Creation', 'Crafts',
  'Digital Marketing', 'Education', 'Event Management',
  'Facilitation', 'Filmmaking', 'Gaming',
  'Graphic Design', 'Music Production', 'Photography',
  'Project Management', 'Social Entrepreneurship', 'Storytelling',
  'Training', 'UX/UI Design', 'Writing'
]

const categories = [
  "Baraza", "Shared Wisdom", "Nipe Nikupe", "Harambee", "Collab Zone"
]

const UbuntuRegistrationForm = ({ onBack, onSuccess }) => {
  const [error, setError] = useState(null);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    categories: '', // Changed from array to string for single selection
    collaborators: [],
    status: 'public',
  });

  const [profileImage, setProfileImage] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);
  // const [galleryImages, setGalleryImages] = useState([]);
  // const [galleryPreviews, setGalleryPreviews] = useState([]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    setProfileImage(file);
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => setImagePreview(reader.result);
      reader.readAsDataURL(file);
    }
  };

  const handleRemoveImage = () => {
    setImagePreview(null);
    setProfileImage(null);
  };

  // const handleGalleryChange = (e) => {
  //   const files = Array.from(e.target.files);
  //   setGalleryImages(files);

  //   const previews = files.map(file => {
  //     return new Promise((resolve) => {
  //       const reader = new FileReader();
  //       reader.onloadend = () => resolve(reader.result);
  //       reader.readAsDataURL(file);
  //     });
  //   });

  //   Promise.all(previews).then(setGalleryPreviews);
  // };

  const handleSubmit = (e) => {
    e.preventDefault();

    const payload = new FormData();
    payload.append('forum[title]', formData.title);
    payload.append('forum[description]', formData.description);
    payload.append('forum[status]', formData.status);
    payload.append('forum[category][]', formData.categories);
    payload.append('forum[collaborators][]', formData.collaborators)

    if (profileImage) {
      payload.append('forum[avatar]', profileImage);
    }
    
    router.post("/forums", payload, {
      preserveScroll: true,
      onSuccess: () => {
        if (onSuccess) onSuccess();
        onBack();
      },
      onError: (errors) => {
        // Extract and display first error
        const firstError = Object.values(errors)[0];
        setError(firstError || "An error occurred. Please try again.");
      }
    });
  };

  const handleSaveDraft = () => {
    alert("Pending feature!");
  };

  return (
    <form
      onSubmit={handleSubmit}
      className="p-6 relative rounded-xl font-inter space-y-6 w-full"
    >
      <FormHeader
        title="Register Forum"
        onClose={onBack}
        borderColor="border-primarymaroon"
      />
      <div className="p-6 bg-white space-y-6 rounded-2xl shadow-lg">
        <ImageUpload
          label="Forum Profile Picture"
          image={profileImage}
          onImageChange={handleImageChange}
          onImageRemove={handleRemoveImage}
          imagePreview={imagePreview}
        />
        <FormField
          label="Name"
          name="title"
          placeholder="Name of your forum..."
          value={formData.title}
          onChange={handleChange}
          required
        />
        <FormField
          label="Description"
          name="description"
          type="textarea"
          placeholder="Tell us about your forum..."
          value={formData.description}
          onChange={handleChange}
          rows={4}
          required
        />
        <DropdownSelection
          formData={formData}
          setFormData={setFormData}
          options={categories}
          label="Category"
          placeholder="Choose a topic category"
          fieldName="categories"
          singleSelection={true}
          maxSelections={1}
        />
        {/* <DropdownSelection
          formData={formData}
          setFormData={setFormData}
          options={tagsOptions}
          label="Tags (max 5)"
          placeholder="e.g Climate Action"
          fieldName="tags" // For multiple selection, you would use this
          singleSelection={false}
          maxSelections={5}
        /> */}
        {/* <GalleryUpload
          images={galleryImages}
          imagePreviews={galleryPreviews}
          onImagesChange={handleGalleryChange}
          required
        /> */}
        <FormField
          label="Collaborators (emails or usernames, comma-separated)"
          name="collaborators"
          placeholder="Collaborators that will be working on your project"
          value={formData.collaborators}
          onChange={handleChange}
        />
        <StatusField
          value={formData.status}
          onChange={handleChange}
          name="status"
          label="Status"
          options={[
            { value: 'public', label: 'Public' },
            { value: 'private', label: 'Private' }
          ]}
          helperText="Public forums are visible to everyone, private forums are only visible to members."
        />
        <ActionButtons
          primaryText="Register Ubuntu Forum"
          secondaryText="Save Draft"
          onSecondaryClick={handleSaveDraft}
        />
      </div>
    </form>
  );
};

export default UbuntuRegistrationForm;

