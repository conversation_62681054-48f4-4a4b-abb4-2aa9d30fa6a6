import { useState, useEffect } from 'react';
import ImageUpload from './ImageUpload';
import <PERSON><PERSON>ield from './FormField';
import SelectionGrid from './SelectionGrid';
import EditableFormField from './EditableFormField';
import ToggleSwitch from './ToggleSwitch'
import PortfolioSection from './PortfolioSection';

const categoryOptions = [
  "activism", "animation", "architecture", "art", "crafts",
  "design", "edutainment", "fashion", "gaming", "graphic",
  "music", "performance", "photography", "R&D", "urban culture",
  "UX design", "video / film"
];

const SkillsCategory = [
  "3D-animation", "actor/actress", "architecture", "climate", "dancer",
  "education", "edutainment", "environment", "facilitator", "girls & women",
  "graphic recording", "guitar", "percussion", "interior design", "moderator",
  "photographer", "recycling", "sculpturing", "singer", "social entrepreneur",
  "social justice", "sound technician", "sports", "training", "upcycling",
  "urban planning"
];

const MAX_CATEGORIES = 5;
const MAX_SKILLS = 5;

const ProfileUpdate = ({
  user, bio, location, avatar, previewUrl, phone, displayName, portfolio,
  handleFileChange, handleRemoveImage, handleSubmit, setBio, setLocation,
  domain, setDomain, setPhone, setDisplayName, setEmail, setPortfolio, skills, setSkills,
  profession, setProfession, publicStatus, setPublicStatus, galleryImages, setGalleryImages 
}) => {
  const [selected, setSelected] = useState(user.categories);
  const [error, setError] = useState("");
  const [skillsError, setSkillsError] = useState("");
  const [isPortfolioPublic, setIsPortfolioPublic] = useState(publicStatus);

  const handleSelect = (item) => {
    if (selected.length === MAX_CATEGORIES && !selected.includes(item)) {
      setError("Sorry, maximum categories reached");
      return;
    }

    setSelected((prev) =>
      prev.includes(item) ? prev.filter((i) => i !== item) : [...prev, item]
    );
  };

  const handleSelectSkill = (item) => {
    if (skills.length === MAX_SKILLS && !skills.includes(item)) {
      setSkillsError("Sorry, maximum skills reached");
      return;
    }

    const updatedSkills = skills.includes(item)
      ? skills.filter((i) => i !== item)
      : [...skills, item];

    setSkills(updatedSkills);
  };

  const handlePortfolioUpload = (files) => {
    setGalleryImages((prev) => [...prev, ...files]);
  };

  useEffect(() => {
    const timers = [];

    if (error) {
      timers.push(setTimeout(() => setError(""), 3000));
    }

    if (skillsError) {
      timers.push(setTimeout(() => setSkillsError(""), 3000));
    }

    return () => timers.forEach(clearTimeout);
  }, [error, skillsError]);


  useEffect(() => {
    setDomain(selected);
  }, [selected]);

  return (
    <form
      onSubmit={handleSubmit}
      encType="multipart/form-data"
      className="w-full p-6"
    >
      <div className="mb-6 flex justify-between items-center">
        <h2 className="text-2xl font-semibold text-gray-800 mb-6">Complete Your Profile</h2>
        <button type="submit" className="bg-primarybrown text-white px-4 py-2 hover:bg-black rounded-full">Save Changes</button>
      </div>

      <div className="flex flex-col justify-center w-full">
        <div className="w-full bg-white rounded-xl space-y-10 p-6 shadow-lg mb-10">
          <div className="mb-6 flex items-center justify-between">
            <h2 className="text-2xl font-semibold text-gray-800">Public Profile Information</h2>
            <ToggleSwitch
              label={isPortfolioPublic ? "Public" : "Private"}
              checked={isPortfolioPublic}
              onChange={() => {
                const newStatus = !isPortfolioPublic;
                setPublicStatus(newStatus);
                setIsPortfolioPublic(newStatus);
              }}

              disabled={false}
              color="yellow"
            />
          </div>

          {/* Avatar */}
          <ImageUpload
            label="Change Your Profile Avatar"
            image={avatar}
            onImageChange={handleFileChange}
            onImageRemove={handleRemoveImage}
            imagePreview={previewUrl ? previewUrl : user.avatar_url}
            shape="rounded"
          />

          {/* Categories */}
          <SelectionGrid
            title="Professional Categories"
            options={categoryOptions}
            selected={selected}
            maxItems={MAX_CATEGORIES}
            errorMessage={error}
            onSelect={handleSelect}
          />

          {/* Skills */}
          <SelectionGrid
            title="Skills"
            options={SkillsCategory}
            selected={skills}
            maxItems={MAX_SKILLS}
            errorMessage={skillsError}
            onSelect={handleSelectSkill}
          />

          {/* Bio */}
          <FormField
            label="Bio (Maximum 1000 characters)"
            name="bio"
            type="textarea"
            placeholder="Zeus is a highly dedicated and detail-oriented videographer with a strong background in visual storytelling and cinematic production. Specializing in corporate, commercial, and creative video content, Zeus consistently delivers high-quality visuals that align with each client's vision and objectives."
            value={bio}
            onChange={(e) => setBio(e.target.value)}
            rows={4}
          />
        </div>
        <div className="w-full bg-white rounded-xl p-6 shadow-lg mb-10">
          <h2 className="text-2xl font-semibold text-gray-800 mb-6">Basic Information</h2>
          <EditableFormField
            label="Display Name"
            name="displayName"
            type="text"
            placeholder="Display Name"
            value={displayName}
            onChange={(e) => setDisplayName(e.target.value)}
          />
          <EditableFormField
            label="Profession"
            name="displayName"
            placeholder="Profession"
            value={profession}
            onChange={(e) => setProfession(e.target.value)}
          />
          <EditableFormField
            label="Location"
            name="location"
            placeholder="City, Country"
            value={location.address}
            onChange={(e) => setLocation(e.target.value)}
          />
        </div>
        <div className="w-full bg-white rounded-xl p-6 shadow-lg mb-10">
          <h2 className="text-2xl font-semibold text-gray-800 mb-6">Contact Details</h2>
          <EditableFormField
            label="Email"
            name="email"
            type="email"
            placeholder="Email"
            value={user.email}
            onChange={(e) => setEmail(e.target.value)}
            readOnly={true}
          />
          <EditableFormField
            label="Phone"
            name="phone"
            type="tel"
            placeholder="Phone"
            value={phone}
            onChange={(e) => setPhone(e.target.value)}
          />
          <EditableFormField
            label="Portfolio"
            name="portfolio"
            placeholder="Portfolio URL (https://yourportfolio.com)"
            value={portfolio}
            onChange={(e) => setPortfolio(e.target.value)}
            type="url"
          />
        </div>
        <div className="w-full bg-white rounded-xl p-6 shadow-lg">
          <div className="mb-6 flex items-center justify-between">
            <h2 className="text-2xl font-semibold text-gray-800">Gallery</h2>
            <ToggleSwitch
              label={isPortfolioPublic ? "Public" : "Private"}
              checked={isPortfolioPublic}
              onChange={() => setIsPortfolioPublic(!isPortfolioPublic)}
              disabled={false}
              color="yellow"
            />
          </div>
          <PortfolioSection
            projects={galleryImages}
            isPublic={isPortfolioPublic}
            onTogglePublic={(e) => setIsPortfolioPublic(e.target.checked)}
            onUpload={handlePortfolioUpload}
          />
        </div>
      </div>
    </form>
  );
};

export default ProfileUpdate;
