// app/frontend/components/pageSpecific/dashboard/DashUbuntuCard.jsx
import { MapPin, Bookmark } from "lucide-react";

const DashUbuntuCard = ({ ubuntu, isBookmarked=false, showJoin=false, handleJoin }) => {
    const renderStars = (rating) => {
        const stars = [];
        const fullStars = Math.round(rating);
        const hasHalfStar = Math.round(rating * 2) / 2 > fullStars;

        for (let i = 0; i < 5; i++) {
            if (i < fullStars) {
                stars.push(
                    <span key={i} className="text-yellow-400">★</span>
                );
            } else if (i === fullStars && hasHalfStar) {
                stars.push(
                    <span key={i} className="text-yellow-400">★</span>
                );
            } else {
                stars.push(
                    <span key={i} className="text-gray-300">★</span>
                );
            }
        }
        return stars;
    };
    const collaborators = ubuntu.forum_members || [];
    const remainingCollaborators = collaborators.slice(3);
    
    return (
        <div className="flex items-center p-4 bg-red-50 border-2 border-yellow-500 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
            {/* ubuntu Image */}
            <div className="flex-shrink-0 mr-4">
                <img
                    src={(ubuntu.avatar_url) || "https://via.placeholder.com/150"}
                    alt={ubuntu.title}
                    className="w-24 h-24 rounded-full object-cover border-2 border-yellow-500"
                />
            </div>

            {/* ubuntu Details */}
            <div className="flex-grow min-w-0">
                <div className="flex items-center justify-between w-fit gap-4">
                    <div className="flex items-center">
                        <h3 className="text-base md:text-lg font-semibold">{ubuntu.title}</h3>
                    </div>
                    {isBookmarked && (
                        <Bookmark size={24} className="text-primarymaroon fill-primarymaroon" />
                    )}
                </div>
                <p className="text-sm text-gray-600 truncate">
                    {ubuntu.description}
                </p>
                <div className="flex items-center mt-1 sm:mt-2">
                    <div className="flex">
                        {renderStars()}
                    </div>
                    <span className="ml-1 text-xs sm:text-sm text-gray-600">({ })</span>
                </div>
                {/* Collaborators Section */}
                <div className="flex items-center justify-between mb-2 sm:mb-4 w-fit gap-4">
                    <span className="text-sm text-gray-700 font-medium">Members:</span>
                    <div className="flex items-center -space-x-2 overflow-hidden">
                        {collaborators.slice(0, 3).map((collaborator) => (
                            <img
                                key={collaborator.id}
                                className="inline-block h-8 w-8 rounded-full ring-2 ring-white"
                                src={collaborator.avatar}
                                alt={collaborator.name}
                            />
                        ))}
                        {remainingCollaborators > 0 && (
                            <span className="flex items-center justify-center h-8 w-8 rounded-full bg-gray-200 text-gray-600 text-xs ring-2 ring-white">
                                +{remainingCollaborators}
                            </span>
                        )}
                    </div>
                </div>
            </div>
            {showJoin && (
                <div className="flex-shrink-0 ml-4">
                    <button onClick={()=>{handleJoin(ubuntu.id)}} className="bg-primarybrown hover:bg-black text-white px-6 py-2 rounded-full transition-colors duration-200">
                        Join Now
                    </button>
                </div>
            )}
        </div>
    );
};

export default DashUbuntuCard;
