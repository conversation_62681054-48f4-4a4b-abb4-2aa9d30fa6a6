import { useState } from 'react';
import DropdownSelection from './DropdownSelection';
import ImageUpload from './ImageUpload';
import FormField from './FormField';
import ActionButtons from './ActionButtons';
import FormHeader from './FormHeader';
import StatusField from './StatusField';
import GalleryUpload from './GalleryUpload';
import { router } from '@inertiajs/react';


const tagsOptions = [
  'accelerator', 'co-creators', 'crafts workshop', 'event space', 'gallery',
  'incubator', 'innovation hub', 'maker space', 'co-working space', 'music studio',
  'training facility'
]

const whatWeDoOptions = [
  'Show-Stage', 'Workshop', 'Residency', 'Training Facilities', 'Marketplace', 'Exhibitions', 'Incubator', 'Pitching', 'Tools'
]

/**
 * PlaceRegistrationForm Component
 * Handles the registration of new places with image upload capabilities
 * @param {Function} props.onBack - Callback function for handling back navigation
 */
const PlaceRegistrationForm = ({ onBack }) => {
  // State management for form data and image handling
  const [formData, setFormData] = useState({
    name: '',
    location: '',
    description: '',
    what_we_do: '',
    tags: '',
    seeking: '',
  });

  const [avatar, setAvatar] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);
  const [galleryImages, setGalleryImages] = useState([]);
  const [galleryPreviews, setGalleryPreviews] = useState([]);

  // Event handlers for form inputs
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Image handling functions
  const handleImageChange = (e) => {
    const file = e.target.files[0];
    setAvatar(file);
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => setImagePreview(reader.result);
      reader.readAsDataURL(file);
    }
  };

  const handleRemoveImage = () => {
    setImagePreview(null);
    setAvatar(null);
  };

  const handleGalleryChange = (e) => {
    const files = Array.from(e.target.files);
    setGalleryImages(files);

    const previews = files.map(file => {
      return new Promise((resolve) => {
        const reader = new FileReader();
        reader.onloadend = () => resolve(reader.result);
        reader.readAsDataURL(file);
      });
    });

    Promise.all(previews).then(setGalleryPreviews);
  };

  // Form submission handler
  const handleSubmit = (e) => {
    e.preventDefault();
  
    const formPayload = new FormData();
    formPayload.append('place[name]', formData.name);
    formPayload.append('place[location]', formData.location);
    formPayload.append('place[description]', formData.description);
    formPayload.append('place[seeking]', formData.seeking);
  
    // Handle tags as an array
    formData.tags.split(',').forEach((tag) => {
      formPayload.append('place[tags][]', tag.trim());
    });

    // Handle what_we_do as an array (if it's a string, split; if array, use directly)
  if (Array.isArray(formData.what_we_do)) {
    formData.what_we_do.forEach((item) => {
      formPayload.append('place[what_we_do][]', item);
    });
  } else if (typeof formData.what_we_do === 'string') {
    formData.what_we_do.split(',').forEach((item) => {
      formPayload.append('place[what_we_do][]', item.trim());
    });
  }
  
    // Add avatar
    if (avatar) {
      formPayload.append('place[avatar]', avatar);
    }
  
    // Add gallery images
    galleryImages.forEach((img) => {
      formPayload.append('place[gallery_images][]', img);
    });
  
    // Submit the form
    router.post("/places/new", formPayload, {
      preserveScroll: true,
      onSuccess: () => {
        alert("Saved!")
      },
      onError: (errors) => {
        // Extract and display first error
        const firstError = Object.values(errors)[0];
        setError(firstError || "An error occurred. Please try again.");
      }
    });
  };

  const handleSaveDraft = () => {
    alert("Pending feature!");
  };

  return (
    <form onSubmit={handleSubmit} className="p-6 relative font-inter space-y-6 w-full">
      <FormHeader
        title="Register a Place"
        onClose={onBack}
        borderColor="border-primaryorange"
      />

      <div className="p-6 bg-white space-y-6 rounded-2xl shadow-lg">
        <ImageUpload
          label="Place Image"
          image={avatar}
          onImageChange={handleImageChange}
          onImageRemove={handleRemoveImage}
          imagePreview={imagePreview}
        />

        <FormField
          label="Place Name"
          name="name"
          placeholder="Name of the place..."
          value={formData.name}
          onChange={handleChange}
          required
        />

        <FormField
          label="Location"
          name="location"
          placeholder="Address or coordinates"
          value={formData.location}
          onChange={handleChange}
          required
        />

        <FormField
          label="Description"
          name="description"
          type="textarea"
          placeholder="Describe this place"
          value={formData.description}
          onChange={handleChange}
          rows={4}
          required
        />

        <DropdownSelection
          formData={formData}
          setFormData={setFormData}
          options={whatWeDoOptions}
          label="What we offer (max 5)"
          placeholder="Select options..."
          fieldName="what_we_do"
          singleSelection={false}
          maxSelections={5} 
        />

        <DropdownSelection
          formData={formData}
          setFormData={setFormData}
          options={tagsOptions}
          label="Categories (max 5)"
          placeholder="Select categories..."
          fieldName="tags"
          singleSelection={false}
          maxSelections={5}
        />

        <GalleryUpload
          images={galleryImages}
          imagePreviews={galleryPreviews}
          onImagesChange={handleGalleryChange}
          required
        />

        <StatusField
          value={formData.seeking}
          onChange={handleChange}
        />

        <ActionButtons
          primaryText="Create Place"
          secondaryText="Save as Draft"
          onSecondaryClick={handleSaveDraft}
        />
      </div>
    </form>
  );
};

export default PlaceRegistrationForm;
