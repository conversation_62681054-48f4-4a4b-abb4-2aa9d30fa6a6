import SettingItem from './SettingItem';

/**
 * SettingsSubsection Component
 * Renders a subsection of settings with a title and list of setting items
 * @param {string} props.title - Subsection title
 * @param {string} props.icon - Icon for the subsection (optional)
 * @param {Array} props.settings - Array of setting objects
 * @param {Object} props.values - Current values for all settings
 * @param {Function} props.on<PERSON>hange - Handler for setting changes
 */
const SettingsSubsection = ({
    title,
    icon,
    settings = [],
    values = {},
    onChange
}) => {
    return (
        <div className="mb-8">
            {/* Subsection Header */}
            <div className="flex items-center gap-2 mb-4">
                {icon && (
                    <div className="w-6 text-settingsblue flex items-center justify-center">
                        {icon}
                    </div>
                )}
                <h3 className="text-lg font-semibold text-gray-800">{title}</h3>
            </div>

            {/* Settings Items */}
            <div className="bg-white rounded-xl border border-gray-200 divide-y divide-gray-200">
                {settings.map((setting, index) => (
                    <SettingItem
                        key={setting.key || index}
                        label={setting.label}
                        description={setting.description}
                        checked={values[setting.key] || false}
                        disabled={setting.disabled || false}
                        color={setting.color || 'blue'}
                        onChange={(checked) => onChange(setting.key, checked)}
                    />
                ))}
            </div>
        </div>
    );
};

export default SettingsSubsection;
