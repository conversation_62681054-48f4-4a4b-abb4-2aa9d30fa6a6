import { useState, useRef } from 'react';
import { Link } from '@inertiajs/react';
import { Search } from 'lucide-react';
import noChatsImage from '/assets/no-chats.png';
import { SquarePen } from 'lucide-react';
import ChatItem from './ChatItem';

const MessagesSection = ({ user, onOpenChat }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const searchInputRef = useRef(null);

  const chats = [
    {
      id: 1,
      name: "<PERSON><PERSON><PERSON>",
      avatar: "https://randomuser.me/api/portraits/women/44.jpg",
      lastMessage: "Hey <PERSON>, I came across your portfolio...",
      lastMessageTime: "2024-06-19T14:53:00Z",
      unreadCount: 0,
      isGroup: false,
      isActive: true
    },
    {
      id: 2,
      name: "Technical Writers",
      avatar: "https://randomuser.me/api/portraits/men/32.jpg",
      lastMessage: "We're exploring potential meetup spots...",
      lastMessageTime: "2025-06-19T14:53:00Z",
      unreadCount: 8,
      isGroup: true,
      isActive: false
    },
    {
      id: 3,
      name: "<PERSON><PERSON><PERSON>",
      avatar: "https://randomuser.me/api/portraits/men/15.jpg",
      lastMessage: "Hi Dancers, we're thinking of linking up with other creative groups like Kuona Creatives...",
      lastMessageTime: "2025-06-20T14:53:00Z",
      unreadCount: 0,
      isGroup: false,
      isActive: false
    },
    {
      id: 4,
      name: "Kuona Artist Creatives",
      avatar: "https://randomuser.me/api/portraits/women/20.jpg",
      lastMessage: "Salam Hi everyone! We're mapping out new spaces for creative meetups, showcases, and...",
      lastMessageTime: "2025-06-20T14:53:00Z",
      unreadCount: 0,
      isGroup: true,
      isActive: false
    }
  ];

  // Filter chats based on search query
  const filteredChats = chats.filter(chat =>
    chat.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    chat.lastMessage?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const formatTime = (timestamp) => {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now - date) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      });
    } else {
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric'
      });
    }
  };

  const handleNewChatClick = (e) => {
    e.preventDefault(); // Prevent default link navigation
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  };

  const EmptyState = () => (
    <div className="w-full flex flex-col items-center justify-center py-10 px-4 text-center">
      <div className="p-6 mb-4">
        <img src={noChatsImage} alt="No Chats" className="w-36" />
      </div>
      <h3 className="text-lg font-medium text-gray-900 mb-2">You do not have any messages</h3>
      <p className="text-gray-500 mb-6 max-w-sm">
        Start a conversation by creating a new chat or joining an existing group.
      </p>
    </div>
  );

  return (
    <div className="p-4 w-full mx-auto rounded-xl bg-white">
      <div className="flex items-center justify-between p-6">
        <h1 className="text-2xl font-bold text-gray-900">Messages</h1>
        <Link href="/chats/new"
        className="bg-primarybrown hover:bg-primarybrown text-white px-4 py-2 rounded-full font-medium transition-colors flex items-center gap-2"
        onClick={handleNewChatClick}>
          <SquarePen /> New Chat
        </Link>
      </div>
      <div className="rounded-full border border-gray-300 rounded-full">
        <div className="relative">
          <div className="absolute bg-primarybrown rounded-full w-16 inset-y-0 left-0 pl-5 flex items-center pointer-events-none">
            <Search className="h-5 w-5 text-white" />
          </div>
          <input
            type="text"
            placeholder="Search..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            ref={searchInputRef}
            className="w-full pl-18 pr-4 py-3 border border-gray-300 rounded-full text-gray-500 placeholder-gray-400 placeholder-opacity-80 focus:ring-2 focus:ring-primarybrown focus:outline-none"
          />
        </div>
      </div>

      {/* Chat List */}
      <div className="divide-y divide-gray-200">
        {chats.length === 0 ? (
          <EmptyState />
        ) : filteredChats.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-12 px-6 text-center">
            <Search className="w-8 h-8 text-gray-400 mb-3" />
            <p className="text-gray-500">No chats found matching "{searchQuery}"</p>
          </div>
        ) : (
          filteredChats.map((chat) => (
            <div key={chat.id}>
              <ChatItem
                chat={chat}
                onOpenChat={onOpenChat}
              />
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default MessagesSection;