// app/frontend/components/pageSpecific/dashboard/DashPeopleCard.jsx
import { BadgeCheck, MapPin, Eye, Bookmark } from "lucide-react";



const DashPeopleCard = ({ person, isBookmarked }) => {
    const renderStars = (rating) => {
        const stars = [];
        const fullStars = Math.round(rating);
        const hasHalfStar = Math.round(rating * 2) / 2 > fullStars;

        for (let i = 0; i < 5; i++) {
            if (i < fullStars) {
                stars.push(
                    <span key={i} className="text-yellow-400">★</span>
                );
            } else if (i === fullStars && hasHalfStar) {
                stars.push(
                    <span key={i} className="text-yellow-400">★</span>
                );
            } else {
                stars.push(
                    <span key={i} className="text-gray-300">★</span>
                );
            }
        }
        return stars;
    };
    return (
        <div className="flex items-center p-4 bg-yellow-50 border-2 border-yellow-400 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
            {/* Profile Image */}
            <div className="flex-shrink-0 mr-4">
                <img
                    src={person.avatar_url}
                    alt={person.name}
                    className="w-24 h-24 rounded-full object-cover border-2 border-yellow-300"
                />
            </div>

            {/* Profile Details */}
            <div className="flex-grow min-w-0">
                <div className="flex items-center justify-between w-fit gap-4">
                    <div className="flex items-center">
                        <h3 className="text-base md:text-lg font-semibold">{person.name}</h3>
                        {person.verified && (
                            <BadgeCheck size={16} className="ml-1 text-green-500" />
                        )}
                    </div>
                    {isBookmarked && (
                        <Bookmark size={24} className="text-yellow-500 fill-yellow-500" />
                    )}
                </div>
                <p className="text-sm text-gray-600 truncate">
                    {person.profession}
                </p>
                <div className="flex items-center mt-1 sm:mt-2 text-gray-600">
                    <MapPin size={16} className="mr-1" />
                    <span className="text-xs sm:text-sm">{person.address}</span>
                </div>
                <div className="flex items-center mt-1 sm:mt-2">
                    <div className="flex">
                        {renderStars()}
                    </div>
                    <span className="ml-1 text-xs sm:text-sm text-gray-600">({ })</span>
                </div>
                { /* width just to accomodate items */}
                <div className="flex items-center bg-primaryyellow text-black-600 px-1 sm:px-2 py-1 rounded-full w-fit">
                    <Eye size={16} className="mr-1" />
                    <span className="text-xs sm:text-sm">{person.views}k</span>
                </div>
            </div>

            {/* Match Button */}
            {(!isBookmarked || isBookmarked === undefined) && (
                <div className="flex-shrink-0 ml-4">
                    <button className="bg-primarybrown hover:bg-black text-white px-6 py-2 rounded-full transition-colors duration-200">
                        Match
                    </button>
                </div>
            )}
        </div>
    );
};

export default DashPeopleCard;
