import { Pin } from 'lucide-react';

const SelectionGrid = ({ 
  title, 
  options, 
  selected, 
  maxItems, 
  errorMessage, 
  onSelect 
}) => {
  return (
    <div className="mb-6">
      <label className="block font-semibold text-md text-gray-700">
        {title} <span className="text-red-500">*</span>
      </label>
      <p className="text-sm text-gray-600">({selected.length} / {maxItems}) <i>max {maxItems}</i></p>

      {errorMessage && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {errorMessage}
        </div>
      )}

      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-4 mt-4">
        {options.map((item) => (
          <button
            type="button"
            key={item}
            className={`${selected.includes(item)
              ? "bg-primaryyellow text-gray-800 hover:bg-borderyellow"
              : "bg-gray-100 text-gray-800 hover:bg-primaryyellow"
              } px-4 py-2 rounded-lg shadow text-sm font-medium cursor-pointer`}
            onClick={(e) => {
              e.preventDefault();
              onSelect(item);
            }}
          >
            <div className="flex items-center justify-center gap-2">
              <Pin size={16} />
              <span>{item}</span>
            </div>
          </button>
        ))}
      </div>
    </div>
  );
};

export default SelectionGrid;
