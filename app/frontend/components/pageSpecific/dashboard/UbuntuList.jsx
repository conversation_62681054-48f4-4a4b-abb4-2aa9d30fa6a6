// app/frontend/components/pageSpecific/dashboard/UbuntuList.jsx
import DashUbuntuCard from './DashUbuntuCard';
import EmptyState from './EmptyState';

const UbuntuList = ({ title, forums, emptyStateProps, renderCardProps }) => {
  if (forums.length === 0) {
    return <EmptyState {...emptyStateProps} />;
  }

  return (
    <div className="w-full bg-white rounded-xl shadow-lg">
      <div className="p-4 flex justify-between items-center mb-4">
        <h2 className="text-2xl font-semibold text-black mb-2">{title}</h2>
      </div>
      <div className="space-y-3 p-4 overflow-y-auto max-h-144">
        {forums.map((ubuntu) => (
          <div key={ubuntu.id}>
            <DashUbuntuCard ubuntu={ubuntu} {...renderCardProps(ubuntu)} />
          </div>
        ))}
      </div>
    </div>
  );
};

export default UbuntuList;
