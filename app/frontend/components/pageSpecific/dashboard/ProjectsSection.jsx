// app/frontend/components/pageSpecific/dashboard/ProjectsSection.jsx
import { useState, useEffect } from 'react';
import ProjectRegistrationForm from './ProjectRegistrationForm';
import ProjectsCard from '../projects/ProjectsCard';
import { CirclePlus } from 'lucide-react';
import EmptyState from './EmptyState';
import ProjectsList from './ProjectsList';

// Fetch recommendations
const getProjectsRecomendations = async () => {
  const res = await fetch("/projects/recomendations?verified=true")
  const resJson = await res.json();
  return resJson.projects;
};

const getUserProjects = async () => {
  const res = await fetch("/projects/userprojects")
  const resJson = await res.json();
  return resJson.projects;
}

const getBookmarkedProjects = async () => {
  const res = await fetch("/projects/bookmarked")
  const resJson = await res.json();
  return resJson.projects;
};

const getAssociatedProjects = async () => {
  const res = await fetch("/projects/associated")
  const resJson = await res.json();
  return resJson.projects;
};

const handleJoin = () => {
  alert("Join project functionality should be implemented here.");
}

// Projects Section Component
const ProjectsSection = ({ user }) => {
  const [recommendedProjects, setRecommendedProjects] = useState([]);
  const [associatedProjects, setAssociatedProjects] = useState([]);
  const [bookmarkedProjects, setBookmarkedProjects] = useState([]);
  const [showRegistrationForm, setShowRegistrationForm] = useState(false);
  const [userProjects, setUserProjects] = useState([])

  // Check URL parameters for sub-section navigation
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const view = urlParams.get('view');
    
    // If view=create-project, show the registration form
    if (view === 'create-project') {
      setShowRegistrationForm(true);
    }
  }, []);

  useEffect(() => {
      getUserProjects().then(setUserProjects)
      getProjectsRecomendations().then(setRecommendedProjects);
      getBookmarkedProjects().then(setBookmarkedProjects);
      getAssociatedProjects().then(setAssociatedProjects);
  }, []);

  const handleGoToProjects = () => {
    // Assuming Inertia.visit is defined elsewhere for navigation
    if (typeof Inertia !== 'undefined' && Inertia.visit) {
        Inertia.visit('/projects');
    } else {
        console.warn("Inertia.visit not found. Navigation might not work as expected.");
        // Fallback for demonstration if Inertia is not available
        window.location.href = '/projects';
    }
  }

  const handleShowForm = () => {
    setShowRegistrationForm(true);
    const params = new URLSearchParams(window.location.search);
    params.set('view', 'create-project');
    const newUrl = `${window.location.pathname}?${params.toString()}`;
    window.history.replaceState({}, '', newUrl);
  };

  const handleBackFromForm = () => {
    setShowRegistrationForm(false);
    const params = new URLSearchParams(window.location.search);
    params.delete('view');
    const newUrl = `${window.location.pathname}?${params.toString()}`;
    window.history.replaceState({}, '', newUrl);
  };

  return (
    <div className="flex flex-col space-y-4 items-center w-full">
      {showRegistrationForm ? (
        <div className="p-6 rounded-xl w-full flex justify-center items-center">
          <ProjectRegistrationForm onBack={handleBackFromForm} />
        </div>
      ) : (
        <> {/* Use a React Fragment to group multiple elements */}
          {user && userProjects && userProjects.length === 0 ? (
            <EmptyState
              title="My Projects"
              description="Looks like you haven't created any projects yet. Start by creating a new project."
              actionText="Create New Project"
              action={handleShowForm}
              section={"projects"}
            />
          ) : (
            <div className="w-full">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-2xl border-b-4 border-primarygreen font-semibold text-black mb-4">My Projects</h2>
                <button onClick={handleShowForm} className="bg-primarybrown text-white px-4 py-2 rounded-full inline-flex items-center gap-2 hover:bg-black"><CirclePlus />Add New Project</button>
              </div>
              <div className="bg-gray-50 p-4 rounded-xl shadow-lg border-2 border-gray-200 overflow-x-auto whitespace-nowrap hide-scrollbar">
                <div className="inline-flex space-x-4 w-full">
                  {userProjects.map((project) => (
                    <div key={project.id} className="flex-none min-w-80 max-w-96">
                      <ProjectsCard project={project} />
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          <ProjectsList
            title="Associated Projects"
            projects={associatedProjects}
            emptyStateProps={{
              title: "No Associated Projects",
              description: "You will see projects you interact with here. Join or create a project to get started!",
              actionText: "Go to Projects",
              action: handleGoToProjects,
              section: "projects",
            }}
            renderCardProps={() => ({})}
          />

          <ProjectsList
            title="Bookmarked Projects"
            projects={bookmarkedProjects}
            emptyStateProps={{
              title: "No Bookmarked Projects",
              description: "You will see projects you bookmark here. Bookmark a project to get started!",
              actionText: "Go to Projects",
              action: handleGoToProjects,
              section: "projects",
            }}
            renderCardProps={() => ({ isBookmarked: true })}
          />

          <ProjectsList
            title="Suggested for You"
            projects={recommendedProjects}
            emptyStateProps={{
              title: "No Suggested Projects",
              description: "Explore and join new projects!",
              actionText: "Go to Projects",
              action: handleGoToProjects,
              section: "projects",
            }}
            renderCardProps={() => ({ showJoin: true, handleJoin: handleJoin })}
          />
        </>
      )}
    </div>
  );
};

export default ProjectsSection;