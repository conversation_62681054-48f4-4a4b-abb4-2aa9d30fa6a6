import { Upload } from 'lucide-react';

/**
 * GalleryUpload Component
 * Reusable gallery upload component with multiple image preview
 * @param {string} props.label - Label text for the upload section
 * @param {Array} props.images - Array of selected image files
 * @param {Array} props.imagePreviews - Array of preview URLs
 * @param {Function} props.onImagesChange - Callback when images are selected
 * @param {boolean} props.required - Whether the field is required
 * @param {string} props.helpText - Help text displayed in the upload area
 */
const GalleryUpload = ({
  label = "Images (you can upload several)",
  images = [],
  imagePreviews = [],
  onImagesChange,
  required = false,
  helpText = "Drag and drop your images here or click to select.\nSupports JPG, PNG, webP, and GIF. Max 10MB each.",
}) => {
  return (
    <div>
      <label className="block font-medium text-gray-700 mb-1">
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      <div className="relative border-2 border-dashed border-primarybrown rounded-2xl p-6 h-32 flex flex-col items-center justify-center">
        <Upload className="h-8 w-8 text-primarybrown mb-2" />
        <p className="text-sm text-gray-500 text-center whitespace-pre-line">
          {helpText}
        </p>
        <input
          type="file"
          accept="image/*"
          multiple
          onChange={onImagesChange}
          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
          required={required}
        />
      </div>
      {imagePreviews.length > 0 && (
        <div className={`mt-2 grid grid-cols-8 gap-3`}>
          {imagePreviews.map((src, index) => (
            <img
              key={index}
              src={src}
              alt={`Gallery Preview ${index + 1}`}
              className="h-24 w-24 object-cover rounded-2xl border-1 border-gray-300"
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default GalleryUpload;
