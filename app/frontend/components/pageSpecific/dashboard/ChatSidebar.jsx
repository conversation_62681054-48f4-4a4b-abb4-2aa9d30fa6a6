import { useState } from 'react';
import { ArrowLeft, Search, Users } from 'lucide-react';

const ChatSidebar = ({ user, onOpenChat, currentChatId, onCloseChat }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeFilter, setActiveFilter] = useState('All'); // New state for active filter

  // Dummy chat data - replace with real data later
  const chats = [
    {
      id: 1,
      name: "<PERSON><PERSON><PERSON>",
      avatar: "https://randomuser.me/api/portraits/women/44.jpg",
      lastMessage: "Hey <PERSON>, I came across your portfolio...",
      lastMessageTime: "2024-06-19T14:53:00Z",
      unreadCount: 0,
      isGroup: false,
      isActive: true
    },
    {
      id: 2,
      name: "Technical Writers",
      avatar: "https://randomuser.me/api/portraits/men/32.jpg",
      lastMessage: "We're exploring potential meetup spots...",
      lastMessageTime: "2025-06-19T14:53:00Z",
      unreadCount: 8,
      isGroup: true,
      isActive: false
    },
    {
      id: 3,
      name: "<PERSON><PERSON><PERSON>",
      avatar: "https://randomuser.me/api/portraits/men/15.jpg",
      lastMessage: "Hi Dancers, we're thinking of linking up with other creative groups like Kuona Creatives...",
      lastMessageTime: "2025-06-20T14:53:00Z",
      unreadCount: 0,
      isGroup: false,
      isActive: false
    },
    {
      id: 4,
      name: "Kuona Artist Creatives",
      avatar: "https://randomuser.me/api/portraits/women/20.jpg",
      lastMessage: "Salam Hi everyone! We're mapping out new spaces for creative meetups, showcases, and...",
      lastMessageTime: "2025-06-20T14:53:00Z",
      unreadCount: 0,
      isGroup: true,
      isActive: false
    }
  ];

  const filteredChats = chats.filter(chat => {
    const matchesSearch = chat.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          chat.lastMessage?.toLowerCase().includes(searchQuery.toLowerCase());

    if (activeFilter === 'Read') {
      return matchesSearch && chat.unreadCount === 0;
    } else if (activeFilter === 'Unread') {
      return matchesSearch && chat.unreadCount > 0;
    }
    return matchesSearch; // 'All' filter
  });

  const formatTime = (timestamp) => {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now - date) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      });
    } else {
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric'
      });
    }
  };

  const truncateMessage = (message, maxLength = 60) => {
    if (!message) return '';
    return message.length > maxLength
      ? message.substring(0, maxLength) + '...'
      : message;
  };

  return (
    <div className="flex-none sticky top-28 w-80 h-screen mt-4 rounded-xl border-gray-200 bg-gray-100 shadow-lg border-1 border-grey-200">
      {/* Header */}
      <div className="p-4">
        <div className="flex items-center justify-between mb-4">
          <button
            onClick={onCloseChat}
            className="flex items-center p-2 rounded-full text-gray-600 hover:bg-gray-200"
          >
            <div className="flex items-center mr-2 justify-center w-8 h-8 bg-primarybrown rounded-full">
              <ArrowLeft className="w-5 h-5 text-white" />
            </div>
            <span className="font-medium">Go Back to Chats</span>
          </button>
        </div>

        {/* Search */}
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-4 w-4 text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-full shadow-sm text-sm focus:ring-2 focus:ring-primarybrown focus:border-primarybrown"
          />
        </div>
      </div>

      {/* Filter Tabs */}
      <div className="p-4 border-gray-200">
        <div className="flex space-x-1">
          <button
            onClick={() => setActiveFilter('All')}
            className={`px-4 py-2 rounded-full text-sm font-semibold ${
              activeFilter === 'All'
                ? 'bg-primarybrown text-white'
                : 'text-gray-600 hover:bg-gray-200'
            }`}
          >
            All
          </button>
          <button
            onClick={() => setActiveFilter('Read')}
            className={`px-4 py-2 rounded-full text-sm font-semibold ${
              activeFilter === 'Read'
                ? 'bg-primarybrown text-white'
                : 'text-gray-600 hover:bg-gray-200'
            }`}
          >
            Read
          </button>
          <button
            onClick={() => setActiveFilter('Unread')}
            className={`px-4 py-2 rounded-full text-sm font-semibold ${
              activeFilter === 'Unread'
                ? 'bg-primarybrown text-white'
                : 'text-gray-600 hover:bg-gray-200'
            }`}
          >
            Unread
          </button>
        </div>
      </div>

      {/* Chat List */}
      <div className="h-[60vh] overflow-y-auto p-2 space-y-2">
        {filteredChats.map((chat) => (
          <div
            key={chat.id}
            onClick={() => onOpenChat(chat.id)}
            className={`p-4 rounded-xl border-1 border-gray-200 cursor-pointer transition-colors ${
              chat.id === currentChatId
                ? 'bg-stone-200'
                : 'bg-white hover:bg-gray-200'
            }`}
          >
            <div className="flex items-start space-x-3">
              {/* Avatar */}
              <div className="flex-shrink-0">
                {chat.avatar ? (
                  <img
                    src={chat.avatar}
                    alt={chat.name}
                    className="w-10 h-10 rounded-full object-cover"
                  />
                ) : (
                  <div className="w-10 h-10 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center">
                    <span className="text-white font-semibold text-sm">
                      {chat.name?.charAt(0)?.toUpperCase() || '?'}
                    </span>
                  </div>
                )}
              </div>

              {/* Chat Info */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-1">
                  <div className="flex items-center">
                    <h3 className="text-sm font-semibold text-gray-900 truncate">
                      {chat.name}
                    </h3>
                    {chat.isGroup && (
                      <Users className="w-3 h-3 text-gray-400 ml-1 flex-shrink-0" />
                    )}
                  </div>
                  <span className="text-xs text-gray-500">
                    {formatTime(chat.lastMessageTime)}
                  </span>
                </div>
                <p className="text-gray-600 text-xs leading-relaxed mb-1">
                  {truncateMessage(chat.lastMessage)}
                </p>
                {chat.unreadCount > 0 && (
                  <div className="flex justify-end">
                    <div className="bg-primarybrown text-white text-xs font-bold rounded-full min-w-[20px] h-5 flex items-center justify-center px-1.5">
                      {chat.unreadCount > 99 ? '99+' : chat.unreadCount}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ChatSidebar;
