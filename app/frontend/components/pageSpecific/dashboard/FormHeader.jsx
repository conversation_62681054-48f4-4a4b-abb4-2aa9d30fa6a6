import { X } from 'lucide-react';

/**
 * FormHeader Component
 * Reusable form header with title and close button
 * @param {string} props.title - Title text for the form
 * @param {Function} props.onClose - Close button click handler
 * @param {string} props.borderColor - Border color class (default: 'border-primaryorange')
 */
const FormHeader = ({ 
  title, 
  onClose, 
  borderColor = 'border-primaryorange' 
}) => {
  return (
    <>
      <button
        type="button"
        onClick={onClose}
        className="absolute text-2xl top-8 right-6 text-primarybrown hover:text-gray-800 cursor-pointer"
      >
        <X size={32} />
      </button>

      <h2 className={`text-2xl border-b-4 ${borderColor} pb-2 font-semibold text-black`}>
        {title}
      </h2>
    </>
  );
};

export default FormHeader;
