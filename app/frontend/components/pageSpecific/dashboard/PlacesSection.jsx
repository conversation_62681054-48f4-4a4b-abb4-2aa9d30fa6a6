// app/frontend/components/pageSpecific/dashboard/PlacesSection.jsx
import { useState, useEffect } from 'react';
import PlaceRegistrationForm from './PlaceRegistrationForm';
import PlacesCard from '../places/PlacesCard';
import { CirclePlus } from 'lucide-react';
import EmptyState from './EmptyState';
import PlacesList from './PlacesList';

// Places Section Component
const PlacesSection = ({ user }) => {
    const [recommendedPlaces, setRecommendedPlaces] = useState([]);
    const [associatedPlaces, setAssociatedPlaces] = useState([]);
    const [bookmarkedPlaces, setBookmarkedPlaces] = useState([]);
    const [showRegistrationForm, setShowRegistrationForm] = useState(false);
    const [userPlaces, setUserPlaces] = useState([]);
    const [error, setError] = useState(null);

    // Check URL parameters for sub-section navigation
    useEffect(() => {
        const urlParams = new URLSearchParams(window.location.search);
        const view = urlParams.get('view');

        // If view=create-place, show the registration form
        if (view === 'create-place') {
            setShowRegistrationForm(true);
        }
    }, []);

    // Fetch recommendations
    const getRecommendedPlaces = async () => {
        try {
            const res = await fetch("/locations_recomentation");
            const resJson = await res.json();
            setRecommendedPlaces(resJson.places || []);
        } catch (err) {
            setError("Sorry, we couldn't load places at the moment.");
        }
    };

    const getUserPlaces = async () => {
        try {
            const res = await fetch("/user_places");
            const resJson = await res.json();
            setUserPlaces(resJson.places || []);
        } catch (err) {
            setError(`Error fetching user places: ${err.message}`);
        }
    }

    const getBookmarkedPlaces = async () => {
        try {
            const res = await fetch("/places/bookmarked");
            const resJson = await res.json();
            setBookmarkedPlaces(resJson.places || []);
        } catch (err) {
            setError("Error fetching bookmarked places:", err);
        }
    };

    const getAssociatedPlaces = async () => {
        try {
            const res = await fetch("/associated_places");
            const resJson = await res.json();
            setAssociatedPlaces(resJson.places || []);
        } catch (err) {
            setError("Error fetching associated places:", err);
        }
    };

    const handleJoin = () => {
        alert("Join place functionality should be implemented here.");
    }

    useEffect(() => {
        getRecommendedPlaces();
        getUserPlaces();
        getBookmarkedPlaces();
        getAssociatedPlaces();
    }, []);

    const handleGoToPlaces = () => {
        window.location.href = '/places';
    }

    const handleShowForm = (type) => {
        setShowRegistrationForm(true);
        const params = new URLSearchParams(window.location.search);
        params.set('view', 'create-place');
        const newUrl = `${window.location.pathname}?${params.toString()}`;
        window.history.replaceState({}, '', newUrl);
    };

    const handleBackFromForm = () => {
        setShowRegistrationForm(false);
        const params = new URLSearchParams(window.location.search);
        params.delete('view');
        const newUrl = `${window.location.pathname}?${params.toString()}`;
        window.history.replaceState({}, '', newUrl);
    };

    return (
        <div className="flex flex-col space-y-4 items-center w-full">
            {showRegistrationForm ? (
                <div className="p-6 rounded-xl w-full flex justify-center items-center">
                    <PlaceRegistrationForm onBack={handleBackFromForm} />
                </div>
            ) : (
                <>
                    {userPlaces.length === 0 ? (
                        <EmptyState
                            title="My Places"
                            description="Looks like you haven't created any places yet. Start by creating a new place."
                            actionText="Create New Place"
                            action={handleShowForm}
                            section={"places"}
                        />
                    ) : (
                        <div className="w-full">
                            <div className="flex justify-between items-center mb-4">
                                <h2 className="text-2xl border-b-4 border-primaryorange font-semibold text-black mb-4">My Places</h2>
                                <button onClick={handleShowForm} className="bg-primarybrown text-white px-4 py-2 rounded-full inline-flex items-center gap-2 hover:bg-black"><CirclePlus />Add New Place</button>
                            </div>
                            <div className="bg-gray-50 p-4 rounded-xl shadow-lg border-2 border-gray-200 overflow-x-auto whitespace-nowrap hide-scrollbar">
                                <div className="inline-flex space-x-4 w-full">
                                    {userPlaces.map((place) => (
                                        <div key={place.id} className="flex-none max-w-80">
                                            <PlacesCard place={place} />
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                    )}

                    <PlacesList
                        title="Associated Places"
                        places={associatedPlaces}
                        emptyStateProps={{
                            title: "No Associated Places",
                            description: "You will see places you interact with here. Join or create a place to get started!",
                            actionText: "Go to Places",
                            action: handleGoToPlaces,
                            section: "places",
                        }}
                        renderCardProps={() => ({})}
                    />

                    <PlacesList
                        title="Bookmarked Places"
                        places={bookmarkedPlaces}
                        emptyStateProps={{
                            title: "No Bookmarked Places",
                            description: "You will see places you bookmark here. Bookmark a place to get started!",
                            actionText: "Go to Places",
                            action: handleGoToPlaces,
                            section: "places",
                        }}
                        renderCardProps={() => ({ isBookmarked: true })}
                    />

                    <PlacesList
                        title="Suggested for You"
                        places={recommendedPlaces}
                        emptyStateProps={{
                            title: "No Suggested Places",
                            description: "Explore and join new places!",
                            actionText: "Go to Places",
                            action: handleGoToPlaces,
                            section: "places",
                        }}
                        renderCardProps={() => ({ showJoin: true, handleJoin: handleJoin })}
                    />
                </>
            )}
        </div>
    );
};

export default PlacesSection;
