import { router } from '@inertiajs/react';
import { useState } from 'react';
import DropdownSelection from './DropdownSelection';
import ImageUpload from './ImageUpload';
import FormField from './FormField';
import ActionButtons from './ActionButtons';
import FormHeader from './FormHeader';
import StatusField from './StatusField';
import GalleryUpload from './GalleryUpload';

const tagsOptions = [
  'Community', 'Culture', 'Environment', 'Financing', 'Gender',
  'Job Creation', 'Music', 'Climate', 'Food', 'Urban beautification',
  'Sports', 'Youth', 'Education'
]

const whatWeDoOptions = [
  'Campaigns', 'Community Activities', 'Research & Development', 'Shows & Exhibitions'
]

const ProjectRegistrationForm = ({ onBack }) => {
  const [formData, setFormData] = useState({
    name: '',
    location: '',
    description: '',
    tags: '',
    collaborators: '',
    seeking: '',
    links: '',
    whatWeDoOptions: '',
  });

  const [profileImage, setProfileImage] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);
  const [galleryImages, setGalleryImages] = useState([]);
  const [galleryPreviews, setGalleryPreviews] = useState([]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    setProfileImage(file);
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => setImagePreview(reader.result);
      reader.readAsDataURL(file);
    }
  };

  const handleRemoveImage = () => {
    setImagePreview(null);
    setProfileImage(null);
  };

  const handleGalleryChange = (e) => {
    const files = Array.from(e.target.files);
    setGalleryImages(files);

    const previews = files.map(file => {
      return new Promise((resolve) => {
        const reader = new FileReader();
        reader.onloadend = () => resolve(reader.result);
        reader.readAsDataURL(file);
      });
    });

    Promise.all(previews).then(setGalleryPreviews);
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    const payload = {
      ...formData,
      tags: formData.tags.split(',').map(tag => tag.trim()),
      collaborators: formData.collaborators.split(',').map(c => c.trim()),
      whatWeDo: formData.whatWeDo.split(',').map(c => c.trim()),
    };

    const formPayload = new FormData();
    Object.entries(payload).forEach(([key, value]) =>
      formPayload.append(key, typeof value === 'object' ? JSON.stringify(value) : value)
    );

    if (profileImage) {
      formPayload.append('profile_image', profileImage);
    }

    galleryImages.forEach((img, idx) => {
      formPayload.append(`gallery_images[]`, img);
    });

    router.post("/projects/new", formPayload, { 
      preserveScroll: true,
    onSuccess: () => {
      alert("saved!")
    }})
  };

  const handleSaveDraft = () => {
    alert("Pending feature!");
  };

  return (
    <form
      onSubmit={handleSubmit}
      className="p-6 relative rounded-xl font-inter space-y-6 w-full"
    >
      <FormHeader
        title="Register a Project"
        onClose={onBack}
        borderColor="border-primarygreen"
      />

      <div className="p-6 bg-white space-y-6 rounded-2xl shadow-lg">
        <ImageUpload
          label="Project Profile Picture"
          image={profileImage}
          onImageChange={handleImageChange}
          onImageRemove={handleRemoveImage}
          imagePreview={imagePreview}
        />

        <FormField
          label="Project Name"
          name="name"
          placeholder="Name of your project..."
          value={formData.name}
          onChange={handleChange}
          required
        />

        <FormField
          label="Location"
          name="location"
          placeholder="Where is your project based?"
          value={formData.location}
          onChange={handleChange}
          required
        />

        <FormField
          label="Project Description"
          name="description"
          type="textarea"
          placeholder="Tell us about your project"
          value={formData.description}
          onChange={handleChange}
          rows={4}
          required
        />

        <DropdownSelection
          formData={formData}
          setFormData={setFormData}
          options={whatWeDoOptions}
          label="What we do (max 5)"
          placeholder="Select options..."
          fieldName="whatWeDo"
          singleSelection={false}
          maxSelections={5} 
        />

        <DropdownSelection
          formData={formData}
          setFormData={setFormData}
          options={tagsOptions}
          label="Categories (max 5)"
          placeholder="Select categories..."
          fieldName="tags"
          singleSelection={false}
          maxSelections={5} 
        />

        <GalleryUpload
          images={galleryImages}
          imagePreviews={galleryPreviews}
          onImagesChange={handleGalleryChange}
          required
        />

        <FormField
          label="Collaborators (emails or usernames, comma-separated)"
          name="collaborators"
          placeholder="Collaborators that will be working on your project"
          value={formData.collaborators}
          onChange={handleChange}
        />
        
        <FormField
          label="External Links (comma-separated)"
          name="links"
          placeholder="Links to your project's website, social media, etc."
          value={formData.links}
          onChange={handleChange}
        />
        
        <StatusField
          value={formData.seeking}
          onChange={handleChange}
        />
        
        <ActionButtons
          primaryText="Create Project"
          secondaryText="Save Draft"
          onSecondaryClick={handleSaveDraft}
        />
      </div>
    </form>
  );
};

export default ProjectRegistrationForm;
