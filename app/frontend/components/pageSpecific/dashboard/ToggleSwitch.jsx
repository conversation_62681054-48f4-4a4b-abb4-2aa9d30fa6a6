/**
 * ToggleSwitch Component
 * A reusable toggle switch with label and color variants
 * @param {string} props.label - Label text for the toggle
 * @param {boolean} props.checked - Whether the toggle is checked
 * @param {Function} props.onChange - Change handler function
 * @param {boolean} props.disabled - Whether the toggle is disabled
 * @param {string} props.color - Color variant ('blue', 'yellow', 'green', etc.)
 */
const ToggleSwitch = ({
  label,
  checked = false,
  onChange,
  disabled = false,
  color = 'blue'
}) => {
  // Define color variants
  const colorVariants = {
    blue: {
      border: 'border-settingsblue',
      focus: 'peer-focus:ring-settingsblue',
      checked: 'peer-checked:bg-settingsblue'
    },
    yellow: {
      border: 'border-primaryyellow',
      focus: 'peer-focus:ring-primaryyellow',
      checked: 'peer-checked:bg-primaryyellow'
    },
  };

  const variant = colorVariants[color] || colorVariants.blue;

  return (
    <div className={`flex items-center gap-2 ${label ? 'justify-between' : 'justify-end'}`}>
      {label && (
        <div className="text-sm font-medium text-gray-700">{label}</div>
      )}
      <label className="relative inline-flex items-center cursor-pointer">
        <input
          type="checkbox"
          className="sr-only peer"
          checked={checked}
          onChange={onChange}
          disabled={disabled}
        />
        <div className={`w-11 h-6 bg-gray-200 border-2 ${variant.border} peer-focus:outline-none peer-focus:ring-2 ${variant.focus} rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all ${variant.checked} disabled:opacity-50`}></div>
      </label>
    </div>
  );
};

export default ToggleSwitch;
