import { Upload } from "lucide-react";
/**
 * PortfolioUpload Component
 * Upload section for portfolio images
 * @param {Function} props.onUpload - Upload handler function
 * @param {boolean} props.disabled - Whether upload is disabled
 */
const PortfolioUpload = ({ onUpload, disabled = false }) => {
  const handleFileSelect = (event) => {
    const files = Array.from(event.target.files);
    if (files.length && onUpload) {
      onUpload(files);
    }
  };

return (
    <div className="border-2 border-dashed border-primarybrown rounded-lg p-8 text-center">
        <div className="flex flex-col items-center space-y-4">
            <input
                type="file"
                accept="image/*"
                multiple
                onChange={handleFileSelect}
                disabled={disabled}
                className="hidden"
                id="portfolio-upload"
            />
            <label 
                htmlFor="portfolio-upload" 
                className={`px-4 py-2 text-gray-600 cursor-pointer ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
            <div className="text-primarybrown flex items-center justify-center">
                <Upload size={32} strokeWidth={2} />
            </div>
            <div>
                <p className="text-gray-600 font-medium">Upload Image</p>
                <p className="text-sm text-gray-500">(JPEG, JPG, PNG, Up to 1MB)</p>
            </div>
                Click to upload
            </label>
        </div>
    </div>
);
};

export default PortfolioUpload;