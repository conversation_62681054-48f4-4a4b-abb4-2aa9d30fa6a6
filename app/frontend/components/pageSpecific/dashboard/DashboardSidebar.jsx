import { Inertia } from "@inertiajs/inertia";
import {
  Bell,
  User,
  Users,
  FolderOpen,
  MapPin,
  HeartHandshake,
  MessageCircleMore,
  Settings,
  LogOut,
  BadgeCheck
} from "lucide-react";

const DashboardSidebar = ({ user, activeSection, setActiveSection }) => {
  return (
    <div className="flex-none sticky top-28 w-80 h-screen mt-4 rounded-xl border-gray-200 bg-gray-100 shadow-lg border-1 border-grey-200">
      <div className="text-center p-4">
        <img
          src={
            user.avatar_url ||
            `https://ui-avatars.com/api/?name=${user.first_name}+${user.last_name}&background=random`
          }
          alt="User Avatar"
          className="w-24 h-24 rounded-full mx-auto object-cover"
        />
        <h2 className="text-xl text-primarybrown font-semibold">{user.profile.first_name} {user.profile.last_name}</h2>
        <p className="text-sm text-gray-600">{user.email}</p>
        <button className="mt-2 bg-primarybrown text-white px-4 py-2 rounded-full flex items-center gap-2 mx-auto hover:bg-black hover:text-white">
          Verify Me
          <BadgeCheck size={16} fill="gold"/>
        </button>
      </div>
      <div className="flex flex-col gap-1 p-4">
        <button
          onClick={() => setActiveSection("newsfeed")}
          className={`px-6 py-2 border-t border-gray-200 text-left cursor-pointer flex items-center gap-3 justify-between ${activeSection === "newsfeed"
            ? "bg-settingsblue text-white rounded-full"
            : "bg-transparent hover:bg-gray-200"
            }`}
        >
          <div className="flex items-center gap-3">
            <Bell size={24} />
            News & Alerts
          </div>
          {( activeSection !== "newsfeed" && user.unread_notifications_count > 0) && (
          <span className="bg-red-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center">
            {user.unread_notifications_count}
          </span>
        )}
        </button>
        <button
          onClick={() => setActiveSection("profile")}
          className={`px-6 py-2 border-t border-gray-200 text-left cursor-pointer flex items-center gap-3 ${activeSection === "profile"
            ? "bg-primaryyellow text-black rounded-full"
            : "bg-transparent hover:bg-gray-200"
            }`}
        >
          <User size={24} />
          My Profile
        </button>
        <button
          onClick={() => setActiveSection("people")}
          className={`px-6 py-2 border-t border-gray-200 text-left cursor-pointer flex items-center gap-3 ${activeSection === "people"
            ? "bg-primaryyellow text-black rounded-full"
            : "bg-transparent hover:bg-gray-200"
            }`}
        >
          <Users size={24} />
          People
        </button>
        <button
          onClick={() => setActiveSection("projects")}
          className={`px-6 py-2 border-t border-gray-200 text-left cursor-pointer flex items-center gap-3 ${activeSection === "projects"
            ? "bg-primarygreen text-white rounded-full"
            : "bg-transparent hover:bg-gray-200"
            }`}
        >
          <FolderOpen size={24} />
          Projects
        </button>
        <button
          onClick={() => setActiveSection("places")}
          className={`px-6 py-2 border-t border-gray-200 text-left cursor-pointer flex items-center gap-3 ${activeSection === "places"
            ? "bg-primaryorange text-white rounded-full"
            : "bg-transparent hover:bg-gray-200"
            }`}
        >
          <MapPin size={24} />
          Places
        </button>
        <button
          onClick={() => setActiveSection("ubuntu")}
          className={`px-6 py-2 border-t border-gray-200 text-left cursor-pointer flex items-center gap-3 ${activeSection === "ubuntu"
            ? "bg-primarymaroon text-white rounded-full"
            : "bg-transparent hover:bg-gray-200"
            }`}
        >
          <HeartHandshake size={24} />
          Ubuntu Channels
        </button>
        <button
          onClick={() => setActiveSection("messages")}
          className={`px-6 py-2 border-t border-gray-200 text-left cursor-pointer flex items-center gap-3 justify-between ${activeSection === "messages"
            ? "bg-primarybrown text-white rounded-full"
            : "bg-transparent hover:bg-gray-200"
            }`}
        >
          <div className="flex items-center gap-3">
            <MessageCircleMore size={24} />
            Messages
          </div>
          <span className={`text-xs rounded-full px-2 py-1 min-w-[20px] text-center ${activeSection === "messages" ? "bg-white text-primarybrown" : "bg-primarybrown text-white"}`}>
            3
          </span>
        </button>
        <button
          onClick={() => setActiveSection("settings")}
          className={`px-6 py-2 border-t border-gray-200 text-left cursor-pointer flex items-center gap-3 ${activeSection === "settings"
            ? "bg-settingsblue text-white rounded-full"
            : "bg-transparent hover:bg-gray-200"
            }`}
        >
          <Settings size={24} />
          Settings
        </button>
        <button
          onClick={() => Inertia.delete("/users/sign_out")}
          className="mt-4 bg-red-300 rounded-full px-6 py-2 hover:bg-red-700 hover:text-white cursor-pointer flex items-center gap-3"
        >
          <LogOut size={24} />
          Logout
        </button>
      </div>
    </div>
  );
};

export default DashboardSidebar;
