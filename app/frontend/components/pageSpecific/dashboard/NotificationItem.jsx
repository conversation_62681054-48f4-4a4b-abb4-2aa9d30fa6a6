// app/frontend/components/pageSpecific/dashboard/NotificationItem.jsx
import { Bookmark, Star, Info, Heart, User } from 'lucide-react';

// Map notification types to Lucide Icon components
const IconMap = {
    bookmark: Bookmark,
    star: Star,
    suggestion: Info,
    like: Heart,
    connect: User
};

// Notification item component
const NotificationItem = ({ 
    type, 
    title, 
    description, 
    actionText, 
    timeAgo, 
    read = false,
    onClick
}) => {
    const IconComponent = IconMap[type] || Info; // Fallback to Info if type is unknown
    return (
        <div className={`p-4 rounded-lg ${read ? 'bg-gray-100': 'bg-blue-100 border-1 border-settingsblue'}`}
        onClick={onClick}
        >
            <div className="flex items-start space-x-3">
                {/* Render the selected Lucide icon component */}
                <div className="w-10 h-10 bg-gray-400 rounded-full flex items-center justify-center">
                    <IconComponent className="w-5 h-5 text-white" />
                </div>
                <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                        <h3 className="text-sm font-semibold text-gray-900">{title}</h3>
                        <span className="text-xs text-gray-500">{timeAgo}</span>
                    </div>
                    <p className="text-sm text-gray-600 mt-1 italic">{description}</p>
                    <button className="text-blue-600 text-sm font-bold mt-2 hover:text-blue-800">
                        {actionText}
                    </button>
                </div>
            </div>
        </div>
    );
};

export default NotificationItem;
