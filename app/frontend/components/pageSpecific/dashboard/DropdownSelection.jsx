import { useState } from 'react';
import { ChevronDown, X } from 'lucide-react';

const DropdownSelection = ({ 
  formData, 
  setFormData, 
  options, 
  label = "Tags (max 5)", 
  placeholder = "Add tags...",
  fieldName = "tags", // New prop to specify which field to update
  maxSelections = 5, // New prop to control max selections
  singleSelection = false // New prop to enable single selection mode
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleItemClick = (item) => {
    if (singleSelection) {
      // Single selection mode - replace the current selection
      setFormData({ ...formData, [fieldName]: item });
      setIsOpen(false); // Close dropdown after selection
    } else {
      // Multiple selection mode - existing logic
      const currentItems = formData[fieldName] ? formData[fieldName].split(',').map(t => t.trim()) : [];

      if (!currentItems.includes(item) && currentItems.length < maxSelections) {
        const newItems = [...currentItems, item].join(', ');
        setFormData({ ...formData, [fieldName]: newItems });
      }
    }
  };

  const removeItem = (itemToRemove) => {
    if (singleSelection) {
      // Single selection mode - clear the selection
      setFormData({ ...formData, [fieldName]: '' });
    } else {
      // Multiple selection mode - existing logic
      const currentItems = formData[fieldName].split(',').map(t => t.trim());
      const newItems = currentItems.filter(item => item !== itemToRemove).join(', ');
      setFormData({ ...formData, [fieldName]: newItems });
    }
  };

  const selectedItems = singleSelection 
    ? (formData[fieldName] ? [formData[fieldName]] : [])
    : (formData[fieldName] ? formData[fieldName].split(',').map(t => t.trim()).filter(t => t) : []);

  const displayValue = singleSelection 
    ? (formData[fieldName] || '')
    : (formData[fieldName] || '');

  return (
    <div className="w-full mx-auto">
      <div className="relative">
        <label className="block font-medium text-gray-700 mb-1">
          {label} <span className="text-red-500">*</span>
        </label>

        {/* Selected items display */}
        {selectedItems.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-2">
            {selectedItems.map((item, index) => (
              <span
                key={index}
                className="inline-flex items-center px-3 py-1 bg-gray-200 text-gray-800 text-sm rounded-full"
              >
                {item}
                <button
                  type="button"
                  onClick={() => removeItem(item)}
                  className="ml-2 text-gray-600 hover:text-gray-800"
                >
                  <X size={14} />
                </button>
              </span>
            ))}
          </div>
        )}

        {/* Input field with dropdown trigger */}
        <div className="relative">
          <input
            type="text"
            name={fieldName}
            placeholder={placeholder}
            value={displayValue}
            onChange={handleChange}
            className="w-full border rounded-2xl p-3 pr-10"
            required
            readOnly={singleSelection} // Make input read-only in single selection mode
          />
          <button
            type="button"
            onClick={() => setIsOpen(!isOpen)}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
          >
            <ChevronDown size={20} className={`transition-transform ${isOpen ? 'rotate-180' : ''}`} />
          </button>
        </div>

        {/* Dropdown */}
        {isOpen && (
          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
            {options.map((item) => (
              <button
                key={item}
                type="button"
                onClick={() => handleItemClick(item)}
                className={`w-full text-left px-4 py-2 hover:bg-gray-100 transition-colors ${
                  selectedItems.includes(item)
                    ? 'bg-gray-50 text-gray-700 font-medium'
                    : (!singleSelection && selectedItems.length >= maxSelections)
                    ? 'text-gray-400 cursor-not-allowed'
                    : 'text-gray-700'
                }`}
                disabled={!singleSelection && selectedItems.length >= maxSelections && !selectedItems.includes(item)}
              >
                {item}
                {selectedItems.includes(item) && (
                  <span className="float-right text-gray-500">✓</span>
                )}
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Close dropdown when clicking outside */}
      {isOpen && (
        <div
          className="fixed inset-0 z-5"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
};

export default DropdownSelection;

