/**
 * FormField Component
 * Reusable form field component for inputs and textareas
 * @param {string} props.label - Label text for the field
 * @param {string} props.name - Name attribute for the input
 * @param {string} props.type - Input type ('text', 'textarea', 'select')
 * @param {string} props.placeholder - Placeholder text
 * @param {string} props.value - Current value
 * @param {Function} props.onChange - Change handler function
 * @param {boolean} props.required - Whether field is required
 * @param {number} props.rows - Number of rows for textarea
 * @param {Array} props.options - Options for select field
 * @param {string} props.helperText - Helper text below the field
 */
const FormField = ({
  label,
  name,
  type = 'text',
  placeholder,
  value,
  onChange,
  required = false,
  rows = 4,
  options = [],
  helperText
}) => {
  const renderInput = () => {
    const baseClasses = "w-full border rounded-2xl p-3";

    switch (type) {
      case 'textarea':
        return (
          <textarea
            name={name}
            placeholder={placeholder}
            value={value}
            onChange={onChange}
            rows={rows}
            className={baseClasses}
            required={required}
          />
        );
      
      case 'select':
        return (
          <select
            name={name}
            value={value}
            onChange={onChange}
            className={baseClasses}
            required={required}
          >
            {options.map((option, index) => (
              <option key={index} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        );
      
      default:
        return (
          <input
            type={type}
            name={name}
            placeholder={placeholder}
            value={value}
            onChange={onChange}
            className={baseClasses}
            required={required}
          />
        );
    }
  };

  return (
    <div className="mb-4">
      <label className="block font-semibold text-md text-gray-700 mb-1">
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      {renderInput()}
      {helperText && (
        <p className="text-sm text-gray-500 mt-1">{helperText}</p>
      )}
    </div>
  );
};

export default FormField;
