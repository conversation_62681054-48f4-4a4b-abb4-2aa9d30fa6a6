import PortfolioProject from './PortfolioProject';
import PortfolioUpload from './PortfolioUpload';

/**
 * PortfolioSection Component
 * Complete portfolio section with projects and upload functionality
 * @param {Array} props.projects - Array of portfolio projects
 * @param {boolean} props.isPublic - Whether portfolio is public
 * @param {Function} props.onTogglePublic - Toggle public/private handler
 * @param {Function} props.onUpload - Upload handler
 * @param {Function} props.onEditProject - Edit project handler
 * @param {Function} props.onDeleteProject - Delete project handler
 * @param {boolean} props.editable - Whether projects are editable
 */
const PortfolioSection = ({ 
  projects, 
  isPublic = true, 
  onTogglePublic, 
  onUpload, 
  onEditProject, 
  onDeleteProject,
  editable = false
}) => {
  return (
    <>
      {projects.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          {projects.map((project, index) => (
            <PortfolioProject
              key={index}
              image={project}
              title={project.title}
              description={project.description}
              onEdit={() => onEditProject && onEditProject(index)}
              onDelete={() => onDeleteProject && onDeleteProject(index)}
              editable={editable}
            />
          ))}
        </div>
      )}
      
      <PortfolioUpload onUpload={onUpload} />
    </>
  );
};

export default PortfolioSection;