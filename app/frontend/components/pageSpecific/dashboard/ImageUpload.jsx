import { useState } from 'react';

/**
 * ImageUpload Component
 * Reusable image upload component with preview functionality
 * @param {string} props.label - Label text for the upload section
 * @param {File|null} props.image - Current image file
 * @param {Function} props.onImageChange - Callback when image is selected
 * @param {Function} props.onImageRemove - Callback when image is removed
 * @param {string} props.imagePreview - Preview URL for the image
 * @param {string} props.shape - Shape of the image ('rounded' or 'square')
 */
const ImageUpload = ({ 
  label, 
  image, 
  onImageChange, 
  onImageRemove, 
  imagePreview,
  shape = 'square'
}) => {
  return (
    <div className="flex items-center gap-10">
      {imagePreview ? (
        <img
          src={imagePreview}
          alt="Preview"
          className={`h-48 w-48 ${shape === 'rounded' ? 'rounded-full' : 'rounded-xl'} object-cover border border-gray-100`}
        />
      ) : (
        <div className={`h-48 w-48 ${shape === 'rounded' ? 'rounded-full' : 'rounded-xl'} border border-gray-300 bg-gray-100 flex items-center justify-center text-gray-400 text-sm`}>
          No Image
        </div>
      )}

      <div className="flex flex-col">
        <span className="font-semibold text-md text-gray-700 mb-2">{label}</span>
        <div className="flex gap-3 justify-center">
          <label className="mt-2 bg-primarybrown w-48 text-white text-center px-4 py-2 rounded-full flex items-center justify-center gap-3 hover:bg-black hover:text-white cursor-pointer">
            Upload
            <input
              type="file"
              accept="image/*"
              onChange={onImageChange}
              className="hidden"
            />
          </label>
          <button
            type="button"
            onClick={onImageRemove}
            className="mt-2 w-48 text-gray-700 px-4 py-2 border-2 border-primarybrown rounded-full flex items-center justify-center gap-3 hover:bg-black hover:text-white"
          >
            Remove
          </button>
        </div>
      </div>
    </div>
  );
};

export default ImageUpload;