/**
 * ActionButtons Component
 * Reusable action buttons component for form submissions
 * @param {string} props.primaryText - Text for primary action button
 * @param {string} props.secondaryText - Text for secondary action button
 * @param {Function} props.onPrimaryClick - Primary button click handler
 * @param {Function} props.onSecondaryClick - Secondary button click handler
 * @param {string} props.primaryType - Button type for primary button (default: 'submit')
 * @param {string} props.secondaryType - Button type for secondary button (default: 'button')
 * @param {boolean} props.disabled - Whether buttons should be disabled
 */
const ActionButtons = ({
  primaryText,
  secondaryText,
  onPrimaryClick,
  onSecondaryClick,
  primaryType = 'submit',
  secondaryType = 'button',
  disabled = false
}) => {
  return (
    <div className="flex gap-3 justify-center">
      <button
        type={primaryType}
        onClick={onPrimaryClick}
        disabled={disabled}
        className="w-1/2 bg-primarybrown text-lg text-white font-medium py-3 rounded-full hover:bg-black cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {primaryText}
      </button>
      <button
        type={secondaryType}
        onClick={onSecondaryClick}
        disabled={disabled}
        className="w-1/2 border-2 border-primarybrown text-lg text-black font-medium py-3 rounded-full hover:bg-black hover:text-white hover:border-black cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {secondaryText}
      </button>
    </div>
  );
};

export default ActionButtons;
