import ToggleSwitch from './ToggleSwitch';

/**
 * SettingItem Component
 * Renders an individual setting item with label, description, and toggle
 * @param {string} props.label - Setting label
 * @param {string} props.description - Setting description
 * @param {boolean} props.checked - Whether the setting is enabled
 * @param {Function} props.onChange - Change handler function
 * @param {boolean} props.disabled - Whether the setting is disabled
 * @param {string} props.color - Color variant for the toggle
 */
const SettingItem = ({
    label,
    description,
    checked = false,
    onChange,
    disabled = false,
    color = 'blue'
}) => {
    return (
        <div className="p-4 flex items-start justify-between gap-4">
            <div className="flex-1">
                <div className="font-medium text-gray-900 mb-1">
                    {label}
                </div>
                {description && (
                    <div className="text-sm text-gray-500 leading-relaxed">
                        {description}
                    </div>
                )}
            </div>
            <div className="flex-shrink-0">
                <ToggleSwitch
                    checked={checked}
                    onChange={(e) => onChange(e.target.checked)}
                    disabled={disabled}
                    color={color}
                />
            </div>
        </div>
    );
};

export default SettingItem;
