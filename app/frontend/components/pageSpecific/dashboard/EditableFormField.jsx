import { useState, useEffect } from 'react'; // Added useEffect for consistency
import { Pen } from 'lucide-react';

/**
 * EditableFormField Component
 * Profile field component that displays as read-only with edit functionality,
 * or as a static display if readOnly prop is true.
 * @param {string} props.label - Label text for the field
 * @param {string} props.name - Name attribute for the input
 * @param {string} props.type - Input type ('text', 'textarea', 'select', 'email', 'tel', etc.)
 * @param {string} props.placeholder - Placeholder text when editing
 * @param {string} props.value - Current value
 * @param {Function} props.onChange - Change handler function (to update parent state immediately)
 * @param {boolean} props.required - Whether field is required
 * @param {number} props.rows - Number of rows for textarea
 * @param {Array} props.options - Options for select field
 * @param {string} props.helperText - Helper text below the field
 * @param {string} props.emptyText - Text to show when value is empty (default: "Not provided")
 * @param {boolean} props.readOnly - If true, the field is always in display mode and not editable.
 */
const EditableFormField = ({
  label,
  name,
  type = 'text',
  placeholder,
  value,
  onChange,
  required = false,
  rows = 4,
  options = [],
  helperText,
  emptyText = "Not provided",
  readOnly = false // New prop for read-only functionality
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [tempValue, setTempValue] = useState(value);

  // Sync tempValue with the prop value, useful when parent updates or on initial load
  useEffect(() => {
    setTempValue(value);
  }, [value]);

  const handleEdit = () => {
    if (readOnly) return; // Prevent editing if readOnly is true
    setTempValue(value);
    setIsEditing(true);
  };

  const handleBlur = () => {
    if (readOnly) return; // Prevent blur actions if readOnly
    // When the input loses focus, commit the changes if tempValue is different from original value
    if (tempValue !== value) {
      onChange({ target: { name, value: tempValue } });
    }
    setIsEditing(false);
  };

  const handleKeyDown = (e) => {
    if (readOnly) return; // Prevent keydown actions if readOnly
    if (e.key === 'Enter' && type !== 'textarea') {
      e.preventDefault(); // Prevent form submission
      handleBlur(); // Commit changes on Enter for text-like inputs
    } else if (e.key === 'Escape') {
      setTempValue(value); // Revert to original value on Escape
      setIsEditing(false);
    }
  };

  const formatDisplayValue = (val) => {
    if (!val || val.toString().trim() === '') {
      return emptyText;
    }

    if (type === 'select' && options.length > 0) {
      const option = options.find(opt => opt.value === val);
      return option ? option.label : val;
    }

    return val;
  };

  const renderInput = () => {
    const baseClasses = "w-full border border-gray-300 rounded-lg p-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none";

    // Add disabled attribute based on readOnly prop
    const inputProps = {
      name,
      placeholder,
      value: tempValue,
      onChange: (e) => setTempValue(e.target.value),
      onBlur: handleBlur,
      onKeyDown: handleKeyDown,
      className: baseClasses,
      required: required,
      autoFocus: true, // Auto focus when entering edit mode
      disabled: readOnly // Disable input if readOnly
    };

    switch (type) {
      case 'textarea':
        return <textarea {...inputProps} rows={rows} />;
      case 'select':
        return (
          <select {...inputProps}>
            <option value="">Select an option</option>
            {options.map((option, index) => (
              <option key={index} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        );
      default:
        return <input {...inputProps} type={type} />;
    }
  };

  const displayValue = formatDisplayValue(value);
  const isEmpty = !value || value.toString().trim() === '';

  return (
    <div className="mb-6">
      <label className="block font-semibold text-md text-gray-600 mb-2">
        {label} {required && <span className="text-red-500">*</span>}
      </label>

      {/* If readOnly is true, always show the display mode without the edit button */}
      {isEditing && !readOnly ? (
        <div className="space-y-3">
          {renderInput()}
        </div>
      ) : (
        <div className="flex items-center group">
          <div>
            <div className={`p-3 rounded-lg min-h-[48px] flex items-center ${isEmpty ? 'text-gray-600 italic' : 'text-gray-900'}`}>
              {type === 'textarea' ? (
                <div className="whitespace-pre-wrap">{displayValue}</div>
              ) : (
                <span>{displayValue}</span>
              )}
            </div>
          </div>
          {/* Only show the edit button if not readOnly */}
          {!readOnly && (
            <button
              type="button"
              onClick={handleEdit}
              className="ml-2 p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
              aria-label={`Edit ${label}`}
            >
              <Pen size={16} />
            </button>
          )}
        </div>
      )}

      {helperText && (
        <p className="text-sm text-gray-500 mt-1">{helperText}</p>
      )}
    </div>
  );
};

export default EditableFormField;