import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import Gallery from './Gallery';

describe('Gallery Component', () => {
  const mockItems = [
    { id: 1, name: "Project 1", image: "/image1.jpg" },
    { id: 2, name: "Project 2", image: "/image2.jpg" },
    { id: 3, name: "Project 3", image: "/image3.jpg" },
    { id: 4, name: "Project 4", image: "/image4.jpg" }
  ];

  it('renders the gallery title correctly', () => {
    render(<Gallery items={mockItems} title="Test Gallery" />);
    expect(screen.getByText('Test Gallery')).toBeInTheDocument();
  });

  it('renders the default title if none provided', () => {
    render(<Gallery items={mockItems} />);
    expect(screen.getByText('Gallery')).toBeInTheDocument();
  });

  it('renders all gallery items', () => {
    render(<Gallery items={mockItems} />);
    
    const images = screen.getAllByRole('img');
    expect(images).toHaveLength(mockItems.length);
    
    images.forEach((img, index) => {
      expect(img).toHaveAttribute('src', mockItems[index].image);
      expect(img).toHaveAttribute('alt', mockItems[index].name);
    });
  });

  it('applies correct grid layout classes', () => {
    render(<Gallery items={mockItems} />);
    
    const grid = screen.getByRole('list');
    expect(grid).toHaveClass('grid', 'grid-cols-2', 'md:grid-cols-3', 'lg:grid-cols-4', 'gap-4');
  });
});