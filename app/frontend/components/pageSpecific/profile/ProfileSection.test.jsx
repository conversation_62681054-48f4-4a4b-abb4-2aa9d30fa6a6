import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import ProfileSection from './ProfileSection';

describe('ProfileSection Component', () => {
  const mockUser = {
    name: "<PERSON><PERSON>",
    title: "Project Manager",
    location: "Mombasa, Kenya",
    status: "Online",
    avatar: "/sample-avatar.jpg",
    bio: "Sample bio text",
    professionalFocus: ["Photography", "Conservation"],
    skills: ["Visual Storytelling", "Community Engagement"]
  };

  it('renders user information correctly', () => {
    render(<ProfileSection user={mockUser} />);
    
    expect(screen.getByText(mockUser.name)).toBeInTheDocument();
    expect(screen.getByText(mockUser.title)).toBeInTheDocument();
    expect(screen.getByText(mockUser.location)).toBeInTheDocument();
    expect(screen.getByText(mockUser.status)).toBeInTheDocument();
  });

  it('displays professional focus items', () => {
    render(<ProfileSection user={mockUser} />);
    
    mockUser.professionalFocus.forEach(focus => {
      expect(screen.getByText(focus)).toBeInTheDocument();
    });
  });

  it('displays skills', () => {
    render(<ProfileSection user={mockUser} />);
    
    mockUser.skills.forEach(skill => {
      expect(screen.getByText(skill)).toBeInTheDocument();
    });
  });

  it('renders the message button', () => {
    render(<ProfileSection user={mockUser} />);
    
    expect(screen.getByText('Message')).toBeInTheDocument();
  });

  it('displays the verification badge', () => {
    render(<ProfileSection user={mockUser} />);
    
    expect(screen.getByAltText('Verified')).toBeInTheDocument();
  });
});