import React from 'react';
import { ArrowUpRight } from 'lucide-react';
import { router } from '@inertiajs/react';

// Portfolio Section Component
const PortfolioSection = ({ user, EmptyState }) => (
  <div className="bg-[#FDF45AD9] rounded-xl p-6 mb-6">
    <h2 className="text-xl font-bold text-gray-800 mb-6">My Portfolio</h2>

    {/* Projects */}
    <div className="mb-8">
      <div className="flex justify-between items-center mb-4">
        <h3 className="font-semibold text-gray-800">My Projects</h3>
        {user.projects?.length > 0 && (
          <button 
          className="flex items-center text-black hover:underline text-sm"
          onClick={() => {router.visit("/projects")}}
          >
            <span>View All Projects</span> <ArrowUpRight className="w-4 h-4" />
          </button>
        )}
      </div>
      {user.projects?.length > 0 ? (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {user.projects.map((project) => (
            <div key={project.id} className="group">
              <img
                src={project?.image[0].im}
                alt={project.name}
                className="w-full h-30 md:h-40 lg:h-60 object-cover rounded-lg shadow-md group-hover:shadow-lg transition-shadow mb-2"
              />
              <p className="text-sm md:text-base font-medium text-gray-800 text-center">{project.name}</p>
            </div>
          ))}
        </div>
      ) : (
        <EmptyState type="projects" />
      )}
    </div>

    {/* Places */}
    <div className="mb-8">
      <div className="flex justify-between items-center mb-4">
        <h3 className="font-semibold text-gray-800">My Places</h3>
        {user.places?.length > 0 && (
          <button 
          className="flex items-center text-black hover:underline text-sm"
          onClick={() => {router.visit("/places")}}
          >
            <span>View All Places</span> <ArrowUpRight className="w-4 h-4" />
          </button>
        )}
      </div>
      {user.places?.length > 0 ? (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {user.places.map((place) => (
            <div key={place.id} className="group">
              <img
                src={place.image}
                alt={place.name}
                className="w-full h-30 md:h-40 lg:h-60 object-cover rounded-lg shadow-md group-hover:shadow-lg transition-shadow mb-2"
              />
              <p className="text-sm md:text-base font-medium text-gray-800 text-center">{place.name}</p>
            </div>
          ))}
        </div>
      ) : (
        <EmptyState type="places" />
      )}
    </div>

    {/* Forums */}
    <div>
      <div className="flex justify-between items-center mb-4">
        <h3 className="font-semibold text-gray-800">My Forums</h3>
        {user.forums?.length > 0 && (
          <button 
          className="flex items-center text-black hover:underline text-sm"
          onClick={() => {router.visit("/forums")}}
          >
            <span>View All Forums</span> <ArrowUpRight className="w-4 h-4" />
          </button>
        )}
      </div>
      {user.forums?.length > 0 ? (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {user.forums.map((forum) => (
            <div key={forum.id} className="group">
              <img
                src={forum.image}
                alt={forum.name}
                className="w-full h-30 md:h-40 lg:h-60 object-cover rounded-lg shadow-md group-hover:shadow-lg transition-shadow mb-2"
              />
              <p className="text-sm md:text-base font-medium text-gray-800 text-center">{forum.name}</p>
            </div>
          ))}
        </div>
      ) : (
        <EmptyState type="forums" />
      )}
    </div>
  </div>
);

export default PortfolioSection;