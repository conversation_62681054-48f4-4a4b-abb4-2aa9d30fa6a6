// app/frontend/components/pageSpecific/profile/Toggle.jsx
import React, { useState } from 'react';
import { ArrowRight, Bookmark } from 'lucide-react';
import { Link } from '@inertiajs/react';
import { useBookmark } from '../../../custom_hooks/bookmark';
import UnbookmarkModal from '../../shared/UnbookmarkModal';

const Toggle = ({ user }) => {
  const { 
    bookmarked, 
    toggleBookmark, 
    showUnbookmarkModal, 
    confirmUnbookmark, 
    cancelUnbookmark 
  } = useBookmark({
    id: user.id,
    type: 'Profile',
    initialBookmarked: user.bookmarkedProfileIds.includes(user.id),
  });
  const [bookmarkHover, setBookmarkHover] = useState(false);

  return (
    <div className="pt-24 px-4 py-3">
      <div className="flex items-center justify-between max-w-9xl mx-auto">
        <Link href="/people">
          <button className="flex items-center text-black font-semibold hover:text-gray-800">
            <ArrowRight className="w-5 h-5 rotate-180 mr-2" data-testid="arrow-right" />
            View More Profiles
          </button>
        </Link>
        <button
          type="button"
          onClick={(e) => { toggleBookmark(e); }}
          onMouseEnter={() => setBookmarkHover(true)}
          onMouseLeave={() => setBookmarkHover(false)}
          className={`p-0.5 md:p-1 rounded-md md:rounded-lg z-20 transition-all duration-200 transform hover:scale-110 cursor-pointer ${
            bookmarked 
              ? 'bg-primaryyellow text-black shadow-md' 
              : bookmarkHover 
                ? 'bg-primaryyellow text-black shadow-md' 
                : 'bg-white/80 text-gray-600 hover:bg-white'
          }`}
          aria-label={bookmarked ? "Remove bookmark" : "Add bookmark"}
        >
          <Bookmark 
            size={24} 
            className={`transition-all duration-200 ${
              bookmarked ? 'fill-current' : ''
            }`}
          />
        </button>
      </div>
      {/* Unbookmark Confirmation Modal */}
      <UnbookmarkModal
        isOpen={showUnbookmarkModal}
        onClose={cancelUnbookmark}
        onConfirm={confirmUnbookmark}
        name={user.name}
      />
    </div>
  );
};

export default Toggle;
