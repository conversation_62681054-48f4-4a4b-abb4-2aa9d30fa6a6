import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import CommunityStanding from './CommunityStanding';

describe('CommunityStanding Component', () => {
  const mockUser = {
    communityRating: 4.3,
    totalRatings: 134,
    friendlinessLevel: "Very Friendly",
    associatedProjects: ["FlipFlop Recycling", "Ocean Cleanup"],
    associatedPlaces: ["Oceania Reefs"],
    associatedForums: ["Environmental Photographers"]
  };

  it('renders community rating correctly', () => {
    render(<CommunityStanding user={mockUser} />);
    
    expect(screen.getByText(`${mockUser.communityRating}(${mockUser.totalRatings})`)).toBeInTheDocument();
    expect(screen.getByText(mockUser.friendlinessLevel)).toBeInTheDocument();
  });

  it('displays associated projects', () => {
    render(<CommunityStanding user={mockUser} />);
    
    mockUser.associatedProjects.forEach(project => {
      expect(screen.getByText(project)).toBeInTheDocument();
    });
  });

  it('displays associated places', () => {
    render(<CommunityStanding user={mockUser} />);
    
    mockUser.associatedPlaces.forEach(place => {
      expect(screen.getByText(place)).toBeInTheDocument();
    });
  });

  it('displays associated forums', () => {
    render(<CommunityStanding user={mockUser} />);
    
    mockUser.associatedForums.forEach(forum => {
      expect(screen.getByText(forum)).toBeInTheDocument();
    });
  });

  it('renders the correct number of rating stars', () => {
    render(<CommunityStanding user={mockUser} />);
    
    const stars = screen.getAllByTestId('star-icon');
    expect(stars).toHaveLength(5);
  });
});