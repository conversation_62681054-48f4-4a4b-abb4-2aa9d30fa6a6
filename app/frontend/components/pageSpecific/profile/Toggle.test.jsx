import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import Toggle from './Toggle';

describe('Toggle Component', () => {
  it('renders the toggle component correctly', () => {
    render(<Toggle />);
    
    // Check if the button text is present
    expect(screen.getByText('View More Profiles')).toBeInTheDocument();
    
    // Check if both icons are rendered
    expect(screen.getByTestId('arrow-right')).toBeInTheDocument();
    expect(screen.getByTestId('bookmark')).toBeInTheDocument();
  });

  it('maintains correct layout classes', () => {
    render(<Toggle />);
    
    // Check if the main container has correct padding
    const container = screen.getByRole('navigation');
    expect(container).toHaveClass('px-4 py-3');
    
    // Check if the inner container has correct flex layout
    const innerContainer = container.firstChild;
    expect(innerContainer).toHaveClass('flex items-center justify-between max-w-9xl mx-auto');
  });
});