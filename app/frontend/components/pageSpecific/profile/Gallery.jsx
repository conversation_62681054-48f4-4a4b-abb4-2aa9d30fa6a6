import React from 'react';

// Gallery Component
const Gallery = ({ items, title = "Gallery", EmptyState }) => (
  <div className="bg-[#FDF45AD9] rounded-xl p-6 mb-6">
    <h2 className="text-xl font-bold text-gray-800 mb-4">{title}</h2>
    {items.length !== 0 ? (
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        {items.slice(0, 4).map((img, index) => (
          <img
            key={index}
            src={img}
            alt={`Gallery Image ${index + 1}`}
            className="w-full h-30 md:h-40 lg:h-60 object-cover rounded-lg shadow-md hover:shadow-lg transition-shadow"
          />
        ))}
      </div>
    ) : (
      <EmptyState type="gallery" />
    )}
  </div>
);

export default Gallery;