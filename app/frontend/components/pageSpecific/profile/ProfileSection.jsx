// app/frontend/components/pageSpecific/profile/ProfileSection.jsx
import React from 'react';
import { MapPin, MessageCircle, Camera, Leaf, Heart, BookOpen, Award } from 'lucide-react';
import verify from '/assets/verification.png';

// Profile Section Component (About Me + Profile Info)
const ProfileSection = ({ user }) => (
  <div className=" mb-6">
    <div className="flex flex-col md:flex-row gap-6">
      {/* Left Side - Profile Info */}
      <div className="md:w-1/4 bg-[#FDF45AD9] rounded-xl py-6">
        <div className="text-center">
          <div className="relative inline-block mb-4">
            <img
              src={user?.avatar}
              alt={user.name}
              className="w-30 h-30 rounded-full shadow-lg object-cover object-center"
            />
            {user.verified &&
              <img
                src={verify} // Replace with your verification icon path
                alt="Verified"
                className="absolute bottom-0 right-0 w-9 h-9 rounded-full p-1 "
              />
            }
          </div>
          <h1 className="text-xl font-bold text-gray-800 mb-1">{user.name}</h1>
          <p className="text-gray-700 font-medium mb-2">{user.title}</p>
          <div className="flex mb-4 items-center justify-center">
            <span className=" text-black px-3 py-1 text-base flex items-center">{user.profession}</span>
          </div>
          <div className="flex items-center justify-center text-gray-600 mb-2">
            <MapPin className="w-4 h-4 mr-1" />
            <span className="text-sm">{user.address}</span>
          </div>
          <div className="flex items-center justify-center mb-4">
            <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
            <span className="text-sm text-gray-600">{user.status}</span>
          </div>
          <button className="bg-primarybrown text-white px-6 py-2 rounded-full transition-colors flex items-center mx-auto">
            <MessageCircle className="w-4 h-4 mr-2" />
            Message
          </button>
        </div>
      </div>

      {/* Right Side - About Me */}
      <div className="md:w-3/4 bg-[#FDF45AD9] rounded-xl p-6">
        <h2 className="text-xl font-bold text-gray-800 mb-4">About Me</h2>
        <p className="text-gray-700 mb-6 leading-relaxed text-sm">{user.bio}</p>

        <div className="mb-4">
          <h3 className="font-semibold text-gray-800 mb-3">Professional Focus</h3>
          <div className="flex flex-wrap gap-2 mb-4">
            {user.professionalFocus.map((focus, index) => (
              <span key={index} className="bg-primarybrown text-white px-3 py-1 rounded-lg text-base flex items-center">{focus}</span>
            ))}
            {/* {user.professionalFocus.map((focus, index) => (
              <span key={index} className="bg-primarybrown text-white px-3 py-1 rounded-lg text-base flex items-center">
                {focus === 'Photography' && <Camera className="w-3 h-3 mr-1" />}
                {focus === 'Conservation' && <Leaf className="w-3 h-3 mr-1" />}
                {focus === 'Activism' && <Heart className="w-3 h-3 mr-1" />}
                {focus === 'Journalism' && <BookOpen className="w-3 h-3 mr-1" />}
                {focus === 'Education' && <Award className="w-3 h-3 mr-1" />}
                {focus}
              </span>
            ))} */}
          </div>
        </div>

        <div>
          <h3 className="font-semibold text-gray-800 mb-3">Skills</h3>
          <div className="flex flex-wrap gap-2">
            {user.skills.map((skill, index) => (
              <span key={index} className="bg-primarybrown text-white px-3 py-1 rounded-lg text-base">
                {skill}
              </span>
            ))}
          </div>
        </div>
      </div>
    </div>
  </div>
);

export default ProfileSection;