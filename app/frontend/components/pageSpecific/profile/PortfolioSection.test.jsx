import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import PortfolioSection from './PortfolioSection';

describe('PortfolioSection Component', () => {
  const mockUser = {
    projects: [
      { id: 1, name: "Project 1", image: "/project1.jpg" },
      { id: 2, name: "Project 2", image: "/project2.jpg" }
    ],
    places: [
      { id: 1, name: "Place 1", image: "/place1.jpg" },
      { id: 2, name: "Place 2", image: "/place2.jpg" }
    ],
    forums: [
      { id: 1, name: "Forum 1", image: "/forum1.jpg" },
      { id: 2, name: "Forum 2", image: "/forum2.jpg" }
    ]
  };

  it('renders all section titles', () => {
    render(<PortfolioSection user={mockUser} />);
    
    expect(screen.getByText('My Portfolio')).toBeInTheDocument();
    expect(screen.getByText('My Projects')).toBeInTheDocument();
    expect(screen.getByText('My Places')).toBeInTheDocument();
    expect(screen.getByText('My Forums')).toBeInTheDocument();
  });

  it('renders all view all buttons', () => {
    render(<PortfolioSection user={mockUser} />);
    
    expect(screen.getByText('View All Projects')).toBeInTheDocument();
    expect(screen.getByText('View All Places')).toBeInTheDocument();
    expect(screen.getByText('View All Forums')).toBeInTheDocument();
  });

  it('renders all projects with images and names', () => {
    render(<PortfolioSection user={mockUser} />);
    
    mockUser.projects.forEach(project => {
      const image = screen.getByAltText(project.name);
      expect(image).toHaveAttribute('src', project.image);
      expect(screen.getByText(project.name)).toBeInTheDocument();
    });
  });

  it('renders all places with images and names', () => {
    render(<PortfolioSection user={mockUser} />);
    
    mockUser.places.forEach(place => {
      const image = screen.getByAltText(place.name);
      expect(image).toHaveAttribute('src', place.image);
      expect(screen.getByText(place.name)).toBeInTheDocument();
    });
  });

  it('renders all forums with images and names', () => {
    render(<PortfolioSection user={mockUser} />);
    
    mockUser.forums.forEach(forum => {
      const image = screen.getByAltText(forum.name);
      expect(image).toHaveAttribute('src', forum.image);
      expect(screen.getByText(forum.name)).toBeInTheDocument();
    });
  });
});