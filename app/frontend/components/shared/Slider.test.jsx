import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import React from 'react'; 
import Slider from './Slider';

// Mock setInterval and clearInterval
beforeEach(() => {
  vi.useFakeTimers();
});

afterEach(() => {
  vi.useRealTimers();
});

describe('Slider', () => {
  const mockImages = ['image1.jpg', 'image2.jpg', 'image3.jpg'];
  const mockFrame = 'frame.png';
  const mockFadeDuration = 500;
  const mockImageInterval = 2000;

  it('renders without crashing', () => {
    render(<Slider images={mockImages} />);
    expect(screen.getByRole('img', { name: /Slider Image 1/i })).toBeInTheDocument();
  });

  it('renders the correct number of images', () => {
    render(<Slider images={mockImages} />);
    const images = screen.getAllByRole('img', { name: /Slider Image/i });
    expect(images).toHaveLength(mockImages.length);
  });

  it('renders the frame image when provided', () => {
    render(<Slider images={mockImages} frame={mockFrame} />);
    expect(screen.getByRole('img', { name: /Frame/i })).toBeInTheDocument();
    expect(screen.getByRole('img', { name: /Frame/i })).toHaveAttribute('src', mockFrame);
  });

  it('does not render the frame image when not provided', () => {
    render(<Slider images={mockImages} />);
    expect(screen.queryByRole('img', { name: /Frame/i })).not.toBeInTheDocument();
  });

  it('displays the first image initially', () => {
    render(<Slider images={mockImages} />);
    const firstImage = screen.getByRole('img', { name: /Slider Image 1/i });
    expect(firstImage).toHaveClass('opacity-100');

    const otherImages = screen.getAllByRole('img', { name: /Slider Image/i }).slice(1);
    otherImages.forEach(img => {
      expect(img).toHaveClass('opacity-0');
    });
  });

  it('applies the fade duration style', () => {
    render(<Slider images={mockImages} fadeDuration={mockFadeDuration} />);
    const images = screen.getAllByRole('img', { name: /Slider Image/i });
    images.forEach(img => {
      expect(img).toHaveStyle(`transition-duration: ${mockFadeDuration}ms`);
    });
  });

  it('does nothing if images array is empty', () => {
    const { container } = render(<Slider images={[]} imageInterval={mockImageInterval} />);
    // Advance time significantly
    vi.advanceTimersByTime(mockImageInterval * 5);
    // Expect no errors and the component to still be rendered (though likely empty of images)
    expect(container).toBeInTheDocument();
    expect(screen.queryAllByRole('img', { name: /Slider Image/i })).toHaveLength(0);
  });

  it('does nothing if images prop is not provided', () => {
    const { container } = render(<Slider imageInterval={mockImageInterval} />);
     // Advance time significantly
     vi.advanceTimersByTime(mockImageInterval * 5);
    // Expect no errors and the component to still be rendered
    expect(container).toBeInTheDocument();
    expect(screen.queryAllByRole('img', { name: /Slider Image/i })).toHaveLength(0);
  });
});
