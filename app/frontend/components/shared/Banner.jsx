import React, { useState, useEffect } from 'react';
import Slider from './Slider';

import PatternStrip from '/assets/headerstrip.png';

const FADE_DURATION_MS = 1000;
const IMAGE_INTERVAL_MS = 4000;

// Banner Component
const Banner = ({
  bgColor,
  title,
  description,
  images,
  frame,
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    if (!images || images.length === 0) {
      return;
    }
    const intervalId = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % images.length);
    }, IMAGE_INTERVAL_MS);

    // Clear the interval when the component unmounts or images prop changes
    return () => clearInterval(intervalId);
  }, [images]);

  // Determine if the layout should be reversed
  const isProjectsTitle = title === 'Projects';
  const flexOrderClass = isProjectsTitle ? 'md:flex-row-reverse' : 'md:flex-row';
  const textIsWhite = title === 'Ubuntu' ? 'text-white' : 'text-black';

  return (
    <>
      {/* Background Section */}
      <div
        className={`${bgColor} font-inter px-6 2xl:px-20 md:pb-2 pt-24 md:pt-24 relative`}
      >
        {/* Container for Flexbox Layout  */}
        <div className={`w-full flex flex-col md:flex-row justify-between items-center md:items-center relative z-10 ${flexOrderClass}`}>
          {/* Text Content Section */}
          <div className={`w-full max-w-screen-lg mr-auto md:w-2/3 ${textIsWhite} mb-0`}>
            <h2 className="text-2xl md:text-4xl lg:text-6xl font-semibold md:font-bold pb-1 md:mb-4 md:mb-6">
              {title}
            </h2>
            <p className="text-sm md:text-base lg:text-lg font-normal mb-1 md:mb-4 md:mb-8 leading-relaxed">
              {description}
            </p>
          </div>

          {/* Image Slider Section */}
          {images && images.length > 0 && (
            <div className="w-full md:w-1/3 flex flex-col justify-center items-center mb-8 md:mb-0">
              <Slider images={images} frame={frame} fadeDuration={FADE_DURATION_MS} imageInterval={IMAGE_INTERVAL_MS} />
            </div>
          )}

        </div>
      </div>
      {/* Pattern Strip */}
      <div className="w-full">
        <img src={PatternStrip} alt="Decorative pattern" className="w-full h-3 md:h-4" />
      </div>
    </>
  );
};

export default Banner;


