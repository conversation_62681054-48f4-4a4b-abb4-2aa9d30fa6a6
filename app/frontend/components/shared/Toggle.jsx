import React, { useState, useEffect } from 'react';
import { Link } from '@inertiajs/react'
import { Bookmark, ArrowRight } from 'lucide-react';
import UnbookmarkModal from './UnbookmarkModal';
import { useBookmark } from '../../custom_hooks/bookmark';
// Toggle Component
const Toggle = ({ pageData, theme, backText, backLink, title }) => {
  
    let type = "Forum"
    if (pageData.theme === "projects") {
        type = "Project"
    } else if (pageData.theme === "places") {
        type = "Place"
    };
  const { 
    bookmarked, 
    toggleBookmark, 
    showUnbookmarkModal, 
    confirmUnbookmark, 
    cancelUnbookmark 
  } = useBookmark({
    id: pageData.profile.id,
    type: type,
    initialBookmarked: pageData.bookmarkedProfileIds.includes(pageData.profile.id),
  });
  const [bookmarkHover, setBookmarkHover] = useState(false);

  // Theme-specific color
  let themeColor = "";

  if (theme === 'places') {
    themeColor = '#ffa310b4'
  } else if (theme === 'projects') {
    themeColor = '#01BDA7D9'
  } else {
    themeColor = '#d84f4f'
  }

  // Determine styles dynamically
  const getButtonStyle = () => {
    if (bookmarked || bookmarkHover) {
      return {
        backgroundColor: themeColor,
        color: 'black',
        boxShadow: '0 1px 3px rgba(0,0,0,0.2)',
      };
    }
    return {};
  };

  return (
    <div className="px-4 py-3">
      <div className="flex items-center justify-between max-w-9xl mx-auto">
        <Link href={backLink}>
          <button className="flex items-center text-black font-semibold hover:text-gray-800">
            <ArrowRight className="w-5 h-5 rotate-180 mr-2" />
            {backText}
          </button>
        </Link>

        <button
          type="button"
          onClick={(e) => toggleBookmark(e)}
          onMouseEnter={() => setBookmarkHover(true)}
          onMouseLeave={() => setBookmarkHover(false)}
          className={`p-0.5 md:p-1 rounded-md md:rounded-lg z-20 transition-all duration-200 transform hover:scale-110 cursor-pointer bg-white/80 text-gray-600`}
          style={getButtonStyle()}
          aria-label={bookmarked ? "Remove bookmark" : "Add bookmark"}
        >
          <Bookmark
            size={24}
            className={`transition-all duration-200 ${bookmarked ? 'fill-current' : ''}`}
          />
        </button>
      </div>
      {/* Unbookmark Confirmation Modal */}
      <UnbookmarkModal
        isOpen={showUnbookmarkModal}
        onClose={cancelUnbookmark}
        onConfirm={confirmUnbookmark}
        name={title}
      />
    </div>
  );
};

export default Toggle;