import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import '@testing-library/jest-dom';

// Mock Inertia router BEFORE importing the component
vi.mock('@inertiajs/react', () => ({
  router: {
    get: vi.fn(),
    post: vi.fn(),
  },
}))

// Import the mocked router and your component
import { router } from '@inertiajs/react'
import Search from './Search';

// Mocking window.scrollTo for the focusSearchInput test
const mockScrollTo = vi.fn();
global.scrollTo = mockScrollTo;

// Define mock functions outside for better control
let mockInputFocusFunc = vi.fn();
let mockGetBoundingClientRectFunc = vi.fn(() => ({
  top: 100,
  bottom: 0,
  left: 0,
  right: 0,
  width: 0,
  height: 0,
}));

// Mock refs with specific objects
const mockRefs = {
  inputRef: { current: { focus: mockInputFocusFunc } },
  searchRef: { current: { getBoundingClientRect: mockGetBoundingClientRectFunc } },
  dropdownRef: { current: { contains: vi.fn(() => false) } },
};

// Mock window.innerWidth for getCurrentStickyThreshold
Object.defineProperty(window, 'innerWidth', {
  writable: true,
  configurable: true,
  value: 1024, // Default to desktop size
});

describe('Search Component', () => {
  // Default test props
  const defaultProps = {
    color: 'maroon',
    defaultPlaceholder: 'Search for people in designers, visual artists, music, craft, technology, and more...',
    mostSearchedItems: ['designers', 'artists', 'musicians'],
    filterOptions: ['People', 'Projects', 'Forums'],
    onSearch: vi.fn(),
  };

  beforeEach(() => {
    vi.resetAllMocks();

    // Setup mocks for useRef to return our controlled objects
    // This approach gives us separate mocks for each ref based on the variable name in the component
    vi.spyOn(React, 'useRef').mockImplementation((initialValue) => {
      if (initialValue === null) {
        // Track which ref is being created based on call order within the component
        const refCallCount = React.useRef.mock.calls.length;

        // Return appropriate mock ref based on call order
        if (refCallCount === 1) return mockRefs.searchRef;
        if (refCallCount === 2) return mockRefs.inputRef;
        if (refCallCount === 3) return mockRefs.dropdownRef;

        // Fallback for any other refs
        return { current: { contains: vi.fn(() => false) } };
      }
      // For refs with initial values, return a regular object
      return { current: initialValue };
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  test('renders with default props', () => {
    render(<Search {...defaultProps} />);
    expect(screen.getByText('Filter By:')).toBeInTheDocument();
    expect(screen.getByPlaceholderText(defaultProps.defaultPlaceholder)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Submit search' })).toBeInTheDocument();
    expect(screen.getByText('Most Searched:')).toBeInTheDocument();

    // Check most searched items are rendered
    defaultProps.mostSearchedItems.forEach(item => {
      expect(screen.getByText(item)).toBeInTheDocument();
    });
  });

  test('updates search term on input change', () => {
    render(<Search {...defaultProps} />);
    const searchInput = screen.getByRole('textbox');
    fireEvent.change(searchInput, { target: { value: 'test search' } });
    expect(searchInput).toHaveValue('test search');
  });


  test('closes dropdown when clicking outside', async () => {
    render(<Search {...defaultProps} />);
    const filterButton = screen.getByRole('button', { name: /Filter By:/i });

    // Open dropdown
    fireEvent.click(filterButton);

    // Click outside the component (on the body)
    fireEvent.mouseDown(document.body);

    // Wait for the dropdown to disappear
    await waitFor(() => {
      expect(screen.queryByText(defaultProps.filterOptions[0])).not.toBeInTheDocument();
    });
  });

  test('placeholder text changes on input focus and blur', () => {
    render(<Search {...defaultProps} />);
    const searchInput = screen.getByRole('textbox');

    // Check initial placeholder
    expect(searchInput).toHaveAttribute('placeholder', defaultProps.defaultPlaceholder);

    // Focus the input
    fireEvent.focus(searchInput);

    // Check the focused placeholder
    expect(searchInput).toHaveAttribute('placeholder', 'Type something...');

    // Blur the input
    fireEvent.blur(searchInput);

    // Check that the placeholder reverted to default
    expect(searchInput).toHaveAttribute('placeholder', defaultProps.defaultPlaceholder);
  });

  test('submits search with correct parameters', () => {
    render(<Search {...defaultProps} />);
    const searchInput = screen.getByRole('textbox');
    const searchButton = screen.getByRole('button', { name: 'Submit search' });

    fireEvent.change(searchInput, { target: { value: 'test search' } });

    fireEvent.click(searchButton);

    expect(defaultProps.onSearch).toHaveBeenCalledWith('test search');

  });

  test('clicking on most searched item performs search', async () => {
  const propsWithColor = {
    ...defaultProps,
    color: 'yellow'
  };
  
  render(<Search {...propsWithColor} />);

  const searchItem = 'designers';
  fireEvent.click(screen.getByText(searchItem));

  expect(router.post).toHaveBeenCalledWith(
    "/people", 
    { query: searchItem }, 
    { preserveScroll: true }
  );
});

  test('applies correct theme classes based on color prop', () => {
    // Test with orange theme
    const { rerender } = render(<Search {...defaultProps} color="orange" />);

    // Check if the orange theme class is applied
    expect(document.querySelector('.bg-primaryorange')).toBeInTheDocument();

    // Re-render with yellow theme
    rerender(<Search {...defaultProps} color="yellow" />);

    // Check if the yellow theme class is applied
    expect(document.querySelector('.bg-primaryyellow')).toBeInTheDocument();
  });

  test('handles scrolling when needed based on position', () => {
    // Set up the mock to return a position that would require scrolling
    mockGetBoundingClientRectFunc.mockReturnValueOnce({
      top: 200,  // Position is beyond the threshold, so scrolling should be triggered
      bottom: 250,
      left: 0,
      right: 100,
      width: 100,
      height: 50
    });

    // The component's focusSearchInput method should be triggered by clicking the input
    render(<Search {...defaultProps} />);

    // Focus directly instead of click to better isolate the behavior
    // This will call component's handleInputFocusChange
    const searchInput = screen.getByRole('textbox');
    fireEvent.click(searchInput);
  });
});