// app/frontend/components/shared/UnbookmarkModal.jsx
import React from 'react';
import { X, Bookmark, AlertTriangle } from 'lucide-react';

const UnbookmarkModal = ({ isOpen, onClose, onConfirm, name }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/50 backdrop-blur-sm transition-opacity"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div 
        className="relative bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4 transform transition-all"
        onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
        }}
        >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-100">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-amber-100 rounded-full">
              <AlertTriangle className="w-5 h-5 text-amber-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900">
              Remove Bookmark
            </h3>
          </div>
          <button
            onClick={(e) => {
                e.stopPropagation(); // just in case
                e.preventDefault();
                onClose();
            }}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            aria-label="Close modal"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          <div className="flex items-start space-x-4">
            <div className="p-3 bg-yellow-100 rounded-full flex-shrink-0">
              <Bookmark className="w-6 h-6 text-yellow-600 fill-current" />
            </div>
            <div className="flex-1">
              <p className="text-gray-700 leading-relaxed">
                Are you sure you want to remove{' '}
                <span className="font-semibold text-gray-900">
                  {name || 'this profile'}
                </span>{' '}
                from your bookmarks?
              </p>
              <p className="text-sm text-gray-500 mt-2">
                You can always bookmark it again later.
              </p>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-end space-x-3 p-6 bg-gray-50 rounded-b-2xl">
          <button
            onClick={(e) => {
                e.stopPropagation();
                e.preventDefault();
                onClose();
            }}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={(e) => {
                e.stopPropagation();
                e.preventDefault();
                onConfirm();
            }}
            className="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors"
          >
            Remove Bookmark
          </button>
        </div>
      </div>
    </div>
  );
};

export default UnbookmarkModal;

