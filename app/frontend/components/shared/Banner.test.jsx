import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import React from 'react';
import Banner from './Banner'; 

// Mock the PatternStrip import
vi.mock('/assets/headerstrip.png', () => ({
  default: '/assets/headerstrip.png',
}));


// Mock the Slider component to test Banner in isolation
vi.mock('./Slider', () => ({
  default: vi.fn(({ images, frame, fadeDuration, imageInterval }) => (
    // Return a simple div or element that we can assert on
    <div data-testid="mock-slider">
      {images && images.length > 0 && (
        <span data-testid="slider-images-count">{images.length}</span>
      )}
      {frame && <span data-testid="slider-frame">{frame}</span>}
      {fadeDuration && <span data-testid="slider-fade-duration">{fadeDuration}</span>}
      {imageInterval && <span data-testid="slider-image-interval">{imageInterval}</span>}
    </div>
  )),
}));

// Mock setInterval and clearInterval for testing the Banner's internal timer
beforeEach(() => {
  vi.useFakeTimers();
});

afterEach(() => {
  vi.useRealTimers();
  vi.clearAllMocks(); // Clear mocks after each test
});

describe('Banner', () => {
  const mockProps = {
    bgColor: 'bg-blue-500',
    title: 'Test Banner Title',
    description: 'This is a test banner description.',
    images: ['imageA.jpg', 'imageB.jpg'],
    frame: 'frame.png',
  };

  // Define mockPatternStrip here if needed for other tests, but not for the mock itself
  const mockPatternStrip = '/assets/headerstrip.png';


  it('renders without crashing', () => {
    render(<Banner {...mockProps} />);
    expect(screen.getByText(mockProps.title)).toBeInTheDocument();
  });

  it('renders the title and description', () => {
    render(<Banner {...mockProps} />);
    expect(screen.getByText(mockProps.title)).toBeInTheDocument();
    expect(screen.getByText(mockProps.description)).toBeInTheDocument();
  });

  it('applies the background color class', () => {
    const { container } = render(<Banner {...mockProps} />);
    // Check if the main container div has the background color class
    expect(container.firstChild).toHaveClass(mockProps.bgColor);
  });

  it('renders the pattern strip image', () => {
    render(<Banner {...mockProps} />);
    const patternStripImg = screen.getByAltText('Decorative pattern');
    expect(patternStripImg).toBeInTheDocument();
    // Now we can use the mockPatternStrip variable defined within the describe block
    expect(patternStripImg).toHaveAttribute('src', mockPatternStrip);
  });

  it('renders the Slider component when images are provided', () => {
    render(<Banner {...mockProps} />);
    // Check if the mocked Slider component is rendered
    expect(screen.getByTestId('mock-slider')).toBeInTheDocument();
    // Check if images prop was passed to Slider
    expect(screen.getByTestId('slider-images-count')).toHaveTextContent(mockProps.images.length.toString());
    // Check if frame prop was passed to Slider
     expect(screen.getByTestId('slider-frame')).toHaveTextContent(mockProps.frame);
     // Check if fadeDuration and imageInterval props were passed to Slider
     expect(screen.getByTestId('slider-fade-duration')).toHaveTextContent('1000');
     expect(screen.getByTestId('slider-image-interval')).toHaveTextContent('4000');
  });

  it('does not render the Slider component when images array is empty', () => {
    render(<Banner {...mockProps} images={[]} />);
    expect(screen.queryByTestId('mock-slider')).not.toBeInTheDocument();
  });

   it('does not render the Slider component when images prop is not provided', () => {
    const { images, ...propsWithoutImages } = mockProps;
    render(<Banner {...propsWithoutImages} />);
    expect(screen.queryByTestId('mock-slider')).not.toBeInTheDocument();
  });


  // Test the internal timer logic in Banner
  // Increased timeout for this test
  it('updates the currentIndex at the specified interval when images are provided', async () => {
    render(<Banner {...mockProps} />);

    // Let's test that setInterval is called when images are present
    const setIntervalSpy = vi.spyOn(global, 'setInterval');
    render(<Banner {...mockProps} />);
    expect(setIntervalSpy).toHaveBeenCalledTimes(1);
    expect(setIntervalSpy).toHaveBeenCalledWith(expect.any(Function), 4000); // Check interval time
    setIntervalSpy.mockRestore();

    // This test primarily verifies that the interval is *set up* correctly in Banner.
    // The actual cycling display is tested in the Slider test file.
  }, 10000);


   it('clears the interval on unmount', () => {
    const clearIntervalSpy = vi.spyOn(global, 'clearInterval');
    const setIntervalSpy = vi.spyOn(global, 'setInterval');

    const { unmount } = render(<Banner {...mockProps} />);

    // Get the mock timer object returned by setInterval
    const mockTimer = setIntervalSpy.mock.results[0].value;

    unmount();

    expect(clearIntervalSpy).toHaveBeenCalledTimes(1);
    // Assert that clearInterval was called with the mock timer object
    expect(clearIntervalSpy).toHaveBeenCalledWith(mockTimer);

    clearIntervalSpy.mockRestore();
    setIntervalSpy.mockRestore(); // Restore the setInterval spy
  });

   it('does not set interval if images array is empty', () => {
    const setIntervalSpy = vi.spyOn(global, 'setInterval');
    render(<Banner {...mockProps} images={[]} />);
    expect(setIntervalSpy).not.toHaveBeenCalled();
    setIntervalSpy.mockRestore();
  });

   it('does not set interval if images prop is not provided', () => {
     const setIntervalSpy = vi.spyOn(global, 'setInterval');
     const { images, ...propsWithoutImages } = mockProps;
     render(<Banner {...propsWithoutImages} />);
     expect(setIntervalSpy).not.toHaveBeenCalled();
     setIntervalSpy.mockRestore();
   });
});
