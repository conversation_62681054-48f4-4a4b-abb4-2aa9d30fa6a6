import { useEffect, useState } from 'react';

// Slider Component
const Slider = ({ images, frame, fadeDuration, imageInterval }) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    if (!images || images.length <= 1 || !imageInterval) {
        return;
    }

    const intervalId = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % images.length);
    }, imageInterval);

    return () => clearInterval(intervalId);
  }, [images, imageInterval]);

  const imageArray = Array.isArray(images) ? images : [];


  return (
    <div className={`relative w-full md:max-w-md max-w-xs aspect-square mx-auto sm:ml-auto`}>
      {imageArray.map((imgSrc, index) => (
        <img
          key={index}
          src={imgSrc}
          alt={`Slider Image ${index + 1}`}
          className={`absolute inset-0 w-full h-full object-cover scale-95 transition-opacity ease-in-out ${
            index === currentIndex ? 'opacity-100' : 'opacity-0'
          }`}
          style={{ transitionDuration: `${fadeDuration}ms` }}
        />
      ))}
      {frame && (
        <img
          src={frame}
          alt="Frame"
          className="absolute inset-0 h-full z-10 pointer-events-none scale-x-100"
        />
      )}
    </div>
  );
};

export default Slider;

