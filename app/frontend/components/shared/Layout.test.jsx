import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import React from 'react';
import Layout from './Layout';

// Mock the Header and Footer components
// This allows us to test the Layout component in isolation
vi.mock('../../pages/Home/Components/Header', () => ({
  default: () => <div data-testid="mock-header">Mock <PERSON>er</div>,
}));

vi.mock('../../pages/Home/Components/Footer', () => ({
  default: () => <div data-testid="mock-footer">Mock Footer</div>,
}));

describe('Layout', () => {
  it('renders Header, children, and Footer', () => {
    const testChildren = <div data-testid="test-children">Test Children Content</div>;

    render(<Layout>{testChildren}</Layout>);

    // Check if the mocked Header is rendered
    expect(screen.getByTestId('mock-header')).toBeInTheDocument();

    // Check if the children content is rendered
    expect(screen.getByTestId('test-children')).toBeInTheDocument();

    // Check if the mocked Footer is rendered
    expect(screen.getByTestId('mock-footer')).toBeInTheDocument();
  });

  it('renders correctly with different children content', () => {
    const anotherChildren = <p data-testid="another-children">Another Content</p>;

    render(<Layout>{anotherChildren}</Layout>);

    expect(screen.getByTestId('mock-header')).toBeInTheDocument();
    expect(screen.getByTestId('another-children')).toBeInTheDocument();
    expect(screen.getByRole('article')).toContainElement(screen.getByTestId('another-children'));
    expect(screen.getByTestId('mock-footer')).toBeInTheDocument();
  });

   it('renders correctly with no children', () => {
    render(<Layout></Layout>);

    expect(screen.getByTestId('mock-header')).toBeInTheDocument();
    // Check that the article element is still present even with no children
    expect(screen.getByRole('article')).toBeInTheDocument();
    // Check that there are no children elements inside the article
    expect(screen.getByRole('article')).toBeEmptyDOMElement();
    expect(screen.getByTestId('mock-footer')).toBeInTheDocument();
  });
});
