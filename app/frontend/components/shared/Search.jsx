import React, { useState, useEffect, useRef } from 'react';
import { Search as SearchIcon, ChevronDown } from 'lucide-react';
import { router } from '@inertiajs/react';

// Theme configuration for different color variants
const themeClasses = {
  maroon: {
    mainBg: 'bg-primarymaroon',
    formBg: 'bg-bordermaroon',
    hoverBg: 'hover:bg-primarymaroon',
  },
  orange: {
    mainBg: 'bg-primaryorange',
    formBg: 'bg-borderorange',
    hoverBg: 'hover:bg-primaryorange',
  },
  yellow: {
    mainBg: 'bg-primaryyellow',
    formBg: 'bg-borderyellow',
    hoverBg: 'hover:bg-primaryyellow',
  },
  green: {
    mainBg: 'bg-primarygreen',
    formBg: 'bg-bordergreen',
    hoverBg: 'hover:bg-primarygreen',
  },
};

function Search({
  color = 'maroon',
  defaultPlaceholder = 'Search...',
  mostSearchedItems = [],
  filterOptions = [],
  onSearch = (query) => {
    // Default search routing based on color theme
    switch (color) {
      case "yellow": //Searching the peoples model
        if (!query || query.trim() === '') {
          router.get("/people");
          return;
        }
        router.post("/people", { query }, { preserveScroll: true });
        break;
      case "green": //Searching the projects model
        if (!query || query.trim() === '') {
          router.get("/projects", {}, { preserveScroll: true });
          return;
        }
        router.post("/projects", { query }, { preserveScroll: true });
        break;
      case "orange": //Searching the places model
        if (!query || query.trim() === '') {
          router.get("/places", {}, { preserveScroll: true });
          return;
        }
        router.post("/places", { query }, { preserveScroll: true });
        break;

      case "maroon" : //Searching the ubunutu model
        if(!query || query.trim() === '') {
          router.get("/forums", {}, {preserveScroll: true});
          return;
        }
        router.post("/ubuntu", {query, filter}, {preserveScroll: true});
        break;
    }
  },
}) {
  // Centralized search state management
  const [searchState, setSearchState] = useState({
    searchTerm: '',
    isDropdownOpen: false,
    selectedFilterType: 'Filter By:',
    isInputFocused: false,
    showDetailedFilters: false,
    filterType: '',
    filterTerm: '',
  });

  // UI Constants
  const STICKY_PLACEHOLDER = "Type something...";
  const DEFAULT_STICKY_THRESHOLD = 64;
  const MD_STICKY_THRESHOLD = 96;
  const MD_BREAKPOINT = 768;

  // DOM element refs
  const searchRef = useRef(null);
  const inputRef = useRef(null);
  const dropdownRef = useRef(null);
  const filtersRef = useRef(null);

  // Get current theme classes
  const currentTheme = themeClasses[color] || themeClasses.maroon;
  const textIsWhite = color === 'maroon' ? 'text-white' : 'text-black';

  // Convert filter type to API key format
  const getFilterKey = (filterType) => {
    return filterType.toLowerCase().replace(/\s+/g, '_').replace('by_', '');
  };

  // Handle clicks outside dropdowns to close them
  useEffect(() => {
    const handleOutsideClick = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setSearchState(prev => ({ ...prev, isDropdownOpen: false }));
      }
      if (filtersRef.current && !filtersRef.current.contains(event.target)) {
        setSearchState(prev => ({ ...prev, showDetailedFilters: false }));
      }
    };

    document.addEventListener('mousedown', handleOutsideClick);
    return () => document.removeEventListener('mousedown', handleOutsideClick);
  }, []);

  // Get responsive sticky threshold
  const getCurrentStickyThreshold = () => {
    if (typeof window !== 'undefined') {
      return window.innerWidth < MD_BREAKPOINT ? DEFAULT_STICKY_THRESHOLD : MD_STICKY_THRESHOLD;
    }
    return MD_STICKY_THRESHOLD;
  };

  // Focus search input and scroll to it if needed
  const focusSearchInput = () => {
    if (inputRef.current) {
      const currentStickyThreshold = getCurrentStickyThreshold();

      if (searchRef.current) {
        const searchBarPosition = searchRef.current.getBoundingClientRect().top;

        if (searchBarPosition > currentStickyThreshold) {
          const targetScrollY = searchRef.current.getBoundingClientRect().top +
            window.scrollY - currentStickyThreshold;

          window.scrollTo({
            top: targetScrollY,
            behavior: 'smooth'
          });
        }
      }
      // Only focus if dropdown is closed
      if (!searchState.isDropdownOpen) {
        inputRef.current.focus();
      }
    }
  };

  // Handle search input changes and clear behavior
  const handleSearchChange = (event) => {
    const value = event.target.value;

    // Reset to default route when search is cleared
    if (value === "" && color === "yellow") {
      router.get("/people", { filters: searchState.activeFilter }, { preserveScroll: true });
    }
    if (value === "" && color === "green") {
      router.get("/projects", { filters: searchState.activeFilter }, { preserveScroll: true });
    }
    if (value === "" && color === "orange") {
      router.get("/places", { filters: searchState.activeFilter }, { preserveScroll: true });
    }
    if (event.target.value === "" && color === "maroon"){
      router.get("/forums", {}, {preserveScroll: true})
    }

    setSearchState(prev => ({ ...prev, searchTerm: value }));
  };

  // Handle form submission
  const handleSearchSubmit = (event) => {
    event.preventDefault();
    onSearch(searchState.searchTerm);
    focusSearchInput();
  };

  // Toggle filter dropdown visibility
  const toggleDropdown = () => {
    setSearchState(prev => ({
      ...prev,
      isDropdownOpen: !prev.isDropdownOpen,
      showDetailedFilters: false
    }));
  };

  // Handle filter type selection
  const handleFilterTypeSelect = (filterType) => {
    if (filterType === 'Filter By:') {
      // Clear filter selection
      setSearchState(prev => ({
        ...prev,
        selectedFilterType: filterType,
        isDropdownOpen: false,
        showDetailedFilters: false
      }));
      if (color === "yellow") {
        router.get("/people", {}, { preserveScroll: true });
      } else if (color === "green") {
        router.get("/projects", {}, { preserveScroll: true });
      } else if (color === "orange") {
        router.get("/places", {}, { preserveScroll: true });
      }
    } else {
      // Show detailed filter options
      setSearchState(prev => ({
        ...prev,
        selectedFilterType: filterType,
        isDropdownOpen: false,
        showDetailedFilters: true
      }));
    }
    focusSearchInput();
  };

  // Handle detailed filter selection
  const handleDetailedFilterToggle = (filterType, value) => {
    handleFilterSubmit(filterType, value);
  };

  // Submit filter to backend
  const handleFilterSubmit = (filterType, value) => {
    setSearchState(prev => ({
      ...prev,
      filterType,
      filterTerm: value,
      showDetailedFilters: false,
    }));

    // Route to appropriate endpoint based on color
    if (color === "yellow") {
      router.post("/people", { filter: filterType, query: value }, { preserveScroll: true });
    } else if (color === "green") {
      router.post("/projects", { filter: filterType, query: value }, { preserveScroll: true });
    } else if (color === "orange") {
      router.post("/places", { filter: filterType, query: value }, { preserveScroll: true });
    }
  };

  // Handle input focus state changes
  const handleInputFocusChange = (isFocused) => {
    if (isFocused) {
      setSearchState(prev => ({
        ...prev,
        isInputFocused: isFocused,
        selectedFilterType: 'Filter By:',
        isDropdownOpen: false,
        showDetailedFilters: false
      }));
    } else {
      setSearchState(prev => ({ ...prev, isInputFocused: false }));
    }
  };

  const searchTerm = searchState.searchTerm

  // Debounced search - triggers search after user stops typing
  useEffect(() => {
    if (searchTerm.trim() === "") return;

    const delayDebounce = setTimeout(() => {
      if (color === "yellow") {
        router.post("/people", { query: searchTerm }, { preserveScroll: true });
      }
      if (color === "green") {
        router.post("/projects", { query: searchTerm }, { preserveScroll: true });
      }
      if (color === "orange") {
        router.post("/places", { query: searchTerm }, { preserveScroll: true });
      }
      if (color === "maroon" && searchTerm.trim() !== "") {
        router.post("/ubuntu", { query: searchTerm }, { preserveScroll: true });
      }
    }, 500);

    return () => clearTimeout(delayDebounce);
  }, [searchTerm]);

  // Handle most searched item clicks
  const handleMostSearched = (item) => {
    if (color === "yellow") {
      router.post("/people", { query: item }, { preserveScroll: true });
    } else if (color === "green") {
      router.post("/projects", { query: item }, { preserveScroll: true });
    } else if (color === "orange") {
      router.post("/places", { query: item }, { preserveScroll: true });
      } else if(color === "maroon") {
        router.post("/ubuntu", {query: item}, {preserveScroll: true})
    }
  };

  // Get available filter types and current options
  const availableFilterTypes = Object.keys(filterOptions);
  const currentFilterOptions = searchState.selectedFilterType !== 'Filter By:'
    ? filterOptions[searchState.selectedFilterType] || []
    : [];

  return (
    <div
      ref={searchRef}
      className={`w-full px-6 md:px-8 2xl:px-20 py-6 ${currentTheme.mainBg} shadow-sm font-inter sticky top-16 md:top-24 z-10 transition-all duration-300 ease-in-out`}
    >
      <form
        onSubmit={handleSearchSubmit}
        className={`w-full flex items-center ${currentTheme.formBg} rounded-full shadow-md p-1 md:p-2 relative`}
        data-testid="search-form"
      >
        {/* Filter Dropdown */}
        <div
          ref={dropdownRef}
          className="relative w-1/3 md:w-1/6 bg-white rounded-l-full p-2"
        >
          <button
            type="button"
            onClick={toggleDropdown}
            aria-haspopup="listbox"
            aria-expanded={searchState.isDropdownOpen}
            className="w-full justify-center flex items-center text-gray-700 px-3 sm:px-4 py-2 focus:outline-none whitespace-nowrap text-xs sm:text-base relative"
          >
            {searchState.selectedFilterType}
            <ChevronDown
              size={20}
              className={`ml-1 sm:ml-2 transition-transform duration-200 ${searchState.isDropdownOpen ? 'rotate-180' : ''}`}
              aria-hidden="true"
            />
          </button>

          {/* Dropdown Menu */}
          {searchState.isDropdownOpen && (
            <div
              className="w-11/12 absolute justify-center mt-2 bg-white rounded-md shadow-lg z-20"
              role="listbox"
              aria-labelledby="filter-dropdown"
            >
              {/* Clear filter option */}
              {searchState.selectedFilterType !== 'Filter By:' && (
                <button
                  type="button"
                  onClick={() => handleFilterTypeSelect('Filter By:')}
                  className={`block w-full text-sm md:text-base text-left px-4 py-2 text-sm text-center text-gray-700 ${currentTheme.hoverBg} font-semibold`}
                  role="option"
                >
                  Clear
                </button>
              )}
              {/* Filter type options */}
              {availableFilterTypes.map((filterType) => (
                <button
                  key={filterType}
                  type="button"
                  onClick={() => handleFilterTypeSelect(filterType)}
                  className={`block w-full text-xs  md:text-base text-left px-4 py-2 text-sm text-center text-gray-700 ${currentTheme.hoverBg}`}
                  role="option"
                >
                  {filterType}
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Search Input */}
        <div className="flex-grow flex items-center relative rounded-r-full ml-1 md:ml-2">
          <input
            ref={inputRef}
            type="text"
            value={searchState.searchTerm}
            onChange={handleSearchChange}
            onClick={focusSearchInput}
            onFocus={() => handleInputFocusChange(true)}
            onBlur={() => handleInputFocusChange(false)}
            placeholder={searchState.isInputFocused ? STICKY_PLACEHOLDER : defaultPlaceholder}
            className="w-full p-4 text-sm text-gray-700 focus:outline-none focus:ring-0 focus:shadow-none bg-white border-none text-sm sm:text-base placeholder-gray-500 pr-12 rounded-r-full truncate"
            aria-label="Search input"
          />

          {/* Search Button */}
          <button
            type="submit"
            className={`absolute right-2 top-1/2 transform -translate-y-1/2 ${currentTheme.mainBg} hover:bg-black text-black hover:text-white p-2 sm:p-3 rounded-full focus:outline-none transition-colors duration-150`}
            aria-label="Submit search"
          >
            <SearchIcon size={20} className="sm:w-5 sm:h-5" />
          </button>
        </div>
      </form>

      {/* Detailed Filters Panel */}
      {searchState.showDetailedFilters && currentFilterOptions.length > 0 && (
        <div
          ref={filtersRef}
          className="mt-4 bg-white rounded-lg shadow-lg p-4 max-h-64 overflow-y-auto"
        >
          <div className="grid grid-cols-2 md:grid-cols-6 lg:grid-cols-8 gap-2">
            {currentFilterOptions.map((option) => {
              const filterKey = getFilterKey(searchState.selectedFilterType);
              const isActive = searchState.searchTerm == option;

              return (
                <button
                  key={option}
                  type="button"
                  onClick={() => handleDetailedFilterToggle(searchState.selectedFilterType, option)}
                  className={`px-2 py-2 rounded-full text-xs md:text-sm border transition-colors duration-200 ${isActive
                    ? `${currentTheme.mainBg} border-transparent`
                    : 'bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200'
                    }`}
                >
                  {option}
                </button>
              );
            })}
          </div>
        </div>
      )}

      {/* Most Searched Section */}
      {mostSearchedItems.length > 0 && (
        <div className="mt-4 sm:mt-6 text-center">
          <p className={`text-sm text-gray-700 ${textIsWhite}`}>
            <span className="font-semibold">Most Searched:</span>
            {mostSearchedItems.map((item, index) => (
              <React.Fragment key={item}>
                <button
                  type="button"
                  onClick={(e) => {
                    e.preventDefault();
                    setSearchState(prev => ({ ...prev, searchTerm: item }));
                    handleMostSearched(item);
                  }}
                  className="hover:underline ml-1 text-sm bg-transparent border-none cursor-pointer p-0"
                >
                  {item}
                </button>
                {index < mostSearchedItems.length - 1 && <span>, </span>}
              </React.Fragment>
            ))}
          </p>
        </div>
      )}
    </div>
  );
}

export default Search;
