// app/frontend/custom_hooks/bookmark.jsx
import { useState } from 'react';
import { toast } from 'react-hot-toast';

export const useBookmark = ({ id, type, initialBookmarked = false }) => {
  const [bookmarked, setBookmarked] = useState(initialBookmarked);
  const [showUnbookmarkModal, setShowUnbookmarkModal] = useState(false);

  const toggleBookmark = (e) => {
    e.preventDefault();
    e.stopPropagation();

    if (bookmarked) {
      setShowUnbookmarkModal(true);
    } else {
      callApi('POST');
    }
  };

  const confirmUnbookmark = () => {
    setShowUnbookmarkModal(false);
    callApi('DELETE');
  };
  const cancelUnbookmark = () => setShowUnbookmarkModal(false);

  const callApi = async (method) => {
    try {
      const res = await fetch('/bookmarks', {
        method,
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content,
        },
        body: JSON.stringify({
          bookmarkable_type: type,
          bookmarkable_id:   id,
        }),
      });

      if (res.redirected) {
        window.location.href = res.url;
        return;
      }
      if (!res.ok) throw new Error();
      setBookmarked(method === 'POST');
      toast.success(method === 'POST' ? 'Bookmarked!' : 'Bookmark removed.');
    } catch {
      toast.error('Something went wrong.');
    }
  };

  return {
    bookmarked,
    showUnbookmarkModal,
    toggleBookmark,
    confirmUnbookmark,
    cancelUnbookmark,
  };
};
