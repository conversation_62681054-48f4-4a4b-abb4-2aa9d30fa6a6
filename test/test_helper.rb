require "simplecov"
SimpleCov.start "rails" do
  add_filter %w[
  app/views
  lib/rails
  lib/templates
  bin
  coverage
  log
  test
  vendor
  node_modules
  db
  doc
  public
  storage
  tmp
]
  add_group "Model Concerns", "app/model/concerns"
  add_group "Mailers", "app/mailers"
  add_group "Helpers", "app/helpers"
  add_group "Models", "app/model"
  add_group "Controllers", "app/controllers"
  add_group "Policies", "app/policies"
end

ENV["RAILS_ENV"] ||= "test"
require_relative "../config/environment"
require "rails/test_help"


module ActiveSupport
  class TestCase
    # Run tests in parallel with specified workers
    parallelize(workers: :number_of_processors)

    # Setup all fixtures in test/fixtures/*.yml for all tests in alphabetical order.
    # fixtures :all

    # Add more helper methods to be used by all tests here...
    def before_setup
      Bullet.start_request
      super
    end

    def after_teardown
      super
      Bullet.perform_out_of_channel_notifications if Bullet.notification?
      Bullet.end_request
    end
  end
end
