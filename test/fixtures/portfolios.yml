# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

# This model initially had no columns defined. If you add columns to the
# model remove the "{}" from the fixture names and add the columns immediately
# below each fixture, per the syntax in the comments below
#
portfolio_one:
  user: john
  project: web_app
  created_at: <%= 2.days.ago %>
  updated_at: <%= 1.day.ago %>

portfolio_two:
  user: jane
  project: mobile_app
  created_at: <%= 4.days.ago %>
  updated_at: <%= 2.days.ago %>

portfolio_without_user:
  project: api_service
  created_at: <%= 3.days.ago %>
  updated_at: <%= 1.day.ago %>

portfolio_without_project:
  user: admin
  created_at: <%= 5.days.ago %>
  updated_at: <%= 3.days.ago %>

portfolio_empty:
  created_at: <%= 1.week.ago %>
  updated_at: <%= 4.days.ago %>
