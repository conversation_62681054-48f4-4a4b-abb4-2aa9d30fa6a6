# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

# This model initially had no columns defined. If you add columns to the
# model remove the "{}" from the fixture names and add the columns immediately
# below each fixture, per the syntax in the comments below
#
john:
  first_name: "<PERSON>"
  last_name: "<PERSON><PERSON>"
  email: "<EMAIL>"
  encrypted_password: "password"



jane:
  first_name: "<PERSON>"
  last_name: "<PERSON><PERSON>"
  email: "<EMAIL>"
  encrypted_password: "password"

admin:
  first_name: "Admin"
  last_name: User"
  email: "<EMAIL>"
  encrypted_password: "password"

project_collaborator:
  first_name: "<PERSON>"
  last_name: Collaborator"
  email: "<EMAIL>"
  encrypted_password: "password"

commenter:
  first_name: "Project"
  last_name: Collaborator"
  email: "<EMAIL>"
  encrypted_password: "password"
