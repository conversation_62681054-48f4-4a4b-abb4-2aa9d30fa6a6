# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

# This model initially had no columns defined. If you add columns to the
# model remove the "{}" from the fixture names and add the columns immediately
# below each fixture, per the syntax in the comments below
#
web_app:
  name: "E-commerce Website"
  owner: john
  description: "A full-stack e-commerce application"
  created_at: <%= 3.days.ago %>
  updated_at: <%= 1.day.ago %>

mobile_app:
  name: "Task Manager Mobile App"
  owner: john
  description: "iOS and Android task management application"
  created_at: <%= 7.days.ago %>
  updated_at: <%= 2.days.ago %>

api_service:
  name: "REST API Service"
  owner: john
  description: "Microservice for handling user authentication"
  created_at: <%= 1.week.ago %>
  updated_at: <%= 3.days.ago %>
