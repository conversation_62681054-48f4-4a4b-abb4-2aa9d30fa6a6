require "test_helper"

class UserTest < ActiveSupport::TestCase
  # Test fixtures or factory setup
  fixtures :users
  def setup
    @user = users(:john) # using fixture defined below
  end

  test "the truth" do
  assert true
end



  # test "user is not confirmed on creation" do
  #   user = User.create(email: "<EMAIL>", password: "password123", password_confirmation: "password123")
  #   assert_not user.confirmed?

  # end

  # test "user can be confirmed manually" do
  #   user = User.create(email: "<EMAIL>", password: "password123", password_confirmation: "password123")
  #   user.confirm
  #   assert user.confirmed?
  # end
end
