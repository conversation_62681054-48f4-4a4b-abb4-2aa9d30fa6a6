require "test_helper"

class ForumTest < ActiveSupport::TestCase
  fixtures :users
  def setup
    @user = users(:john)
    @commenter = users(:commenter)
    @forum = Forum.new(
      title: "Test Forum",
      description: "This is a test forum for discussion",
      owner: @user
    )
  end

  # Basic validations
  test "should be valid with valid attributes" do
    assert @forum.valid?
  end

  test "should require title" do
    @forum.title = nil
    assert_not @forum.valid?
    assert_includes @forum.errors[:title], "can't be blank"
  end

  test "should require description" do
    @forum.description = nil
    assert_not @forum.valid?
    assert_includes @forum.errors[:description], "can't be blank"
  end

  test "should not be valid with empty title" do
    @forum.title = ""
    assert_not @forum.valid?
    assert_includes @forum.errors[:title], "can't be blank"
  end

  test "should not be valid with empty description" do
    @forum.description = ""
    assert_not @forum.valid?
    assert_includes @forum.errors[:description], "can't be blank"
  end

  # Association tests
  test "should belong to owner" do
    assert_respond_to @forum, :owner
    assert_kind_of User, @forum.owner
  end

  test "should require owner" do
    @forum.owner = nil
    assert_not @forum.valid?
    assert_includes @forum.errors[:owner], "must exist"
  end

  test "should have many comments" do
    assert_respond_to @forum, :comments
    @forum.save!

    comment = @forum.comments.build(
      comment: "Test comment on forum",
      commenter: @commenter
    )

    assert_kind_of Comment, comment
    assert_equal @forum, comment.commentable
  end

  test "should have many posts" do
    assert_respond_to @forum, :posts
    @forum.save!

    post = @forum.posts.build(
      title: "Test Post",
      content: "This is a test post content",
      owner: @user
    )

    assert_kind_of Post, post
  end

  test "should have many bookmarks" do
    assert_respond_to @forum, :bookmarks
    @forum.save!

    bookmark = @forum.bookmarks.build(user: @user)
    assert_kind_of Bookmark, bookmark
    assert_equal @forum, bookmark.bookmarkable
  end

  test "should destroy bookmarks when forum is deleted" do
    @forum.save!
    bookmark = @forum.bookmarks.create!(user: @user)

    assert_difference "Bookmark.count", -1 do
      @forum.destroy
    end
  end

  # Active Storage tests
  test "should have one avatar attached" do
    assert_respond_to @forum, :avatar
    @forum.save!

    # Create a test image blob
    avatar_blob = create_image_blob(filename: "test_avatar.jpg", content_type: "image/jpeg")
    @forum.avatar.attach(avatar_blob)

    assert @forum.avatar.attached?
    assert_equal "image/jpeg", @forum.avatar.content_type
    assert_equal "test_avatar.jpg", @forum.avatar.filename.to_s
  end

  test "should have many images attached" do
    assert_respond_to @forum, :images
    @forum.save!

    image1_blob = create_image_blob(filename: "test_image1.jpg", content_type: "image/jpeg")
    image2_blob = create_image_blob(filename: "test_image2.png", content_type: "image/png")

    @forum.images.attach([ image1_blob, image2_blob ])

    assert_equal 2, @forum.images.count
    assert @forum.images.attached?
  end

  test "should handle single image attachment" do
    @forum.save!

    image_blob = create_image_blob(filename: "test_image1.jpg", content_type: "image/jpeg")
    @forum.images.attach(image_blob)

    assert_equal 1, @forum.images.count
    assert @forum.images.attached?
  end

  test "should purge avatar" do
    @forum.save!
    avatar_blob = create_image_blob(filename: "test_avatar.jpg", content_type: "image/jpeg")
    @forum.avatar.attach(avatar_blob)

    assert @forum.avatar.attached?

    @forum.avatar.purge
    assert_not @forum.avatar.attached?
  end

  # acts_as_taggable_on tests for categories
  test "should respond to taggable methods for categories" do
    assert_respond_to @forum, :category_list
    assert_respond_to @forum, :category_list=
    assert_respond_to @forum, :categories
  end

  test "should accept category list as string" do
    @forum.category_list = "technology, programming, web-development"
    @forum.save!

    assert_equal 3, @forum.categories.count
    assert_includes @forum.category_list, "technology"
    assert_includes @forum.category_list, "programming"
    assert_includes @forum.category_list, "web-development"
  end

  test "should accept category list as array" do
    @forum.category_list = [ "technology", "programming", "web-development" ]
    @forum.save!

    assert_equal 3, @forum.categories.count
    assert_includes @forum.category_list, "technology"
  end

  test "should handle empty category list" do
    @forum.category_list = ""
    @forum.save!

    assert_equal 0, @forum.categories.count
    assert_empty @forum.category_list
  end

  test "should remove duplicate categories" do
    @forum.category_list = "technology, programming, technology, web-development"
    @forum.save!

    assert_equal 3, @forum.categories.count
    category_names = @forum.categories.pluck(:name)
    assert_equal 1, category_names.count("technology")
  end

  test "should find forums by category" do
    @forum.category_list = "technology, programming"
    @forum.save!

    other_forum = Forum.create!(
      title: "Science Forum",
      description: "Discussion about science",
      owner: users(:john),
      category_list: "science, biology"
    )

    tech_forums = Forum.tagged_with("technology", on: :categories)
    assert_includes tech_forums, @forum
    assert_not_includes tech_forums, other_forum
  end

  # PgSearch tests
  test "should respond to search methods" do
    assert_respond_to Forum, :search_all_fields
  end

  test "should search by title" do
    @forum.title = "Ruby Programming Forum"
    @forum.save!

    other_forum = Forum.create!(
      title: "Python Discussion",
      description: "Talk about Python",
      owner: users(:jane)
    )

    results = Forum.search_all_fields("Ruby")
    assert_includes results, @forum
    assert_not_includes results, other_forum
  end

  test "should search by description" do
    @forum.description = "Discuss Ruby on Rails development"
    @forum.save!

    other_forum = Forum.create!(
      title: "General Chat",
      description: "General discussion about anything",
      owner: users(:john)
    )

    results = Forum.search_all_fields("Rails")
    assert_includes results, @forum
    assert_not_includes results, other_forum
  end

  test "should search with partial matches" do
    @forum.title = "JavaScript Programming"
    @forum.save!

    # # Should find with partial word
    # results = Forum.search_all_fields("Script")
    # assert_includes results, @forum

    # Should find with beginning of word
    results = Forum.search_all_fields("Java")
    assert_includes results, @forum
  end

  test "should search with any word matching" do
    @forum.title = "Web Development Forum"
    @forum.description = "Discuss HTML, CSS, and JavaScript"
    @forum.save!

    # Should match any of the search terms
    results = Forum.search_all_fields("HTML Python")
    assert_includes results, @forum
  end

  test "should handle case insensitive search" do
    @forum.title = "Ruby Programming"
    @forum.save!

    results = Forum.search_all_fields("ruby")
    assert_includes results, @forum

    results = Forum.search_all_fields("RUBY")
    assert_includes results, @forum
  end

  test "should return empty results for non-matching search" do
    @forum.save!

    results = Forum.search_all_fields("NonExistentTopic")
    assert_empty results
  end

  # Note: The members association through forum_membership seems incomplete
  # Adding placeholder test - you may need to adjust based on your actual implementation
  test "should have members association" do
    assert_respond_to @forum, :members
    # This test might need adjustment based on your actual forum_membership model
  end

  # Integration tests
  test "should maintain all associations and features together" do
    @forum.category_list = "technology, programming"
    @forum.save!

    # Add avatar and images
    avatar_blob = create_image_blob(filename: "test_avatar.jpg", content_type: "image/jpeg")
    @forum.avatar.attach(avatar_blob)

    image1_blob = create_image_blob(filename: "test_image1.jpg", content_type: "image/jpeg")
    image2_blob = create_image_blob(filename: "test_image2.png", content_type: "image/png")
    @forum.images.attach([ image1_blob, image2_blob ])

    # Add comment and bookmark
    comment = @forum.comments.create!(comment: "Great forum!", commenter: users(:commenter))
    bookmark = @forum.bookmarks.create!(user: users(:jane))

    # Verify everything is maintained
    assert_equal @user, @forum.owner
    assert_equal 2, @forum.categories.count
    assert @forum.avatar.attached?
    assert_equal 2, @forum.images.count
    assert_equal 1, @forum.comments.count
    assert_equal 1, @forum.bookmarks.count

    # Should be searchable
    results = Forum.search_all_fields(@forum.title)
    assert_includes results, @forum
  end

  test "should handle special characters in categories" do
    @forum.category_list = "c++, .net, web-development, node.js"
    @forum.save!

    assert_equal 4, @forum.categories.count
    assert_includes @forum.category_list, "c++"
    assert_includes @forum.category_list, ".net"
  end

  # Edge cases
  test "should handle very long titles and descriptions in search" do
    long_title = "A" * 500  # Very long title
    long_description = "B" * 1000  # Very long description

    @forum.title = long_title
    @forum.description = long_description
    @forum.save!

    results = Forum.search_all_fields("A")
    assert_includes results, @forum
  end

  test "should handle nil search query gracefully" do
    @forum.save!

    # This should not raise an error
    assert_nothing_raised do
      Forum.search_all_fields(nil)
    end
  end

  test "should handle empty search query" do
    @forum.save!

    results = Forum.search_all_fields("")
    # Should return all forums or empty result depending on pg_search configuration
    assert_kind_of ActiveRecord::Relation, results
  end

  private

  # Helper method to create test blobs for Active Storage
  def create_image_blob(filename:, content_type:)
    ActiveStorage::Blob.create_and_upload!(
      io: StringIO.new("fake image data for #{filename}"),
      filename: filename,
      content_type: content_type
    )
  end
end
