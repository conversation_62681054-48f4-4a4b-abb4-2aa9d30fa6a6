require "test_helper"

class LocationTest < ActiveSupport::TestCase
  # test "should not save location without address" do
  #   location = Location.new
  #   assert_not location.save, "Saved the location without an address"
  # end

  # test "should save valid location" do
  #   locatable = User.create!(name: "<PERSON>",email: "user_#{SecureRandom.hex(4)}@example.com")
  #     location = Location.new(
  #     address: "123 Main St",
  #     latitude: 1.234567,
  #     longitude: 36.123456,
  #     locatable: locatable
  #   )
  #   assert location.save
  # end
end
