require "test_helper"

class ProjectJoinRequestTest < ActiveSupport::TestCase
  fixtures :users, :projects
  def setup
    @project = projects(:web_app) # Assuming you have fixtures
    @user = users(:project_collaborator)
    @approver = users(:john)
    @project_join_request = ProjectJoinRequest.new(
      project: @project,
      user: @user,
      status: 0,
      approver: @approver
    )
  end

  # Association tests
  test "should belong to project" do
    assert_respond_to @project_join_request, :project
  end

  test "should belong to user" do
    assert_respond_to @project_join_request, :user
  end

  test "should belong to approver" do
    assert_respond_to @project_join_request, :approver
  end

  test "project association should be required" do
    @project_join_request.project = nil
    assert_not @project_join_request.valid?
    assert_includes @project_join_request.errors[:project], "must exist"
  end

  test "user association should be required" do
    @project_join_request.user = nil
    assert_not @project_join_request.valid?
    assert_includes @project_join_request.errors[:user], "must exist"
  end

  test "approver association should be optional" do
    @project_join_request.approver = nil
    assert @project_join_request.valid?
  end

  # Enum tests
  test "should have pending status by default" do
    request = ProjectJoinRequest.new(project: @project, user: @user)
    assert_equal "pending", request.status
  end

  test "should define status enum values" do
    assert_equal({ "pending" => 0, "approved" => 1, "denied" => 2 }, ProjectJoinRequest.statuses)
  end

  test "should allow setting status to pending" do
    @project_join_request.status = :pending
    assert_equal "pending", @project_join_request.status
    assert @project_join_request.pending?
  end

  test "should allow setting status to approved" do
    @project_join_request.status = :approved
    assert_equal "approved", @project_join_request.status
    assert @project_join_request.approved?
  end

  test "should allow setting status to denied" do
    @project_join_request.status = :denied
    assert_equal "denied", @project_join_request.status
    assert @project_join_request.denied?
  end

  test "should respond to status query methods" do
    assert_respond_to @project_join_request, :pending?
    assert_respond_to @project_join_request, :approved?
    assert_respond_to @project_join_request, :denied?
  end

  # Scopes (if you want to test enum scopes)
  test "should have pending scope" do
    assert_respond_to ProjectJoinRequest, :pending
  end

  test "should have approved scope" do
    assert_respond_to ProjectJoinRequest, :approved
  end

  test "should have denied scope" do
    assert_respond_to ProjectJoinRequest, :denied
  end

  # Validation and creation tests
  test "should be valid with valid attributes" do
    assert @project_join_request.valid?
  end

  test "should save with valid attributes" do
    assert @project_join_request.save
  end

  test "should create with factory" do
    request = ProjectJoinRequest.create!(
      project: @project,
      user: @user,
      status: :pending
    )
    assert_not_nil request.id
    assert_equal @project, request.project
    assert_equal @user, request.user
    assert request.pending?
  end

  # Business logic tests (examples)
  test "should allow approver to be set when approved" do
    @project_join_request.status = :approved
    @project_join_request.approver = @approver
    assert @project_join_request.valid?
    assert_equal @approver, @project_join_request.approver
  end

  test "should allow approver to be set when denied" do
    @project_join_request.status = :denied
    @project_join_request.approver = @approver
    assert @project_join_request.valid?
    assert_equal @approver, @project_join_request.approver
  end

  # Edge cases
  test "should handle status transitions" do
    @project_join_request.save!

    # pending -> approved
    @project_join_request.approved!
    assert @project_join_request.approved?

    # Can't go back to pending (if that's your business rule)
    @project_join_request.pending!
    assert @project_join_request.pending?
  end

  test "user and approver can be the same" do
    @project_join_request.approver = @user
    assert @project_join_request.valid?
  end

  test "should handle nil approver gracefully" do
    @project_join_request.approver = nil
    assert_nil @project_join_request.approver
    assert @project_join_request.valid?
  end
end
