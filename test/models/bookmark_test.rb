require "test_helper"

class BookmarkTest < ActiveSupport::TestCase
  setup do
    @user = User.new(
      email: "test#{SecureRandom.hex(3)}@example.com",
      password: "password",
      first_name: "Test",
      last_name: "User",
      confirmed_at: Time.current
    )
    @user.save(validate: false)

    @forum = Forum.create!(
      title: "Test Forum",
      description: "Forum description",
      owner: @user
    )

    @place = Place.create!(
      name: "Test Place",
      description: "Place description",
      owner: @user
    )

    @project = Project.create!(
      name: "Test Project",
      description: "Project description",
      owner: @user
    )

    @profile = Profile.create!(
      first_name: "<PERSON><PERSON>",
      last_name: "<PERSON><PERSON>",
      bio: "This is the bio of Punda Milia.",
      profession: "Software Engineer",
      user: @user
    )

    @forum_bookmark = Bookmark.create!(
      user: @user,
      bookmarkable: @forum
    )

    @place_bookmark = Bookmark.create!(
      user: @user,
      bookmarkable: @place
    )

    @project_bookmark = Bookmark.create!(
      user: @user,
      bookmarkable: @project
    )

    @profile_bookmark = Bookmark.create!(
      user: @user,
      bookmarkable: @profile
    )
  end

  test "should belong to user and bookmarkable" do
    assert_equal @user, @forum_bookmark.user
    assert_equal @forum, @forum_bookmark.bookmarkable

    assert_equal @user, @place_bookmark.user
    assert_equal @place, @place_bookmark.bookmarkable

    assert_equal @user, @project_bookmark.user
    assert_equal @project, @project_bookmark.bookmarkable

    assert_equal @user, @profile_bookmark.user
    assert_equal @profile, @profile_bookmark.bookmarkable
  end

  test "should create valid bookmark for forum" do
    assert @forum_bookmark.valid?
  end

  test "should create valid bookmark for place" do
    assert @place_bookmark.valid?
  end

  test "should create valid bookmark for project" do
    assert @project_bookmark.valid?
  end

  test "should create valid bookmark for profile" do
    assert @profile_bookmark.valid?
  end

  test "should prevent duplicate bookmarks for same user and bookmarkable" do
    duplicate = Bookmark.new(user: @user, bookmarkable: @forum)
    assert_not duplicate.save, "Saved a duplicate bookmark"
  end

  test "should destroy all bookmarks when user is destroyed" do
    assert_difference("Bookmark.count", -4) do
      @user.destroy
    end
  end

  test "should destroy forum bookmark when forum is destroyed" do
    assert_difference("Bookmark.count", -1) do
      @forum.destroy
    end
  end

  test "should destroy place bookmark when place is destroyed" do
    assert_difference("Bookmark.count", -1) do
      @place.destroy
    end
  end

  test "should destroy project bookmark when project is destroyed" do
    assert_difference("Bookmark.count", -1) do
      @project.destroy
    end
  end

  test "should destroy profile bookmark when profile is destroyed" do
    assert_difference("Bookmark.count", -1) do
      @profile.destroy
    end
  end
end
