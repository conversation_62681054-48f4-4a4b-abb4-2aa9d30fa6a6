require "test_helper"

class PostTest < ActiveSupport::TestCase
  fixtures :users, :forums
  def setup
    @user = users(:john)
    @forum = forums(:forum_one)
    @post = Post.new(
      title: "Test Post",
      content: "This is a test post content",
      owner: @user,
      forum: @forum
    )
  end

  # Basic validations and associations
  test "should be valid with valid attributes" do
    assert @post.valid?
  end

  test "should belong to forum" do
    assert_respond_to @post, :forum
    assert_kind_of Forum, @post.forum
  end

  test "should belong to owner" do
    assert_respond_to @post, :owner
    assert_kind_of User, @post.owner
  end

  test "should require forum" do
    @post.forum = nil
    assert_not @post.valid?
    assert_includes @post.errors[:forum], "must exist"
  end

  test "should require owner" do
    @post.owner = nil
    assert_not @post.valid?
    assert_includes @post.errors[:owner], "must exist"
  end

  # acts_as_votable tests
  test "should respond to acts_as_votable methods" do
    assert_respond_to @post, :votes_for
    # assert_respond_to @post, :votes_against
    assert_respond_to @post, :get_upvotes
    assert_respond_to @post, :get_downvotes
    assert_respond_to @post, :get_likes
    assert_respond_to @post, :get_dislikes
    assert_respond_to @post, :liked_by
    assert_respond_to @post, :disliked_by
    assert_respond_to @post, :vote_registered?
    assert_respond_to @post, :vote_by
  end

  test "should allow users to vote" do
    @post.save!
    voter = users(:jane)

    # Test upvote
    @post.liked_by voter
    assert_equal 1, @post.votes_for.count
    assert_equal 1, @post.get_likes.size
    assert @post.vote_registered?
  end

  test "should track upvotes and downvotes separately" do
    @post.save!
    upvoter = users(:commenter)
    downvoter = users(:project_collaborator)

    @post.liked_by upvoter
    @post.disliked_by downvoter

    assert_equal 1, @post.get_upvotes.size
    assert_equal 1, @post.get_downvotes.size
    assert_equal 2, @post.votes_for.count
    assert_equal 1, @post.get_dislikes.size
  end

  test "should not allow duplicate votes from same user" do
    @post.save!
    voter = users(:jane)

    @post.liked_by voter
    @post.liked_by voter # Try to vote again

    assert_equal 1, @post.votes_for.count
  end

  test "should allow user to change vote" do
    @post.save!
    voter = users(:jane)

    @post.liked_by voter
    assert_equal 1, @post.get_upvotes.size
    assert_equal 0, @post.get_downvotes.size

    @post.disliked_by voter
    assert_equal 0, @post.get_upvotes.size
    assert_equal 1, @post.get_downvotes.size
  end

  test "should calculate vote score" do
    @post.save!
    upvoter1 = users(:jane)
    upvoter2 = users(:commenter)
    downvoter = users(:project_collaborator)

    @post.liked_by upvoter1
    @post.liked_by upvoter2
    @post.disliked_by downvoter

    # Score should be upvotes - downvotes = 2 - 1 = 1
    assert_equal 1, (@post.votes_for.up.count - @post.votes_for.down.count)
  end

  # acts_as_taggable_on tests
  test "should respond to taggable methods" do
    assert_respond_to @post, :tag_list
    assert_respond_to @post, :tag_list=
    assert_respond_to @post, :tags
    assert_respond_to @post.class, :tagged_with
  end

  test "should accept tag list as string" do
    @post.tag_list = "ruby, rails, programming"
    @post.save!

    assert_equal 3, @post.tags.count
    assert_includes @post.tag_list, "ruby"
    assert_includes @post.tag_list, "rails"
    assert_includes @post.tag_list, "programming"
  end

  test "should accept tag list as array" do
    @post.tag_list = [ "ruby", "rails", "programming" ]
    @post.save!

    assert_equal 3, @post.tags.count
    assert_includes @post.tag_list, "ruby"
  end

  test "should handle empty tag list" do
    @post.tag_list = ""
    @post.save!

    assert_equal 0, @post.tags.count
    assert_empty @post.tag_list
  end

  test "should remove duplicate tags" do
    @post.tag_list = "ruby, rails, ruby, programming"
    @post.save!

    assert_equal 3, @post.tags.count
    tag_names = @post.tags.pluck(:name)
    assert_equal 1, tag_names.count("ruby")
  end

  test "should find posts by tag" do
    @post.tag_list = "ruby, rails"
    @post.save!

    other_post = Post.create!(
      title: "Another Post",
      content: "Content",
      owner: @user,
      forum: @forum,
      tag_list: "python, django"
    )

    ruby_posts = Post.tagged_with("ruby")
    assert_includes ruby_posts, @post
    assert_not_includes ruby_posts, other_post
  end

  test "should find posts with any matching tags" do
    @post.tag_list = "ruby, rails"
    @post.save!

    other_post = Post.create!(
      title: "Another Post",
      content: "Content",
      owner: @user,
      forum: @forum,
      tag_list: "ruby, python"
    )

    posts = Post.tagged_with([ "ruby", "javascript" ], any: true)
    assert_includes posts, @post
    assert_includes posts, other_post
  end

  # Comments association tests
  test "should have many comments" do
    assert_respond_to @post, :comments
    @post.save!

    comment = @post.comments.build(
      comment: "Test comment",
      commenter: @user
    )

    assert_kind_of Comment, comment
    assert_equal @post, comment.commentable
  end

  test "should destroy associated comments when post is deleted" do
    @post.save!
    comment = @post.comments.create!(
      comment: "Test comment",
      commenter: @user
    )

    assert_difference "Comment.count", -1 do
      @post.destroy
    end
  end

  # Integration tests
  test "should maintain associations after voting and tagging" do
    @post.tag_list = "ruby, rails"
    @post.save!

    voter = users(:jane)
    @post.liked_by voter

    comment = @post.comments.create!(
      comment: "Great post!",
      commenter: voter
    )

    # Verify all associations are maintained
    assert_equal @forum, @post.forum
    assert_equal @user, @post.owner
    assert_equal 1, @post.votes_for.count
    assert_equal 2, @post.tags.count
    assert_equal 1, @post.comments.count
    assert_includes @post.tag_list, "ruby"
  end

  test "should handle complex tagging scenarios" do
    @post.tag_list = "ruby, rails, web-development"
    @post.save!

    # Test tag updating
    @post.tag_list = "ruby, javascript, react"
    @post.save!

    assert_equal 3, @post.tags.count
    assert_includes @post.tag_list, "ruby"
    assert_includes @post.tag_list, "javascript"
    assert_includes @post.tag_list, "react"
    assert_not_includes @post.tag_list, "rails"
  end

  # Edge cases
  test "should handle voting by post owner" do
    @post.save!

    # Owner should be able to vote on their own post
    @post.liked_by @user
    assert_equal 1, @post.votes_for.count
  end

  test "should handle special characters in tags" do
    @post.tag_list = "c++, .net, web-development, node.js"
    @post.save!

    assert_equal 4, @post.tags.count
    assert_includes @post.tag_list, "c++"
    assert_includes @post.tag_list, ".net"
    assert_includes @post.tag_list, "web-development"
    assert_includes @post.tag_list, "node.js"
  end

  private

  # Helper method for creating test users if needed
  def create_test_user(name = "testuser")
    User.create!(
      email: "#{name}@example.com",
      password: "password123",
      name: name
    )
  end
end
