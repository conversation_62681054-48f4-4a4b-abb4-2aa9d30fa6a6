require "test_helper"

class PortfolioTest < ActiveSupport::TestCase
  # Test fixtures or factory setup
  fixtures :users, :projects
  def setup
    @user = users(:john) # using fixture defined below
    @project = projects(:web_app) # using fixture defined below
    @portfolio = Portfolio.new
  end

  # Test model validity
  test "should be valid without associations" do
    assert @portfolio.valid?
  end

  test "should be valid with user only" do
    @portfolio.user = @user
    assert @portfolio.valid?
  end

  test "should be valid with project only" do
    @portfolio.project = @project
    assert @portfolio.valid?
  end

  test "should be valid with both user and project" do
    @portfolio.user = @user
    @portfolio.project = @project
    assert @portfolio.valid?
  end

  # Test associations
  test "should belong to user" do
    assert_respond_to @portfolio, :user
    assert_respond_to @portfolio, :user=
  end

  test "should belong to project" do
    assert_respond_to @portfolio, :project
    assert_respond_to @portfolio, :project=
  end

  test "user association should be optional" do
    @portfolio.project = @project
    @portfolio.user = nil
    assert @portfolio.valid?
    assert_nil @portfolio.user
  end

  test "project association should be optional" do
    @portfolio.user = @user
    @portfolio.project = nil
    assert @portfolio.valid?
    assert_nil @portfolio.project
  end

  # Test database operations
  test "should save portfolio without associations" do
    assert @portfolio.save
    assert_not_nil @portfolio.id
  end

  test "should save portfolio with user" do
    @portfolio.user = @user
    assert @portfolio.save
    assert_equal @user, @portfolio.reload.user
  end

  test "should save portfolio with project" do
    @portfolio.project = @project
    assert @portfolio.save
    assert_equal @project, @portfolio.reload.project
  end

  test "should save portfolio with both associations" do
    @portfolio.user = @user
    @portfolio.project = @project
    assert @portfolio.save

    saved_portfolio = @portfolio.reload
    assert_equal @user, saved_portfolio.user
    assert_equal @project, saved_portfolio.project
  end

  # # Test foreign key handling
  # test "should handle invalid user_id" do
  #   @portfolio.user_id = 99999 # non-existent user
  #   # This might raise an error or be invalid depending on your DB constraints
  #   # Adjust this test based on your actual database setup
  # end

  # test "should handle invalid project_id" do
  #   @portfolio.project_id = 99999 # non-existent project
  #   # This might raise an error or be invalid depending on your DB constraints
  #   # Adjust this test based on your actual database setup
  # end

  # Test destruction behavior (if you have dependent: :destroy or similar)
  # test "should not be destroyed when user is destroyed" do
  #   @portfolio.user = @user
  #   @portfolio.save!

  #   portfolio_id = @portfolio.id
  #   @user.destroy

  #   # Portfolio should still exist but user should be nil
  #   portfolio = Portfolio.find(portfolio_id)
  #   assert_nil portfolio.user_id
  # end

  # test "should not be destroyed when project is destroyed" do
  #   @portfolio.project = @project
  #   @portfolio.save!

  #   portfolio_id = @portfolio.id
  #   binding.pry
  #   @project.destroy

  #   # Portfolio should still exist but project should be nil
  #   portfolio = Portfolio.find(portfolio_id)
  #   assert_nil portfolio.project_id
  # end

  # Test class methods (add any custom class methods you have)
  test "should respond to Portfolio class" do
    assert_kind_of Class, Portfolio
    assert_equal ApplicationRecord, Portfolio.superclass
  end

  # Add any custom method tests here if your model has additional methods
  # Example:
  # test "custom_method_name should return expected value" do
  #   # Your custom method tests
  # end
end
